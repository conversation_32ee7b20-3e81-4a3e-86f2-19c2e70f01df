---
apiVersion: v1
kind: Service
metadata:
  name: pisa-self-serve-svc
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 80
  selector:
    app: pisa-self-serve


---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pisa-self-serve
spec:
  replicas: 2
  selector:
    matchLabels:
      app: pisa-self-serve
  template:
    metadata:
      labels:
        app: pisa-self-serve
    spec:
      containers:
      - name: pisa-self-serve
        image: harbor.dev.kubix.tm.com.my/ipgen-pisa/pisa-self-serve:<version>
        # resources:
        #    limits:
        #       memory: "600Mi"
        #       cpu: "300m"
        ports:
        - containerPort: 80
        volumeMounts:
        - mountPath: /var/www/html/storage/framework/self-serve-cache
          name: nfs-vol
          subPath: self-serve-cache
      initContainers:
      - name: permissions-init
        image: harbor.dev.kubix.tm.com.my/ipgen-pisa/busybox:<version>
        command: ["sh", "-c", "chmod -R 775 /var/www/html/storage/framework/self-serve-cache && chown -R www-data:www-data /var/www/html/storage/framework/self-serve-cache"]
        volumeMounts:
        - mountPath: /var/www/html/storage/framework/self-serve-cache
          name: nfs-vol
          subPath: self-serve-cache
      volumes:
      - name: nfs-vol
        nfs:
         server: ************
         path: /BF7_NFS_DEV_SC      
      # securityContext:
      #   runAsUser: 33  # UID of www-data in many Linux distributions
      #   runAsGroup: 33  # GID of www-data in many Linux distributions

