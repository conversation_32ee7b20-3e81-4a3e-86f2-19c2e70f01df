apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pisa-self-serve-nginx
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '600'
    # nginx.ingress.kubernetes.io/rewrite-target: /self-serve/$2
    # nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    
spec:
  tls:
  - hosts:
    - pisa-dev.tm.com.my
  rules:
  - host: pisa-dev.tm.com.my
    http:
      paths:
      # - path: /self-serve
      # - path: /self-serve(/|$)(.*)
      - path: /
        pathType: Prefix 
        backend:
          service:
            name: pisa-self-serve-svc
            port: 
              number: 8000
