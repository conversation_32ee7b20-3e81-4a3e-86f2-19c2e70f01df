---
apiVersion: v1
kind: Service
metadata:
  name: pisa-self-serve-svc
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 80
  selector:
    app: pisa-self-serve

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pisa-self-serve
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pisa-self-serve
  template:
    metadata:
      labels:
        app: pisa-self-serve
    spec:
      containers:
      - name: pisa-self-serve
        image: harbor.prod.kubix.tm.com.my/ipgenpisa/pisa-self-serve:<version>
        ports:
        - containerPort: 80
        volumeMounts:
        - mountPath: /var/www/html/storage/framework/self-serve-cache
          name: nfs-vol
          subPath: self-serve-cache
        - mountPath: /var/www/html/storage/public
          name: nfs-vol
          subPath: self-serve-public
      initContainers:
      - name: permissions-init
        image: harbor.prod.kubix.tm.com.my/ipgenpisa/busybox:<version>
        command: ["sh", "-c", "chmod -R 775 /var/www/html/storage/framework/self-serve-cache /var/www/html/storage/public && chown -R www-data:www-data /var/www/html/storage/framework/self-serve-cache /var/www/html/storage/public"]
        volumeMounts:
        - mountPath: /var/www/html/storage/framework/self-serve-cache
          name: nfs-vol
          subPath: self-serve-cache
        - mountPath: /var/www/html/storage/public
          name: nfs-vol
          subPath: self-serve-public
      volumes:
      - name: nfs-vol
        nfs:
          server: ************
          path: /BF7_NFS_PRD_IPGEN_PISA
