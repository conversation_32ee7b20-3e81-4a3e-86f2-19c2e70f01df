apiVersion: batch/v1
kind: CronJob
metadata:
  name: cronjob-pisa-self-serve
spec:
  schedule: "* * * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cron-pisa-self-serve
            image: harbor.dev.kubix.tm.com.my/ipgen-pisa/pisa-self-serve:<version>
            imagePullPolicy: IfNotPresent
            command:
            - /bin/sh
            - -c
            - cd /var/www/html && /usr/local/bin/php artisan schedule:run >> /var/log/cron.log 2>&1
          restartPolicy: OnFailure
