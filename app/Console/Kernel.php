<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('fetch:ogg-data')->everyThirtyMinutes();
        $schedule->command('fetch:unis-data')->everyThirtyMinutes();
        // $schedule->command('fetch:bgp-status-uptime-data')->monthlyOn(1, '00:01'); // Runs at midnight on the 1st of every month
        // $schedule->command('fetch:bgp-status-uptime-v2')->monthlyOn(1, '00:01'); // Runs at midnight on the 1st of every month
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
