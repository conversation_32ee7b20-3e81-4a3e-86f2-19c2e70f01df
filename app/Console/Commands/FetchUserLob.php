<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchUserLob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:user-lob';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Query User Lob and auto populate in User table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Fetch all users
        $users = User::all();

        foreach ($users as $user) {
            try {
                // Assuming the API returns a 'lob' value
                $lob = $this->getEraStaffExtraInfo($user->staff_id)[0]['LOB_Desc'];

                // Update the user model with the 'lob' value
                $user->lob = $lob;

                // Save the user model with the updated 'lob' value
                $user->save();

                $this->info("User Id. ({$user->staff_id}) updated with LOB: {$lob}");
            } catch (\Exception $e) {
                // Log the error and continue
                Log::error("Failed to update User ID {$user->id}: " . $e->getMessage());
                $this->error("Failed to update User ID {$user->id}: " . $e->getMessage());
            }
        }

        return 0;
    }

    // Get ERA API access token
    public function getEraAccessToken() {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic '.env('ERA_BASIC_TOKEN'),
            ])->asForm()->post(env('ERA_API_ENDPOINT2').'token', [
                'grant_type' => 'client_credentials',
            ]);

            if ($response->successful()) {
                return $response->json()['access_token'];
            } else {
                throw new \Exception(__('ERA API: Unexpected HTTP status ('.$response->status().', '.$response->reason().')'));
            }
        } catch (\Exception $e) {
            // Log the error and rethrow it
            Log::error("Failed to get ERA access token: " . $e->getMessage());
            throw $e;
        }
    }

    // Get ERA API extra info
    public function getEraStaffExtraInfo($staffId) {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->getEraAccessToken(),
            ])->get(env('ERA_API_ENDPOINT1').'/profile/extra/info/'.$staffId);

            if ($response->successful()) {
                return $response->json();
            } else {
                throw new \Exception(__('ERA API: Failed to retrieve extra info for staff ID '.$staffId));
            }
        } catch (\Exception $e) {
            // Log the error and rethrow it
            Log::error("Failed to get ERA extra info for staff ID {$staffId}: " . $e->getMessage());
            throw $e;
        }
    }
}
