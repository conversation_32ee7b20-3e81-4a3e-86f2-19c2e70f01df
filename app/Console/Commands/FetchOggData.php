<?php

namespace App\Console\Commands;

use App\Models\OggData;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FetchOggData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:ogg-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data (Order Status==Processing and Activity Status==In Progress) from OGG server via API and dump to local database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        // Fetch data from the API for ORDER STATUS == 'Processing' and ORDER STATUS == 'PONR'
        [$response, $response_ponr] = Http::pool(fn ($pool) => [
            $pool->withHeaders(['accept' => 'application/json'])
                ->get(env('PISA_WORKFLOW_API_ENDPOINT')."/database/v1/ogg/processing_order_inprogress_activity"),
            $pool->withHeaders(['accept' => 'application/json'])
                ->get(env('PISA_WORKFLOW_API_ENDPOINT')."/database/v1/ogg/ponr_order_inprogress_activity"),
        ]);

        // Check if both the API requests were successful
        if ($response->successful() && $response_ponr->successful()) {

            // Empty the Ogg Data table
            OggData::truncate();

            // Dump data processing to database
            foreach ($response->json()['output'] as $item) {
                OggData::updateOrCreate(
                    [
                        'orderNo' => $item['OrderNumber'] ?? 'not available',
                        'orderType' => $item['OrderType'] ?? 'not available',
                        'product' => $item['ProductName'] ?? 'not available',
                        'orderStatus' => $item['OrderStatus'] ?? 'not available',
                        'activityName' => $item['ActivityName'] ?? 'not available',
                        'activityStatus' => $item['ActivityStatus'] ?? 'not available',
                        'serviceId' => $item['ServiceId'] ?? 'not available',
                        'account' => $item['Account'] ?? 'not available',
                        'activityCreated' => !empty($item['ActivityCreated']) ? Carbon::parse($item['ActivityCreated']) : Carbon::now(),
                        'aging' => $item['Aging'] ?? 0,
                        'owner' => $item['Owner'] ?? 'not available',
                        'ownerUnit' => $item['OwnersPosition'] ?? 'not available',
                    ]
                );
            }
            // dump data ponr to database
            foreach ($response_ponr->json()['output'] as $item) {
                OggData::updateOrCreate(
                    [
                        'orderNo' => $item['OrderNumber'] ?? 'not available',
                        'orderType' => $item['OrderType'] ?? 'not available',
                        'product' => $item['ProductName'] ?? 'not available',
                        'orderStatus' => $item['OrderStatus'] ?? 'not available',
                        'activityName' => $item['ActivityName'] ?? 'not available',
                        'activityStatus' => $item['ActivityStatus'] ?? 'not available',
                        'serviceId' => $item['ServiceId'] ?? 'not available',
                        'account' => $item['Account'] ?? 'not available',
                        'activityCreated' => !empty($item['ActivityCreated']) ? Carbon::parse($item['ActivityCreated']) : Carbon::now(),
                        'aging' => $item['Aging'] ?? 0,
                        'owner' => $item['Owner'] ?? 'not available',
                        'ownerUnit' => $item['OwnersPosition'] ?? 'not available',
                    ]
                );
            }

            // Display success message
            $success_msg = 'Data (Order Status: Processing & PONR) fetched and stored successfully at '. date('Y-m-d H:i:s');
            $this->info($success_msg);
            Log::info($success_msg);
        } 

        else {
            // Display error message
            $fail_msg = 'Failed to fetch data (Order Status: Processing) from the API: ' .$response->body() . ' & ' .$response_ponr->body();
            $this->error($fail_msg);
            Log::info($fail_msg);
        }
    }
}
