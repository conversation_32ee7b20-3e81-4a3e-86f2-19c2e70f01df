<?php

namespace App\Console\Commands;

use App\Models\BgpUptimeStatus;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;


class FetchBGPStatusUptimeData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:bgp-status-uptime-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data (BGP Status Uptime) from Network Element server via API and dump to local database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get the current month
        $currentMonth = now()->format('Y-m');

        // Check if there are records created in the same month
        $records = BgpUptimeStatus::where('created_at', 'like', "$currentMonth%")->get();

        if ($records->isNotEmpty()) {
            // Delete records created in the same month
            $deletedCount = BgpUptimeStatus::where('created_at', 'like', "$currentMonth%")->delete();
            Log::info("Deleted {$deletedCount} records from {$currentMonth}");
            $this->info("Deleted {$deletedCount} records from {$currentMonth}");
        } else {
            Log::info("No records found for {$currentMonth}");
            $this->info("No records found for {$currentMonth}");
        }

        // Fetch data from new API endpoint with SSL verification disabled
        $response = Http::withoutVerifying()
            ->post(env('PISA_WORKFLOW_API_ENDPOINT')."/v1/self_serve/bgp_uptime_status");
        
        if ($response->successful()) {
            $data = $response->json();
            Log::info('BGP Status Uptime data fetched successfully.', $data);
            $this->info('BGP Status Uptime data fetched successfully.');
        } else {
            Log::error('Failed to fetch BGP Status Uptime data.');
            $this->error('Failed to fetch BGP Status Uptime data.');
        }

        return 0;
    }
}
