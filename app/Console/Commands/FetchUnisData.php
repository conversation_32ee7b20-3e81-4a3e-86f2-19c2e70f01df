<?php

namespace App\Console\Commands;

use App\Models\PremiumCustomer;
use App\Models\UnisData;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FetchUnisData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:unis-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Layer 2 and Layer 3 circuits based on all premium customer abbreviations available in table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // get list of customers abbreviations
        $abbreviations = PremiumCustomer::distinct()
                                         ->whereNotNull('abbreviation')
                                         ->where('abbreviation', '!=', '')
                                         ->pluck('abbreviation')
                                         ->toArray();

        // Testing output in console
        // $abbreviationsString = implode(', ', $abbreviations);
        // $this->info($abbreviationsString);

        $response = Http::withHeaders([
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post(env('PISA_WORKFLOW_API_ENDPOINT').'/data/v1/get_l2_l3_circuit', [
            "cust_abbr" => $abbreviations
        ]);

        // Check if both the API requests were successful
        if ($response->successful()) {

            // Empty the UNIS Data table
            UnisData::truncate();

            // Dump data processing to database
            foreach ($response->json()['output'] as $item) {
                UnisData::updateOrCreate(
                    [
                        'CustAbbr' => $item['CustAbbr'] ?? null,
                        'ServiceId' => $item['ServiceId'] ?? null,
                        'Product' => $item['Product'] ?? null,
                        'Bandwidth' => $item['Bandwidth'] ?? null,
                        'RedundancyId' => $item['RedundancyId'] ?? null,
                        'ParentId' => $item['ParentId'] ?? null,
                        'PathStatus' => $item['PathStatus'] ?? null,
                        'ServiceType' => $item['ServiceType'] ?? null,
                        'Npe' => $item['Npe'] ?? null,
                        'NpeInterface' => $item['NpeInterface'] ?? null,
                        'Pe' => $item['Pe'] ?? null,
                        'PeIp' => $item['PeIp'] ?? null,
                        'PeInterface' => $item['PeInterface'] ?? null,

                        // Additional columns from JSON
                        'L2CustAbbr' => $item['L2CustAbbr'] ?? null,
                        'ParentServiceId' => $item['ParentServiceId'] ?? null,
                        'L2ServiceId' => $item['L2ServiceId'] ?? null,
                        'L2Product' => $item['L2Product'] ?? null,
                        'L2Bandwidth' => $item['L2Bandwidth'] ?? null,
                        'L2RedundancyId' => $item['L2RedundancyId'] ?? null,
                        'L2ParentSvc' => $item['L2ParentSvc'] ?? null,
                        'L2PathStatus' => $item['L2PathStatus'] ?? null,
                        'L2ServiceType' => $item['L2ServiceType'] ?? null,
                        'UpeNidDlink' => $item['UpeNidDlink'] ?? null,
                        'UpeNidDlinkInterface' => $item['UpeNidDlinkInterface'] ?? null,
                        'UpeNidUlinkPri' => $item['UpeNidUlinkPri'] ?? null,
                        'UpeNidUlinkPriInterface' => $item['UpeNidUlinkPriInterface'] ?? null,
                        'UpeNidUlinkSec' => $item['UpeNidUlinkSec'] ?? null,
                        'UpeNidUlinkSecInterface' => $item['UpeNidUlinkSecInterface'] ?? null,
                        'EpePri' => $item['EpePri'] ?? null,
                        'EpePriInterface' => $item['EpePriInterface'] ?? null,
                        'EpeSec' => $item['EpeSec'] ?? null,
                        'EpeSecInterface' => $item['EpeSecInterface'] ?? null,

                        'CeIp' => $item['CeIp'] ?? null,
                        'PeDeviceId' => $item['PeDeviceId'] ?? null,
                        'Cidr' => $item['PeIpCidr'] ?? null,
                    ]
                );
            }

            // Display success message
            $success_msg = 'Data (Layer 2 and Layer 3 circuits) fetched and stored successfully at '. date('Y-m-d H:i:s');
            $this->info($success_msg);
            Log::info($success_msg);

        }

        else {
            // Display error message
            $fail_msg = 'Failed to fetch data (Layer 2 and Layer 3 circuits) from the API: ' .$response->body();
            $this->error($fail_msg);
            Log::info($fail_msg);
        }

    }
}
