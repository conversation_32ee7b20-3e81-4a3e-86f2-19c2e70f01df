<?php

namespace App\Console\Commands;

use App\Models\BgpUptimeStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class FetchBGPStatusUptimev2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetch:bgp-status-uptime-v2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch data (BGP Status Uptime) from Network Element server via API and dump to local database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get the current month
        $currentMonth = now()->format('Y-m');

        printf("Current Month: %s\n", $currentMonth);

        // Check if there are records created in the same month
        $records = BgpUptimeStatus::whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])->get();

        if ($records->isNotEmpty()) {
            // Delete records created in the same month
            $deletedCount = BgpUptimeStatus::whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])->delete();
            Log::info("Deleted {$deletedCount} records from {$currentMonth}");
            $this->info("Deleted {$deletedCount} records from {$currentMonth}");
        } else {
            Log::info("No records found for {$currentMonth}");
            $this->info("No records found for {$currentMonth}");
        }

        // Step 1: Get distinct PE nodes and CeIp, excluding IBSE
        $peNodes = DB::table('unis_data')
            ->distinct()
            ->select('Pe', 'PeDeviceId', 'CeIp', 'ServiceId', 'CustAbbr', 'ServiceType', 'Product', 'RedundancyId')
            ->whereNotNull('Pe')
            ->whereNotNull('PeDeviceId')
            ->whereNotNull('CeIp')
            ->where('Pe', 'not like', 'IBSE%')
            ->orderBy('Pe')
            ->get();

        // Step 2: Insert into bgp_uptime_status without BGPStatus and BGPUptime
        foreach ($peNodes as $node) {
            DB::table('bgp_uptime_status')->insert([
                'CustAbbr' => $node->CustAbbr ?? null,
                'ServiceType' => $node->ServiceType ?? null,
                'Product' => $node->Product ?? null,
                'LegId' => $node->RedundancyId ?? null,
                'ServiceId' => $node->ServiceId ?? null,
                'PeNode' => $node->Pe ?? null,
                'PeDeviceIp' => $node->PeDeviceId ?? null,
                'CeWanIp' => $node->CeIp ?? null,
                'BGPStatus' => '',
                'BGPUptime' => '',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }

        // Step 3: Group nodes by Pe and PeDeviceId and send API requests
        $grouped = $peNodes->groupBy(function($item) {
            return $item->Pe . '|' . $item->PeDeviceId;
        });

        foreach ($grouped as $key => $group) {
            $first = $group->first();
            $payload = [
                'pe_router' => $first->Pe,
                'ne_router' => $first->PeDeviceId,
                'data' => $group->map(function($node) {
                    return [
                        'service_id' => $node->ServiceId,
                        'ce_ip' => $node->CeIp
                    ];
                })->values()->toArray()
            ];
            $this->info('Sending payload for PE: ' . $first->Pe);
            $this->line(json_encode($payload));
            try {
                // $response = \Illuminate\Support\Facades\Http::post('env('PISA_WORKFLOW_API_ENDPOINT')."/v1/self_serve/bgp_uptime_status_data_v2", $payload);
                $response = Http::withoutVerifying()
                    ->post(env('PISA_WORKFLOW_API_ENDPOINT')."/v1/self_serve/bgp_uptime_status_data_v2", $payload);

                if ($response->successful()) {
                    $this->info('Payload sent successfully.');
                    $apiResults = $response->json('results') ?? [];
                    foreach ($apiResults as $apiCircuit) {
                        DB::table('bgp_uptime_status')
                            ->where('ServiceId', $apiCircuit['service_id'] ?? null)
                            ->where('CeWanIp', $apiCircuit['ce_ip'] ?? null)
                            ->update([
                                'BGPStatus' => $apiCircuit['bgp_status'] ?? null,
                                'BGPUptime' => $apiCircuit['bgp_uptime'] ?? null,
                                'updated_at' => now()
                            ]);
                        $this->info('Updated: ServiceId=' . ($apiCircuit['service_id'] ?? '') . ', CeWanIp=' . ($apiCircuit['ce_ip'] ?? '') . ', Status=' . ($apiCircuit['bgp_status'] ?? '') . ', Uptime=' . ($apiCircuit['bgp_uptime'] ?? ''));
                    }
                } else {
                    $this->error('Failed to send payload. Status: ' . $response->status() . '. Response: ' . $response->body());
                }
            } catch (\Exception $e) {
                $this->error('Exception when sending payload: ' . $e->getMessage());
            }
        }

        return 0;
    }
}
