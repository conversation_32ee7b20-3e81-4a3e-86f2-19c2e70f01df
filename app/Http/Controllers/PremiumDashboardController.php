<?php

namespace App\Http\Controllers;

use App\Models\AuditTrail;
use App\Models\OggData;
use App\Models\PremiumCustomer;
use App\Models\UnisData;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Throwable;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
// use Maatwebsite\Excel\Facades\Excel;


class PremiumDashboardController extends Controller
{
    // format datetime
    public function reformatDateTime($time) {
        // Convert the default timestamp format to the desired format
        return Carbon::createFromFormat('Y-m-d H:i:s', $time)->format('d-m-Y H:i:s');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('self-serve.premium-dashboard.index');
    }

    
    // public function getAnalytics(Request $request)
    // {
    //     $customerName = $request->route('customer');
    //     $selectedMonth = $request->get('selected_month', now()->format('n'));
    
    //     // Get service IDs from JSON for Maybank MA
    //     $serviceIds = [];
    //     if ($customerName === 'Maybank MA') {
    //         $serviceIds = $this->readJson($customerName);
    //         // echo '<pre>';
    //         // echo "Service IDs:\n";
    //         // print_r($serviceIds);
    //         // echo '</pre>';
    //     }
    
    //     // Define TOTAL subquery based on customer
    //     if ($customerName === 'Maybank MA' && !empty($serviceIds)) {
    //         $totalSubquery = "(SELECT COUNT(id) FROM bgp_uptime_status WHERE CustAbbr IN (SELECT abbreviation FROM premium_customers pc WHERE pc.name = '{$customerName}') AND ServiceId IN ('".implode("','", $serviceIds)."')) as TOTAL";
    //     } else {
    //         $totalSubquery = "(SELECT COUNT(id) FROM bgp_uptime_status WHERE CustAbbr IN (SELECT abbreviation FROM premium_customers pc WHERE pc.name = '{$customerName}')) as TOTAL";
    //     }
    
    //     // Product data query
    //     $productData = DB::table('bgp_uptime_status')
    //     ->select(
    //         DB::raw($totalSubquery),
    //         'CustAbbr',
    //         'Product',
    //         DB::raw('COUNT(*) as TotalPerProduct')
    //     )
    //     ->whereIn('CustAbbr', function($query) use ($customerName) {
    //         $query->select('abbreviation')
    //               ->from('premium_customers')
    //               ->where('name', $customerName);
    //     })
    //     ->when($customerName === 'Maybank MA' && !empty($serviceIds), function($query) use ($serviceIds) {
    //         return $query->whereIn('ServiceId', $serviceIds);
    //     })
    //     ->groupBy('CustAbbr', 'Product');
    
    //     $productData = $productData->get();
    
    //     // BGP data query
    //     $bgpData = DB::table('bgp_uptime_status')
    //     ->select(
    //         'custAbbr',
    //         'Product', 
    //         'BGPStatus',
    //         DB::raw('COUNT(*) as status_count')
    //     )
    //     ->whereIn('custAbbr', function($query) use ($customerName) {
    //         $query->select('abbreviation')
    //                 ->from('premium_customers')
    //                 ->where('name', $customerName);
    //     })
    //     ->whereMonth('updated_at', $selectedMonth)
    //     ->when($customerName === 'Maybank MA' && !empty($serviceIds), function($query) use ($serviceIds) {
    //         return $query->whereIn('ServiceId', $serviceIds);
    //     })
    //     ->groupBy('Product', 'custAbbr', 'BGPStatus');
    
    //     $bgpData = $bgpData->get();
    
    //     // Restructure for the chart format
    //     $products = $bgpData->pluck('Product')->unique()->values()->toArray();
    //     $bgpStatuses = $bgpData->pluck('BGPStatus')->unique()->values()->toArray();
    
    //     $bgpSeries = [];
    //     foreach ($bgpStatuses as $status) {
    //         $seriesData = [];
    //         foreach ($products as $product) {
    //             $count = $bgpData->where('Product', $product)
    //                             ->where('BGPStatus', $status)
    //                             ->sum('status_count');
    //             $seriesData[] = $count;
    //         }
            
    //         $bgpSeries[] = [
    //             'name' => $status,
    //             'data' => $seriesData
    //         ];
    //     }
    
    //     $totalCount = $bgpData->sum('status_count');
    //     $establishedCount = $totalCount > 0 
    //         ? round(($bgpData->where('BGPStatus', 'Establ')->sum('status_count') / $totalCount) * 100, 1) . '%' 
    //         : '0%';
    
    //     $analyticsData = [
    //         'metrics' => [
    //             ['title' => 'Total Customers 1', 'value' => $productData->first() ? $productData->first()->TOTAL : 0],
    //             ['title' => 'Total Customers 2', 'value' => $establishedCount],
    //             ['title' => 'Total Customers 3', 'value' => $bgpData->where('BGPStatus', 'Establ')->sum('status_count') .'/'. $bgpData->sum('status_count')],
    //             ['title' => 'Total Customers 4', 'value' => 12.86],
    //             ['title' => 'Total Customers 5', 'value' => 55],
    //         ],
    //         'services' => [
    //             [
    //                 'labels' => $productData->pluck('CustAbbr')->unique()->values()->toArray(),
    //                 'series' => [
    //                     [
    //                         'name' => 'DIRECT',
    //                         'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
    //                             ->map(function($custAbbr) use ($productData) {
    //                                 $value = $productData->where('CustAbbr', $custAbbr)
    //                                                  ->where('Product', 'DIRECT')
    //                                                  ->first();
    //                                 return $value ? $value->TotalPerProduct : null;
    //                             })->toArray()
    //                     ],
    //                     [
    //                         'name' => 'IPVPN',
    //                         'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
    //                             ->map(function($custAbbr) use ($productData) {
    //                                 $value = $productData->where('CustAbbr', $custAbbr)
    //                                                  ->where('Product', 'IPVPN')
    //                                                  ->first();
    //                                 return $value ? $value->TotalPerProduct : null;
    //                             })->toArray()
    //                     ],
    //                     [
    //                         'name' => 'IPTRANSIT',
    //                         'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
    //                             ->map(function($custAbbr) use ($productData) {
    //                                 $value = $productData->where('CustAbbr', $custAbbr)
    //                                                  ->where('Product', 'IPTRANSIT')
    //                                                  ->first();
    //                                 return $value ? $value->TotalPerProduct : null;
    //                             })->toArray()
    //                     ],
    //                     [
    //                         'name' => 'WHOLESALE',
    //                         'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
    //                             ->map(function($custAbbr) use ($productData) {
    //                                 $value = $productData->where('CustAbbr', $custAbbr)
    //                                                  ->where('Product', 'WHOLESALE')
    //                                                  ->first();
    //                                 return $value ? $value->TotalPerProduct : null;
    //                             })->toArray()
    //                     ]
    //                 ]
    //             ]
    //         ],
    //         'services2' => [
    //             [
    //                 'labels' => $products,
    //                 'series' => $bgpSeries
    //             ]
    //         ]
    //     ];

    //     if ($customerName === 'Maybank MA') {
    //         // echo '<pre>';
    //         // echo "Product Data:\n";
    //         // print_r($productData->toArray());
    //         // echo "\nBGP Data:\n";
    //         // print_r($bgpData->toArray());
    //         // echo "\nAnalytics Data:\n";
    //         // print_r($analyticsData);
    //         // echo '</pre>';
    //     }
    
    //     return view('self-serve.premium-dashboard.network-analytics', [
    //         'analyticsData' => $analyticsData,
    //         'customer' => ['name' => $customerName]
    //     ]);
    // }

    public function getAnalytics(Request $request)
    {
        $customerName = $request->route('customer');
        $selectedMonth = $request->get('selected_month', now()->format('n'));
        
        // Get service IDs from JSON for Maybank MA
        $serviceIds = [];
        if ($customerName === 'Maybank MA') {
            $serviceIds = $this->readJson($customerName);
        }
    
        // Define TOTAL subquery based on customer - now using $selectedMonth instead of $latestMonth
        if ($customerName === 'Maybank MA' && !empty($serviceIds)) {
            $totalSubquery = "(SELECT COUNT(DISTINCT ServiceId) FROM bgp_uptime_status 
                              WHERE CustAbbr IN (SELECT abbreviation FROM premium_customers pc WHERE pc.name = '{$customerName}') 
                              AND ServiceId IN ('".implode("','", $serviceIds)."')
                              AND MONTH(updated_at) = {$selectedMonth}) as TOTAL";
        } else {
            $totalSubquery = "(SELECT COUNT(DISTINCT ServiceId) FROM bgp_uptime_status 
                              WHERE CustAbbr IN (SELECT abbreviation FROM premium_customers pc WHERE pc.name = '{$customerName}')
                              AND MONTH(updated_at) = {$selectedMonth}) as TOTAL";
        }
    
        // Product data query - now using $selectedMonth instead of $latestMonth
        $productData = DB::table('bgp_uptime_status')
        ->select(
            DB::raw($totalSubquery),
            'CustAbbr',
            'Product',
            DB::raw('COUNT(DISTINCT ServiceId) as TotalPerProduct')
        )
        ->whereIn('CustAbbr', function($query) use ($customerName) {
            $query->select('abbreviation')
                  ->from('premium_customers')
                  ->where('name', $customerName);
        })
        ->when($customerName === 'Maybank MA' && !empty($serviceIds), function($query) use ($serviceIds) {
            return $query->whereIn('ServiceId', $serviceIds);
        })
        ->whereMonth('updated_at', $selectedMonth) // Changed from $latestMonth to $selectedMonth
        ->groupBy('CustAbbr', 'Product');
    
        $productData = $productData->get();
    
        // BGP data query - Modified to use COUNT(DISTINCT ServiceId)
        $bgpData = DB::table('bgp_uptime_status')
        ->select(
            'custAbbr',
            'Product', 
            'BGPStatus',
            DB::raw('COUNT(DISTINCT ServiceId) as status_count')
        )
        ->whereIn('custAbbr', function($query) use ($customerName) {
            $query->select('abbreviation')
                    ->from('premium_customers')
                    ->where('name', $customerName);
        })
        ->whereMonth('updated_at', $selectedMonth)
        ->when($customerName === 'Maybank MA' && !empty($serviceIds), function($query) use ($serviceIds) {
            return $query->whereIn('ServiceId', $serviceIds);
        })
        ->groupBy('Product', 'custAbbr', 'BGPStatus');
    
        $bgpData = $bgpData->get();

        // Get only established BGP sessions for uptime calculation
        $establishedSessions = DB::table('bgp_uptime_status')
        ->select('ServiceId', 'BGPUptime')
        ->whereIn('custAbbr', function($query) use ($customerName) {
            $query->select('abbreviation')
                    ->from('premium_customers')
                    ->where('name', $customerName);
        })
        ->where('BGPStatus', 'Establ')
        ->whereMonth('updated_at', $selectedMonth)
        ->when($customerName === 'Maybank MA' && !empty($serviceIds), function($query) use ($serviceIds) {
            return $query->whereIn('ServiceId', $serviceIds);
        })
        ->get();

        // Calculate average BGP uptime
        $avgBgpUptime = 'N/A';
        if ($establishedSessions->count() > 0) {
            $totalUptimeHours = 0;
            
            foreach ($establishedSessions as $session) {
                // Parse BGP uptime string (e.g., "23w3d 14:23:53")
                $uptimeString = $session->BGPUptime;
                $hours = 0;
                
                // Extract weeks, days, hours, minutes, seconds using regex
                preg_match('/(?:(\d+)w)?(?:(\d+)d)?\s*(?:(\d+):(\d+):(\d+))?/', $uptimeString, $matches);
                
                // Convert to hours
                $weeks = isset($matches[1]) ? (int)$matches[1] : 0;
                $days = isset($matches[2]) ? (int)$matches[2] : 0;
                $hrs = isset($matches[3]) ? (int)$matches[3] : 0;
                $mins = isset($matches[4]) ? (int)$matches[4] : 0;
                $secs = isset($matches[5]) ? (int)$matches[5] : 0;
                
                // Calculate total hours
                $hours = ($weeks * 7 * 24) + ($days * 24) + $hrs + ($mins / 60) + ($secs / 3600);
                
                $totalUptimeHours += $hours;
            }
            
            // Calculate average hours
            $avgHours = $totalUptimeHours / $establishedSessions->count();
            
            // Convert back to weeks, days, hours, minutes, seconds
            $totalSeconds = $avgHours * 3600;
            $weeks = floor($totalSeconds / (7 * 24 * 3600));
            $totalSeconds -= $weeks * 7 * 24 * 3600;
            
            $days = floor($totalSeconds / (24 * 3600));
            $totalSeconds -= $days * 24 * 3600;
            
            $hours = floor($totalSeconds / 3600);
            $totalSeconds -= $hours * 3600;
            
            $minutes = floor($totalSeconds / 60);
            $seconds = $totalSeconds % 60;
            
            // Format the average uptime
            $avgBgpUptime = "";
            if ($weeks > 0) $avgBgpUptime .= $weeks . "w";
            if ($days > 0) $avgBgpUptime .= $days . "d ";
            $avgBgpUptime .= sprintf("%d:%d:%d", $hours, $minutes, round($seconds));
        }

        // Restructure for the chart format
        $products = $bgpData->pluck('Product')->unique()->values()->toArray();
        $bgpStatuses = $bgpData->pluck('BGPStatus')->unique()->values()->toArray();

        $bgpSeries = [];
        foreach ($bgpStatuses as $status) {
            $seriesData = [];
            foreach ($products as $product) {
                $count = $bgpData->where('Product', $product)
                                ->where('BGPStatus', $status)
                                ->sum('status_count');
                $seriesData[] = $count;
            }
            
            $bgpSeries[] = [
                'name' => $status,
                'data' => $seriesData
            ];
        }

        $totalCount = $productData->first() ? $productData->first()->TOTAL : 0;
        $establishedCount = $totalCount > 0 
            ? round(($bgpData->where('BGPStatus', 'Establ')->sum('status_count') / $totalCount) * 100, 1) . '%' 
            : '0%';
    
        $analyticsData = [
            'metrics' => [
                ['title' => 'Total Customers 1', 'value' => $productData->first() ? $productData->first()->TOTAL : 0],
                ['title' => 'Total Customers 2', 'value' => $establishedCount],
                ['title' => 'Total Customers 3', 'value' => $bgpData->where('BGPStatus', 'Establ')->sum('status_count') .'/'. ($productData->first() ? $productData->first()->TOTAL : 0)],
                ['title' => 'Avg BGP Uptime', 'value' => $avgBgpUptime],
                ['title' => 'Total Customers 5', 'value' => 55],
            ],
            'services' => [
                [
                    'labels' => $productData->pluck('CustAbbr')->unique()->values()->toArray(),
                    'series' => [
                        [
                            'name' => 'DIRECT',
                            'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
                                ->map(function($custAbbr) use ($productData) {
                                    $value = $productData->where('CustAbbr', $custAbbr)
                                                     ->where('Product', 'DIRECT')
                                                     ->first();
                                    return $value ? $value->TotalPerProduct : null;
                                })->toArray()
                        ],
                        [
                            'name' => 'IPVPN',
                            'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
                                ->map(function($custAbbr) use ($productData) {
                                    $value = $productData->where('CustAbbr', $custAbbr)
                                                     ->where('Product', 'IPVPN')
                                                     ->first();
                                    return $value ? $value->TotalPerProduct : null;
                                })->toArray()
                        ],
                        [
                            'name' => 'IPTRANSIT',
                            'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
                                ->map(function($custAbbr) use ($productData) {
                                    $value = $productData->where('CustAbbr', $custAbbr)
                                                     ->where('Product', 'IPTRANSIT')
                                                     ->first();
                                    return $value ? $value->TotalPerProduct : null;
                                })->toArray()
                        ],
                        [
                            'name' => 'WHOLESALE',
                            'data' => collect($productData->pluck('CustAbbr')->unique()->values()->toArray())
                                ->map(function($custAbbr) use ($productData) {
                                    $value = $productData->where('CustAbbr', $custAbbr)
                                                     ->where('Product', 'WHOLESALE')
                                                     ->first();
                                    return $value ? $value->TotalPerProduct : null;
                                })->toArray()
                        ]
                    ]
                ]
            ],
            'services2' => [
                [
                    'labels' => $products,
                    'series' => $bgpSeries
                ]
            ]
        ];

        if ($customerName === 'Maybank MA') {
            // echo '<pre>';
            // echo "Product Data:\n";
            // print_r($productData->toArray());
            // echo "\nBGP Data:\n";
            // print_r($bgpData->toArray());
            // echo "\nAnalytics Data:\n";
            // print_r($analyticsData);
            // echo '</pre>';
        }
    
        return view('self-serve.premium-dashboard.network-analytics', [
            'analyticsData' => $analyticsData,
            'customer' => ['name' => $customerName]
        ]);
    }


    public function downloadProductAnalytics(Request $request, $customer, $month)
    {
        // Get service IDs for Maybank MA
        $serviceIds = [];
        if ($customer === 'Maybank MA') {
            $serviceIds = $this->readJson($customer);
        }

        $query = DB::table('premium_customers')
        ->crossJoin(DB::raw("(SELECT DISTINCT Product FROM bgp_uptime_status) as products"))
        ->leftJoin('bgp_uptime_status', function($join) {
            $join->on('premium_customers.abbreviation', '=', 'bgp_uptime_status.CustAbbr')
                ->on('products.Product', '=', 'bgp_uptime_status.Product');
        })
        ->select(
            'premium_customers.abbreviation as CustAbbr',
            'products.Product',
            DB::raw('COUNT(DISTINCT bgp_uptime_status.ServiceId) as TotalPerProduct')
        )
        ->where('premium_customers.name', $customer);

        // Apply service ID filter for Maybank MA
        if ($customer === 'Maybank MA' && !empty($serviceIds)) {
            $query->whereIn('bgp_uptime_status.ServiceId', $serviceIds);
        }
        
        // Apply month filter if not requesting all data
        if ($month !== 'all') {
            $query->whereMonth('bgp_uptime_status.updated_at', $month);
        }

        // $productData = $query->groupBy('CustAbbr', 'Product')->get();
        // $productData = $query->groupBy('premium_customers.abbreviation', 'products.Product')->get();
        $productData = $query->groupBy('premium_customers.abbreviation', 'products.Product')->get();
    
        $filename = $month === 'all' 
            ? 'Complete_Product_Analytics.csv'
            : date('F_Y', mktime(0, 0, 0, $month, 1)) . '_product_analytics.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
    
        $callback = function() use ($productData) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Customer', 'Product', 'Total']);
            foreach ($productData as $row) {
                fputcsv($file, [$row->CustAbbr, $row->Product, $row->TotalPerProduct]);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function downloadBgpAnalytics(Request $request, $customer, $month)
    {
        // Get service IDs for Maybank MA
        $serviceIds = [];
        if ($customer === 'Maybank MA') {
            $serviceIds = $this->readJson($customer);
        }

        // Define the required columns with table prefix to avoid ambiguity
        $columns = [
            'bgp_uptime_status.CustAbbr', 
            'bgp_uptime_status.ServiceType', 
            'bgp_uptime_status.Product', 
            'bgp_uptime_status.LegId', 
            'bgp_uptime_status.ServiceId', 
            'bgp_uptime_status.PeNode', 
            'bgp_uptime_status.PeDeviceIp', 
            'bgp_uptime_status.CeWanIp', 
            'bgp_uptime_status.BGPStatus', 
            'bgp_uptime_status.BGPUptime'
        ];

        // Create a subquery to get the latest record for each ServiceId
        $latestRecords = DB::table('bgp_uptime_status')
            ->select('ServiceId', DB::raw('MAX(id) as max_id'))
            ->whereIn('CustAbbr', function($query) use ($customer) {
                $query->select('abbreviation')
                    ->from('premium_customers')
                    ->where('name', $customer);
            })
            ->whereMonth('updated_at', $month)
            ->when($customer === 'Maybank MA' && !empty($serviceIds), function($query) use ($serviceIds) {
                return $query->whereIn('ServiceId', $serviceIds);
            })
            ->groupBy('ServiceId');

        // Fetch only the distinct ServiceId records using the subquery
        $bgpData = DB::table('bgp_uptime_status')
            ->select($columns)
            ->joinSub($latestRecords, 'latest', function($join) {
                $join->on('bgp_uptime_status.id', '=', 'latest.max_id');
            });

        $bgpData = $bgpData->get();

        // Define display column names for CSV (without table prefixes)
        $displayColumns = [
            'CustAbbr', 
            'ServiceType', 
            'Product', 
            'LegId', 
            'ServiceId', 
            'PeNode', 
            'PeDeviceIp', 
            'CeWanIp', 
            'BGPStatus', 
            'BGPUptime'
        ];

        // Generate the filename based on the selected month
        $filename = Carbon::create(null, $month, 1)->format('F_Y') . '_bgp_analytics.csv';

        // Define CSV headers
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        // Stream CSV response
        $callback = function() use ($bgpData, $displayColumns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $displayColumns);
            foreach ($bgpData as $row) {
                fputcsv($file, array_values((array) $row));
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    // Read json file 
    public function readJson($customerName = null) {
        // Path to the JSON file (modified to point directly to public/json)
        $path = public_path('json/customer_services.json');
        
        // Check if file exists
        if (!file_exists($path)) {
            return ['error' => 'File not found'];
        }
        
        // Read and decode JSON file
        $jsonContent = file_get_contents($path);
        $data = json_decode($jsonContent, true);
        
        // Check for JSON decoding errors
        if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => 'Invalid JSON format'];
        }
        
        // If no customer name provided, return error
        if (!$customerName) {
            return ['error' => 'Customer name is required'];
        }
        
        // Filter service IDs for the given customer name
        $serviceIds = collect($data)
            ->where('customer_name', $customerName)
            ->pluck('service_id')
            ->unique()
            ->values()
            ->all();
        
        // Return just the array of service IDs
        return $serviceIds;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // if id send in url not equal to 'All' means specific customer
        if ($id != 'All') {

            if ($id == 'Maybank MA') { 
                // Get list of account name from premium customer
                $account = PremiumCustomer::where('name', $id)->get();

                // Collect unique account name
                $listOfAcct = [];
                foreach ($account as $element) {
                    if (!in_array($element->account, $listOfAcct)) $listOfAcct[] = $element->account;
                }

                // Read json file and return list of service id
                $list_customer_services = $this->readJson($id);

                // Count distinct orders matching both conditions
                $countEntryContainDisctintAcctName = OggData::whereIn('account', $listOfAcct)
                    ->whereIn('serviceId', $list_customer_services)
                    ->distinct('orderNo')
                    ->count('orderNo');

                // Get customer abbreviation
                $abbreviation = PremiumCustomer::where('name', $id)->distinct()->pluck('abbreviation')->toArray();

                // Count list of circuit match abbreviation
                $countEntryContainAbbreviation = UnisData::whereIn('CustAbbr', $abbreviation)
                    ->whereIn('ServiceId', $list_customer_services)
                    ->count('ServiceId');

                // Get list of circuit with customer abbreviation and match list of circuit with CE IP Address
                $listServiceIdmatchCeIp = DB::table('temp_ce_ip')
                ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
                // ->select('temp_ce_ip.*', 'unis_data.*')
                // ->select('unis_data.ServiceId as unis_service_id')
                ->whereIn('unis_data.CustAbbr', $abbreviation)
                // ->whereIn('unis_data.ServiceId', $list_customer_services)
                ->pluck('unis_data.ServiceId')
                ->toArray();

                if (count($listServiceIdmatchCeIp) > 0) {
                    if ($dataApi = $this->getCircuitLatencyLaser($listServiceIdmatchCeIp)) {
                        $totalLatency = 0;
                        foreach ($dataApi['data'] as $element) {
                            $totalLatency += $element['median_rtt_avg'];
                        }
                        $avgLatency = round(($totalLatency / count($listServiceIdmatchCeIp)) * 1000, 2).'ms'; // Convert to milliseconds and round to 2 decimal places
                    }
                    else $avgLatency = 'N/A';
                }
                else $avgLatency = 'N/A';

                // Get all list of service id in array form from UnisData table
                $listServiceId = UnisData::whereIn('CustAbbr', $abbreviation)->whereIn('ServiceId', $list_customer_services)->pluck('ServiceId')->toArray();

                // Calculate Service Utilization
                if ($dataApiKoci = $this->getCircuitUtilKoci($listServiceId)) {
                    if (isset($dataApiKoci['Success']) && $dataApiKoci['Success']) {
                        $totalUtil = 0;
                        foreach ($dataApiKoci['data'] as $element) {
                            $totalUtil += $element['avg_util_24hrs'];
                        }
                        $avgUtil = round(($totalUtil / count($listServiceId)), 2).'%'; // Convert to milliseconds and round to 2 decimal places
                    }
                    else if (isset($dataApiKoci['Fail']) && !$dataApiKoci['Fail']) $avgUtil = 'N/A';
                    else $avgUtil = 'N/A';
                }
                else $avgUtil = 'N/A';

            }
            else {
                // Get list of account name from premium customer
                $account = PremiumCustomer::where('name', $id)->get();

                // Collect unique account name
                $listOfAcct = [];
                foreach ($account as $element) {
                    if (!in_array($element->account, $listOfAcct)) $listOfAcct[] = $element->account;
                }

                // Count list of distinct activity match account name
                $countEntryContainDisctintAcctName = OggData::whereIn('account', $listOfAcct)->distinct('orderNo')->count('orderNo');

                // Get customer abbreviation
                $abbreviation = PremiumCustomer::where('name', $id)->distinct()->pluck('abbreviation')->toArray();

                // Count list of circuit match abbreviation
                $countEntryContainAbbreviation = UnisData::whereIn('CustAbbr', $abbreviation)->count('ServiceId');

                // Get list of circuit with customer abbreviation and match list of circuit with CE IP Address
                $listServiceIdmatchCeIp = DB::table('temp_ce_ip')
                ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
                // ->select('temp_ce_ip.*', 'unis_data.*')
                // ->select('unis_data.ServiceId as unis_service_id')
                ->whereIn('unis_data.CustAbbr', $abbreviation)
                ->pluck('unis_data.ServiceId')
                ->toArray();

                if (count($listServiceIdmatchCeIp) > 0) {
                    if ($dataApi = $this->getCircuitLatencyLaser($listServiceIdmatchCeIp)) {
                        $totalLatency = 0;
                        foreach ($dataApi['data'] as $element) {
                            $totalLatency += $element['median_rtt_avg'];
                        }
                        $avgLatency = round(($totalLatency / count($listServiceIdmatchCeIp)) * 1000, 2).'ms'; // Convert to milliseconds and round to 2 decimal places
                    }
                    else $avgLatency = 'N/A';
                }
                else $avgLatency = 'N/A';

                // Get all list of service id in array form from UnisData table
                $listServiceId = UnisData::whereIn('CustAbbr', $abbreviation)->pluck('ServiceId')->toArray();

                // Calculate Service Utilization
                if ($dataApiKoci = $this->getCircuitUtilKoci($listServiceId)) {
                    if (isset($dataApiKoci['Success']) && $dataApiKoci['Success']) {
                        $totalUtil = 0;
                        foreach ($dataApiKoci['data'] as $element) {
                            $totalUtil += $element['avg_util_24hrs'];
                        }
                        $avgUtil = round(($totalUtil / count($listServiceId)), 2).'%'; // Convert to milliseconds and round to 2 decimal places
                    }
                    else if (isset($dataApiKoci['Fail']) && !$dataApiKoci['Fail']) $avgUtil = 'N/A';
                    else $avgUtil = 'N/A';
                }
                else $avgUtil = 'N/A';

            }

        } 
        
        else {
            $id = 'All Customers';

            // Get all customer distinct account
            $listOfAcct = PremiumCustomer::distinct()->pluck('account')->toArray();

            // Count list of activity match account name
            $countEntryContainDisctintAcctName = OggData::whereIn('account', $listOfAcct)->distinct('orderNo')->count('orderNo');

            // Count list of circuit in UnisData
            $countEntryContainAbbreviation = UnisData::count();

            // Get list of circuit match list of circuit with CE IP Address
            $listServiceIdmatchCeIp = DB::table('temp_ce_ip')
            ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
            // ->select('temp_ce_ip.*', 'unis_data.*')
            ->pluck('unis_data.ServiceId')
            ->toArray();

            if (count($listServiceIdmatchCeIp) > 0) {
                if ($dataApi = $this->getCircuitLatencyLaser($listServiceIdmatchCeIp)) {
                    $totalLatency = 0;
                    foreach ($dataApi['data'] as $element) {
                        $totalLatency += $element['median_rtt_avg'];
                    }
                    $avgLatency = round(($totalLatency / count($listServiceIdmatchCeIp)) * 1000, 2).'ms'; // Convert to milliseconds and round to 2 decimal places
                }
                else $avgLatency = 'N/A';
            }
            else $avgLatency = 'N/A';

            // Get all list of service id in array form from UnisData table
            $listServiceId = UnisData::pluck('ServiceId')->toArray();

            // Calculate Service Utilization
            if ($dataApiKoci = $this->getCircuitUtilKoci($listServiceId)) {
                if (isset($dataApiKoci['Success']) && $dataApiKoci['Success']) {
                    $totalUtil = 0;
                    foreach ($dataApiKoci['data'] as $element) {
                        $totalUtil += $element['avg_util_24hrs'];
                    }
                    $avgUtil = round(($totalUtil / count($listServiceId)), 2).'%'; // Convert to milliseconds and round to 2 decimal places
                }
                else if (isset($dataApiKoci['Fail']) && !$dataApiKoci['Fail']) $avgUtil = 'N/A';
                else $avgUtil = 'N/A';
            }
            else $avgUtil = 'N/A';
        }

        $customer = [
            'name' => $id,
            'order_status' => $countEntryContainDisctintAcctName,
            'customer_info' => $countEntryContainAbbreviation,
            'avg_latency' => $avgLatency,
            'avg_utilization' => $avgUtil
        ];

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "View details customer (" . $id . ")",
            'user_id' => Auth::id(),
            'module' => 'Premium Dashboard',
        ]);
        
        return view('self-serve.premium-dashboard.show', compact('customer'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    // Get Customer Info
    public function getCustomerInfo($function) {

        if ($function === 'list') {
            $customers = PremiumCustomer::all();

            $customer = []; // Initialize an empty array to store customer data
            $encounteredNames = []; // Initialize an empty array to store encountered names

            foreach ($customers as $element) {
                // Check if the name has already been encountered
                if (!in_array($element->name, $encounteredNames)) {
                    // If the name is not encountered, add it to the list of encountered names
                    $encounteredNames[] = $element->name;

                    // Add customer data to the $customer array
                    $customer[] = [
                        'name' => $element->name,
                        // 'imgSrc' => str_replace("<image_name>", $element->logo, "{{ URL::asset('assets/images/customer/<image_name>') }}")
                        'imgSrc' => asset('assets/images/customer/' . $element->logo)
                    ];
                }
            }


        }

        return compact('customer');
    }

    // Get Activity List
    public function getActivityList(Request $request) {

        if ($request->customer !== 'All Customers') {

            if ($request->customer === 'Maybank MA') {
                // Get list of account name from premium customer
                $account = PremiumCustomer::where('name', $request->customer)->get();
                $listOfAcct = [];
                foreach ($account as $element) {
                    if (!in_array($element->account, $listOfAcct)) $listOfAcct[] = $element->account;
                }

                $list_customer_services = $this->readJson($request->customer);

                // Get list of activity match account name
                $listActivityContainAcctName = OggData::whereIn('account', $listOfAcct)->whereIn('serviceId', $list_customer_services)->get();
            }
            else {
                // Get list of account name from premium customer
                $account = PremiumCustomer::where('name', $request->customer)->get();
                $listOfAcct = [];
                foreach ($account as $element) {
                    if (!in_array($element->account, $listOfAcct)) $listOfAcct[] = $element->account;
                }

                // Get list of activity match account name
                $listActivityContainAcctName = OggData::whereIn('account', $listOfAcct)->get();

            }

        } else {
            // Get all customer account
            $listOfAcct = PremiumCustomer::distinct()->pluck('account')->toArray();

            // Get list of activity match account name
            $listActivityContainAcctName = OggData::whereIn('account', $listOfAcct)->get();

        }

        if (sizeof($listActivityContainAcctName)===0) $data = array();     // check if no data return

        foreach ($listActivityContainAcctName as $element) {
            $data[] = [
                'id' => $element->id,
                'order_no' => $element->orderNo,
                'order_type' => $element->orderType,
                'product' => $element->product,
                'order_status' => $element->orderStatus,
                'activity' => $element->activityName,
                'activity_status' => $element->activityStatus,
                'service_id' => $element->serviceId,
                'account' => $element->account,
                'activity_created' => $this->reformatDateTime($element->activityCreated),
                'workgroup' => $element->ownerUnit,
                'aging' => $element->aging
            ];

        }

        return array('activityData' => $data);


    }

    // Get Circuit List from UNIS DB
    public function getCircuitList(Request $request) {

        if ($request->customer !== 'All Customers') {

            // Get customer abbreviation
            $abbreviation = PremiumCustomer::where('name', $request->customer)->distinct()->pluck('abbreviation')->toArray();

            if ($request->customer === 'Maybank MA') {

                $list_customer_services = $this->readJson($request->customer);
                // Get list of circuit for the particular customer
                $ListCircuit = UnisData::whereIn('CustAbbr', $abbreviation)->whereIn('serviceId', $list_customer_services)->get();
            }
            else
            // Get list of circuit for the particular customer
            $ListCircuit = UnisData::whereIn('CustAbbr', $abbreviation)->get();
            
        }
        
        else {
            // Get all customer circuit
            $ListCircuit = UnisData::all();

        }

        if (sizeof($ListCircuit)===0) $data = array();     // check if no data return

        foreach ($ListCircuit as $element) {

            // Action - button view details            
            $action = "<td class='text-left'>
                <button name='btn-view-detail' type='button' class='btn btn-soft-primary btn-sm'><span class='btn-label'>View</span><i class='mdi mdi-spin mdi-loading visually-hidden'></i></button>
            </td>";

            $data[] = [
                'CustAbbr' => $element->CustAbbr,
                'ServiceType' => $element->ServiceType,
                'Product' => $element->Product,
                'RedundancyId' => $element->RedundancyId,
                'ServiceId' => $element->ServiceId,
                'Bandwidth' => $element->Bandwidth,
                // 'Qos' => $element->Qos,
                'Pe' => $element->Pe,
                'PeIp' => $element->PeIp,
                'PeInterface' => $element->PeInterface,
                // 'PeWanIp' => $element->PeWanIp,
                'Npe' => $element->Npe,
                'NpeInterface' => $element->NpeInterface,
                'Action' => $action
            ];

        }

        return array('circuitData' => $data);
    }

    // Get Circuit Details from UNIS API
    public function getCircuitDetails(Request $request) {
        try {

            // Call UNIS API to get circuit details
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('SELFSERVE_UNIS_API_ENDPOINT'), [
                "NIGRequest" => [
                    "Layer" => "BOTH",
                    "graniteid" => $request->serviceId,
                    "Qscenario" => "POSITIVE",
                    "serviceType" => ""
                ]
            ]);

            // Get the JSON response body as an array
            $data = $response->json();

            // Check if UNIS API returns no data
            if (strpos($data['NIGResponse']['ErrorMesage'], 'no data found') !== false) throw new \Exception(__('No matching Service Id. in UNIS'));

            // initialize array value with not available
            $svcInfo = array(
                'Service Type' => [$request->serviceType, ''], 
                'Product' => ['not available', ''], 
                'Parent Id' => ['not available', ''], 
                'Leg Id' => ['not available', ''], 
                'Service Id' => [$request->serviceId, ''],
                'Bandwidth' => ['not available', ''], 
                'QOS' => ['not available', ''], 

                'PE' => ['not available', ''], 
                'PE Device Id' => ['not available', ''], 
                'PE Vendor' => ['not available', ''], 
                'PE Interface' => ['not available', ''], 
                'PE WAN IP' => ['not available', ''], 
                'PE WAN IPv6' => ['not available', ''], 

                'NPE' => ['not available', ''], 
                'NPE Vendor' => ['not available', ''], 
                'NPE Device Id' => ['not available', ''], 
                'NPE Interface' => ['not available', ''], 

                'EPE 1' => ['not available', ''], 
                'EPE Device Id 1' => ['not available', ''], 
                'EPE Vendor 1' => ['not available', ''], 
                'EPE Interface 1' => ['not available', ''], 
                'EPE 2' => ['not available', ''], 
                'EPE Device Id 2' => ['not available', ''], 
                'EPE Vendor 2' => ['not available', ''], 
                'EPE Interface 2' => ['not available', ''], 

                'UPE Type' => ['not available', ''], 
                'UPE' => ['not available', ''], 
                'UPE Model' => ['not available', ''], 
                'UPE Vendor' => ['not available', ''], 
                'UPE Device Id' => ['not available', ''], 
                'UPE Interface UL 1' => ['not available', ''], 
                'UPE Interface UL 2' => ['not available', ''], 
                'UPE Interface DL' => ['not available', ''], 

                // 'vrf' => ['not available', ''], 
                // 'vpn' => ['not available', ''], 
                // 'ce_ip' => ['not available', ''], 
                // 'ce_ipv6' => ['not available', ''], 
                // 'ce' => ['not available', ''], 
                // 'ce_vendor' => ['not available', ''], 
                // 'protocol' => ['not available', ''], 
                // 'npe_device_ip_downlink' => ['not available', ''], 
                // 'sla_slg' => ['not available', ''], 
            );

            // initialize wc info array to determine metro-e type
            $LanIP = array();

            // if layer 3 info exists
            if (isset($data['NIGResponse']['layer3'])) {

                // massage L3 info
                foreach ($data['NIGResponse']['layer3'] as $info) {
                    // General Info
                    if ($info['name'] === 'Category' && $info['field'] === 'Network') $svcInfo['Product'][0] = $info['value'];
                    else if ($info['name'] === 'Bandwidth' && $info['group'] === $request->serviceId) $svcInfo['Bandwidth'][0] = $info['value'];
                    else if ($info['name'] === 'QoS Package' && $info['group'] === $request->serviceId) $svcInfo['QOS'][0] = $info['value'];
                    else if ($info['name'] === 'Name' && $info['field'] === 'Redundancy Group') $svcInfo['Leg Id'][0] = $info['value'];
                    else if ($info['name'] === 'Parent Services' && $info['field'] === 'Parent Services' && $info['group'] === $request->serviceId && ctype_alnum($info['value'])) $svcInfo['Parent Id'][0] = $info['value'];

                    // PE Info
                    else if ($info['name'] === 'Device Name' && $info['type'] === 'PE' && $info['group'] === $request->serviceId) $svcInfo['PE'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['type'] === 'PE' && $info['group'] === $request->serviceId) $svcInfo['PE Device Id'][0] = $info['value'];
                    else if ($info['name'] === 'Vendor' && $info['type'] === 'PE' && $info['group'] === $request->serviceId) $svcInfo['PE Vendor'][0] = $info['value'];
                    else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'PE' && $info['group'] === $request->serviceId) $svcInfo['PE WAN IP'][0] = $info['value'];
                    else if ($info['name'] === 'Interface Name' && $info['type'] === 'PE' && $info['group'] === $request->serviceId) $svcInfo['PE Interface'][0] = $info['value'];
                    else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'PE' && $info['group'] === $request->serviceId) $svcInfo['PE WAN IPv6'][0] = $info['value'];
                    else if ($info['name'] === 'CIDR' && $info['group'] === $request->serviceId) $cidr = $info['value'];
                    else if ($info['name'] === 'CIDR IPv6' && $info['group'] === $request->serviceId) $cidr_ipv6 = $info['value'];

                    // else if ($info['name'] === 'Name' && $info['field'] === 'MPLS Network' && $info['group'] === $request->serviceId) $svcInfo['vpn'] = $info['value'];
                    // else if ($info['name'] === 'VRF Name' && $info['field'] === 'MPLSEndPoint' && $info['group'] === $request->serviceId) $svcInfo['vrf'] = $info['value'];
                    // else if ($info['name'] === 'Routing Protocol' && $info['group'] === $request->serviceId) $svcInfo['protocol'] = $info['value'];

                    // CE
                    // else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'CE' && $info['group'] === $request->serviceId) $svcInfo['ce_ip'] = $info['value'];
                    // else if ($info['name'] === 'Interface Name' && $info['type'] === 'CE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->serviceId) $svcInfo['ce_interface'] = $info['value'];
                    // else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'CE' && $info['group'] === $request->serviceId) $svcInfo['ce_ipv6'] = $info['value'];
                    // else if ($info['name'] === 'Device Name' && $info['type'] === 'CE' && $info['field'] === 'Container' && $info['group'] === $request->serviceId) $svcInfo['ce'] = $info['value'];
                    // else if ($info['name'] === 'Device Vendor' && $info['type'] === 'CE' && $info['field'] === 'Container' && $info['group'] === $request->serviceId) $svcInfo['ce_vendor'] = $info['value'];
                    else if ($info['name'] === 'Network' && $info['field'] === 'Network Advertisement' && $info['type'] === 'CE' && $info['group'] === $request->serviceId) $LanIP[] = $info['value'];
                }

                // Assign LAN IP to svcInfo array
                $svcInfo['LAN IP'] = [$LanIP, ''];

                // Add CIDR to IPv4 and IPv6
                if ($svcInfo['PE WAN IP'][0] !== 'not available') $svcInfo['PE WAN IP'][0] .= '/' . $cidr;
                if ($svcInfo['PE WAN IPv6'][0] !== 'not available') $svcInfo['PE WAN IPv6'][0] .= '/' . $cidr_ipv6;
                // if ($svcInfo['ce_ip'] !== 'not available') $svcInfo['ce_ip'] .= '/' . $cidr;
                // if ($svcInfo['ce_ipv6'] !== 'not available') $svcInfo['ce_ipv6'] .= '/' . $cidr_ipv6;

            }

            // if layer 2 info exists
            if (isset($data['NIGResponse']['layer2'])) {

                // find L2 Service Id
                foreach ($data['NIGResponse']['layer2'] as $info) {
                    // General Info
                    if ($info['name'] === 'Name' && $info['field'] === 'L2 Service' && $info['group'] === $request->serviceId) $L2_id = $info['value'];
                    // else if ($info['name'] === 'TECHNICAL SLA/SLG' && $info['field'] === 'L2 Service' && $info['group'] === $request->serviceId) $svcInfo['sla_slg'] = $info['value'];
                    else if ($info['name'] === 'UPE Type Physical Group' && $info['field'] === 'UPE' && $info['group'] === $request->serviceId && strpos($info['value'], 'TYPE') !== false) $svcInfo['UPE Type'][0] = $info['value'];
                }

                // massage L2 info
                foreach ($data['NIGResponse']['layer2'] as $info) {
                    // PE
                    if ($info['name'] === 'Device Name' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $L2_id) $svcInfo['PE'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $L2_id) $svcInfo['PE Device Id'][0] = $info['value'];
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $L2_id) $svcInfo['PE Interface'][0] = $info['value'];

                    // NPE
                    else if ($info['name'] === 'Device Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $L2_id)  $svcInfo['NPE'][0] = $info['value'];
                    else if ($info['name'] === 'Vendor' && $info['field'] === 'NPE UPLINK' && $info['group'] === $L2_id)  $svcInfo['NPE Vendor'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'NPE UPLINK' && $info['group'] === $L2_id) $svcInfo['NPE Device Id'][0] = $info['value'];
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $L2_id) $svcInfo['NPE Interface'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $L2_id) $npe_device_ip_downlink = $info['value'];

                    // for EPE
                    // EPE 1
                    else if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $L2_id) $svcInfo['EPE 1'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $L2_id) $svcInfo['EPE Device Id 1'][0] = $info['value'];
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $L2_id) $svcInfo['EPE Interface 1'][0] = $info['value'];
                    else if ($info['name'] === 'Vendor' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $L2_id) $svcInfo['EPE Vendor 1'][0] = $info['value'];
                    // EPE 2
                    else if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $L2_id) $svcInfo['EPE 2'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $L2_id) $svcInfo['EPE Device Id 2'][0] = $info['value'];
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $L2_id) $svcInfo['EPE Interface 2'][0] = $info['value'];
                    else if ($info['name'] === 'Vendor' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $L2_id) $svcInfo['EPE Vendor 2'][0] = $info['value'];                    

                    // for UPE/NID (1)
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 1' && $info['group'] === $request->serviceId) {
                        if ($svcInfo['UPE Interface UL 1'][0] == 'not available') {
                            $svcInfo['UPE Interface UL 1'][0] = $info['value'];
                            continue;
                        }
                    }
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE DOWNLINK' && $info['group'] === $request->serviceId) $svcInfo['UPE Interface DL'][0] = $info['value'];
                    else if ($info['name'] === 'Device Name' && $info['field'] === 'UPE' && $info['group'] === $request->serviceId) $svcInfo['UPE'][0] = $info['value'];
                    else if ($info['name'] === 'Model' && $info['field'] === 'UPE' && $info['group'] === $request->serviceId) $svcInfo['UPE Model'][0] = $info['value'];
                    else if ($info['name'] === 'Vendor' && $info['field'] === 'UPE' && $info['group'] === $request->serviceId) $svcInfo['UPE Vendor'][0] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'UPE' && $info['group'] === $request->serviceId) $svcInfo['UPE Device Id'][0] = $info['value'];
                    // UPE primary
                    if ($svcInfo['UPE Type'][0] === 'TYPE 2' || $svcInfo['UPE Type'][0] === 'TYPE 4') {
                    if ($svcInfo['UPE Interface UL 1'][0] != 'not available' && $svcInfo['UPE Interface UL 2'][0] == 'not available') if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 2' && $info['group'] === $request->serviceId) $svcInfo['UPE Interface UL 2'][0] = $info['value'];
                    }
                    
                }

                // for AGG
                if (stripos($svcInfo['NPE'][0], 'agg') !== false) {
                    // re-assign PE with AGG and NPE info
                    $svcInfo['PE'][0] = $svcInfo['npe'];
                    $svcInfo['NPE'][0] = 'not available';
                    $svcInfo['PE Vendor'][0] = $svcInfo['npe_vendor'];
                    $svcInfo['NPE Vendor'][0] = 'not available';
                    $svcInfo['PE Device Id'][0] = $npe_device_ip_downlink;
                    $svcInfo['PE Interface'][0] = $svcInfo['npe_interface'];
                    $svcInfo['NPE Interface'][0] = 'not available';
                    $svcInfo['NPE Device Id'][0] = 'not available';
                    // $L2_id = 'not available';
                }
            }

            // sort array alphabetically
            // ksort($svcInfo);

            // if ($request->operation==='compare') {
            //     $valueFromNe = $this->getInfoFromNe($info);
            // }

            // Store activity in Audit Trail
            AuditTrail::create([
                'action' => "View circuit details for service id. (" . $request->serviceId . ")",
                'user_id' => Auth::id(),
                'module' => 'Premium Dashboard',
            ]);

            return response()->json($svcInfo);
        } catch (Throwable $e) {
            return response()->json([
                'error' => 'An error occurred',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),], 404);
        }
    }

    // Get Info from NE
    public function getNeValue(Request $request) {
        try {
            // Validate and sanitize input
            $validatedData = Validator::make($request->all(), [
                'service_id' => ['required', 'alpha_num', Rule::notIn(['not available'])],
                'pe_device_id' => ['required', Rule::notIn(['not available'])],
                'pe_interface' => ['required', Rule::notIn(['not available'])],
            ]);

            // check if validation failed, send json error messages
            if ($validatedData->fails()) {
                return response()->json([
                    'error' => 'An error occurred',
                    'message' => $validatedData->errors()->all(),
                ], 400);
            }

            // Generate Commands to be send to Juniper API
            $ne_ip = $request->pe_device_id;
            $command = array(
                'show interfaces descriptions | match :service_id',
                'show configuration interface :pe_interface',
                'show configuration class-of-service interfaces :port unit :vlan | display set',
            );
            $placeholders = [
                ':service_id' => $request->service_id,
                ':pe_device_id' => $request->pe_device_id,
                ':pe_interface' => $request->pe_interface,
                ':port' => explode(".", $request->pe_interface)[0],
                ':vlan' => explode(".", $request->pe_interface)[1],
            ];
            $modifiedCommands = [];
            foreach ($command as $cmd) {
                $modifiedCmd = str_replace(array_keys($placeholders), array_values($placeholders), $cmd);
                $modifiedCommands[] = $modifiedCmd;
            }

            // Get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_JUNIPER_API_ENDPOINT') . '/pe/v2/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {

                $unisData = $request->unis_data;

                // Get PE interface
                // Define the regular expression pattern to match the desired value
                // Using sprintf to insert the service ID into the pattern
                $pattern = sprintf('/^(\S+)\s+(up|down)\s+(up|down)\s+.*%s/m', preg_quote($request->service_id, '/'));

                // Perform the regex match
                if (preg_match($pattern, $response->json()['data'][0]['ne_response'], $matches)) {
                    // The matched value is in the first capturing group
                    $unisData['PE Interface'][1] = $matches[1];
                } else {
                    $unisData['PE Interface'][1] = 'not found';
                }

                // Get Bandwidth
                // bandwidth\s+(\d+[kKmMgG]): Matches the 'bandwidth' keyword followed by one or more whitespace characters (\s+)
                // Captures one or more digits (\d+) followed by one of the letters k, m, g, K, M, or G
                $pattern = '/bandwidth\s+(\d+[kKmMgG]);/';

                // Perform the regex match
                if (preg_match($pattern, $response->json()['data'][1]['ne_response'], $matches)) {
                    // The matched value is in the first capturing group
                    $unisData['Bandwidth'][1] = strtoupper($matches[1]);
                } else {
                    $unisData['Bandwidth'][1] = 'not found';
                }

                // Get PE IP Address
                // address\s+([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+\/[0-9]+): Matches the word 'address' followed by one or more spaces (\s+),
                // then captures the IP address and subnet mask format (e.g., **************/30)
                $pattern = '/address\s+([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+\/[0-9]+)/';

                // Perform the regex match
                if (preg_match($pattern, $response->json()['data'][1]['ne_response'], $matches)) {
                    // The matched value is in the first capturing group
                    $unisData['PE WAN IP'][1] = $matches[1];
                } else {
                    $unisData['PE WAN IP'][1] = 'not found';
                }

                // Get QOS

                if ($unisData['Product'][0] != 'DIRECT') {

                    // set\s+class-of-service\s+interfaces\s+\S+\s+unit\s+\d+\s+scheduler-map\s+(\S+)
                    // This matches the 'set class-of-service interfaces' line with 'scheduler-map' and captures its value.
                    $pattern = '/set\s+class-of-service\s+interfaces\s+\S+\s+unit\s+\d+\s+scheduler-map\s+(\S+)/';

                    // Perform the regex match
                    if (preg_match($pattern, $response->json()['data'][2]['ne_response'], $matches)) {
                        // The matched scheduler-map value is in the first capturing group
                        $schedulerMap = $matches[1];
                        
                        // Define the QOS mapping based on the scheduler-map value
                        $qosMapping = [
                            'SCHED_CLASSIC' => 'CLASSIC',
                            'SCHED_CONVERGENCE' => 'CONVERGENCE',
                            'SCHED_VERSATILE' => 'VERSATILE',
                            'SCHED_ESSENTIAL' => 'ESSENTIAL',
                            // 'SCHED_CUSTOM' => 'CUSTOMISED'
                        ];
                        
                        // Get the QOS value based on the scheduler-map value
                        if (stripos($matches[1], 'custom') !== false) $unisData['QOS'][1] = 'CUSTOMIZED';
                        else $unisData['QOS'][1] = isset($qosMapping[$schedulerMap]) ? $qosMapping[$schedulerMap] : 'not found';
                    } else {
                        $unisData['QOS'][1] = 'not found';
                    }

                }

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Compare circuit details between UNIS and network element for service id. (" . $request->service_id . ")",
                    'user_id' => Auth::id(),
                    'module' => 'Premium Dashboard',
                ]);

                // Process the response data
                return response()->json([
                    'status' => 'success',
                    'output' => $response->json(),
                    'data' => $unisData,

                ]);
            } else {
                // send the error message
                return response()->json([
                    'status' => 'failed',
                    'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
                    'data' => $response->json(),
                    'details' => [
                        'endpoint' => $response->effectiveUri(),
                        'payload' => [
                            'ne_ip' => $ne_ip,
                            'commands' => $modifiedCommands,  
                        ],
                        'response_data' => $response->json()
                    ]
                ]);
            }

        } catch (Throwable $e) {
            return response()->json(['error' => 'An error occurred',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),], 404);
        }
    }

    // Get circuit latency
    public function getCircuitLatency(Request $request)
    {

        if ($request->customer !== 'All Customers') {

            // Get customer abbreviation
            $abbreviation = PremiumCustomer::where('name', $request->customer)->distinct()->pluck('abbreviation')->toArray();

            // Get list of circuit with customer abbreviation and match list of circuit with CE IP Address
            $ListCircuit = DB::table('temp_ce_ip')
            ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
            ->select('temp_ce_ip.*', 'unis_data.*')
            // ->select('unis_data.ServiceId as unis_service_id')
            ->whereIn('unis_data.CustAbbr', $abbreviation)
            // ->pluck('unis_data.ServiceId')
            ->get()
            ->toArray();

            // Get list of svc_id with customer abbreviation and match list of circuit with CE IP Address
            $listServiceIdmatchCeIp = DB::table('temp_ce_ip')
            ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
            // ->select('temp_ce_ip.*', 'unis_data.*')
            // ->select('unis_data.ServiceId as unis_service_id')
            ->whereIn('unis_data.CustAbbr', $abbreviation)
            ->pluck('unis_data.ServiceId')
            ->toArray();
            
        }
        
        else {
            // Get list of circuit match list of circuit with CE IP Address
            $ListCircuit = DB::table('temp_ce_ip')
            ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
            ->select('temp_ce_ip.*', 'unis_data.*')
            // ->pluck('unis_data.ServiceId')            
            ->get()
            ->toArray();

            // Get list of svc_id match list of circuit with CE IP Address
            $listServiceIdmatchCeIp = DB::table('temp_ce_ip')
            ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
            // ->select('temp_ce_ip.*', 'unis_data.*')
            ->pluck('unis_data.ServiceId')
            ->toArray();

        }

        if (sizeof($ListCircuit)===0) $data = array();     // check if no data return

        // Get latency for each service id
        $dataLaser = $this->getCircuitLatencyLaser($listServiceIdmatchCeIp);

        foreach ($ListCircuit as $element) {

            $latency = 'N/A';   // initialize latency value

            // Action - button view details            
            $action = "<td class='text-left'>
                <button name='btn-view-detail' type='button' class='btn btn-soft-primary btn-sm'><span class='btn-label'>View</span><i class='mdi mdi-spin mdi-loading visually-hidden'></i></button>
            </td>";

            foreach($dataLaser['data'] as $dataApi) {
                if ($dataApi['svc_number'] == $element->ServiceId) $latency = round($dataApi['median_rtt_avg']*1000, 2).'ms';
            }

            $data[] = [
                'CustAbbr' => $element->CustAbbr,
                // 'ServiceType' => $element->ServiceType,
                'Product' => $element->Product,
                // 'RedundancyId' => $element->RedundancyId,
                'ServiceId' => $element->ServiceId,
                'CeWanIp' => $element->ceWanIp,
                'Latency' => $latency,
                'Action' => $action
            ];

        }

        return array('circuitData' => $data);
    }

    // Get circuit latency from Laser API
    public function getCircuitLatencyLaser($listServiceIdmatchCeIp)
    {
        $response = Http::withHeaders([
            'X-Access-Token' => 'pLlWB6TavDNCkq8EOrWE6yfyQM7zcJ9Buvx6Bqh744Jpvx4kk1',
            'Content-Type' => 'application/json',
            'accept' => 'application/json'
        ])->post(env('NNOCGRID_LASER').'/statistic.php?action=fetch', [
            "svc_numbers" => $listServiceIdmatchCeIp
        ]);

        // Check if both the API requests were successful
        if ($response->successful()) return $response->json();
        else return false;
    }


    public function fetchImage(Request $request)
    {
        // $url = $request->query('url');
        $url ='https://ngossweb.tm.com.my/d-solo/Cj7nAKkNz/trending?orgId=88&var-cust_name=CIMB BANK BERHAD&var-service_id=BS1010905245&from=now-24h&to=now&panelId=2';
        $token = 'eyJrIjoiMlRJTXRMNGZjSDM4dDg0TU5aUkdBNzN3MTNrV0pjTHciLCJuIjoic2VsZi1zZXJ2ZSIsImlkIjo4OH0=';  // Replace with the actual token or retrieve dynamically if needed

        if (!$url) {
            return response()->json(['error' => 'URL is required'], 400);
        }

        // Make a request to the external URL with the Bearer token
        $response = Http::withToken($token)->get($url);

        if ($response->successful()) {
            $contentType = $response->header('Content-Type');

            return response($response->body(), 200)
                ->header('Content-Type', $contentType);
        } else {
            return response()->json(['error' => 'Failed to retrieve image'], $response->status());
        }
    }

    // Get Circuit Utilization from KOCI
    public function getCircuitUtilKoci($ListServiceId) {

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . env('KOCI_BEARER_TOKEN'),  // Replace with your actual authorization token
        ])
        ->post(env('KOCI') . '/selfserve.php', [
            'serviceid' => $ListServiceId,
        ]);

        // Check if both the API requests were successful
        if ($response->successful()) return $response->json();
        else return false;

    }

    // Generate data for Avg Util Table
    public function generateDataTableUtil(Request $request) {

        if ($request->customer !== 'All Customers') {
            // Get customer abbreviation
            $abbreviation = PremiumCustomer::where('name', $request->customer)->distinct()->pluck('abbreviation')->toArray();

            if ($request->customer === 'Maybank MA') {

                $list_customer_services = $this->readJson($request->customer);

                // Get all of service details match customer abbreviation
                $listOfServiceDetails = UnisData::whereIn('CustAbbr', $abbreviation)->whereIn('ServiceId', $list_customer_services)->get();

                // Get all list of service id in array form from UnisData table
                $listServiceId = UnisData::whereIn('CustAbbr', $abbreviation)->whereIn('ServiceId', $list_customer_services)->pluck('ServiceId')->toArray();
                
            }
            else {

                // Get all of service details match customer abbreviation
                $listOfServiceDetails = UnisData::whereIn('CustAbbr', $abbreviation)->get();

                // Get all list of service id in array form from UnisData table
                $listServiceId = UnisData::whereIn('CustAbbr', $abbreviation)->pluck('ServiceId')->toArray();

            }

            // Calculate Service Utilization
            if ($dataApiKoci = $this->getCircuitUtilKoci($listServiceId)) {
                if (isset($dataApiKoci['Success']) && $dataApiKoci['Success']) {
                    $avgUtilList = $dataApiKoci['data'];
                }
                else if (isset($dataApiKoci['Fail']) && !$dataApiKoci['Fail']) $avgUtilList = 'not available';
                else $avgUtilList = 'not available';
            }
            else $avgUtilList = 'not available';

            if ($avgUtilList=='not available') $final_output = array();
            else {
                // Massage Data to create table
                foreach($avgUtilList as $row) {
                    foreach($listOfServiceDetails as $service){
                        if ($row['svc_number']==$service->ServiceId){
    
                            $final_output[] = [
                                'CustAbbr' => $service->CustAbbr,
                                'ServiceId' => $service->ServiceId,
                                'Bw' => $service->Bandwidth,
                                'AvgUtil' => round($row['avg_util_24hrs'], 2) . '%'
                            ];
    
                        }
                    }
                }
            }
        }
        else {

            // Get all of service details match customer abbreviation
            $listOfServiceDetails = UnisData::get();

            // Get all list of service id in array form from UnisData table
            $listServiceId = UnisData::pluck('ServiceId')->toArray();

            // Calculate Service Utilization
            if ($dataApiKoci = $this->getCircuitUtilKoci($listServiceId)) {
                if (isset($dataApiKoci['Success']) && $dataApiKoci['Success']) {
                    $avgUtilList = $dataApiKoci['data'];
                }
                else if (isset($dataApiKoci['Fail']) && !$dataApiKoci['Fail']) $avgUtilList = 'not available';
                else $avgUtilList = 'not available';
            }
            else $avgUtilList = 'not available';

            if ($avgUtilList=='not available') $final_output = array();
            else {
                // Massage Data to create table
                foreach($avgUtilList as $row) {
                    foreach($listOfServiceDetails as $service){
                        if ($row['svc_number']==$service->ServiceId){
    
                            $final_output[] = [
                                'CustAbbr' => $service->CustAbbr,
                                'ServiceId' => $service->ServiceId,
                                'Bw' => $service->Bandwidth,
                                'AvgUtil' => round($row['avg_util_24hrs'], 2) . '%'
                            ];
    
                        }
                    }
                }

            }

        }

        return array('CircuitWithAvgUtil' => $final_output);


    }


}

