<?php

namespace App\Http\Controllers;
use App\Models\AuditTrail;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Throwable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Carbon;

class ServiceTestingController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        ini_set('max_execution_time', 1200);
    }

    public function index()
    {
        return view('self-serve.service-testing-handover.service-testing.index');
    }

    protected $service_id;
    // Get Service Info from UNIS
    public function show(Request $request)
    {

        // validate input format
        $request->validate([
            'service_id' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
        ]);

        try {

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('SELFSERVE_UNIS_API_ENDPOINT'), [
                "NIGRequest" => [
                    "Layer" => "BOTH",
                    "graniteid" => $request->service_id,
                    "Qscenario" => "POSITIVE",
                    "serviceType" => ""
                ]
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {
                // Get the JSON response body as an array
                $data = $response->json();

                // Check if UNIS API returns no data
                if (strpos($data['NIGResponse']['ErrorMesage'], 'no data found') !== false) throw new \Exception(__('No matching Service Id. in UNIS'));

                // initialize array value with not available
                $svcInfo = array(
                    'service_id' => 'not available',
                    'customer_name' => 'not available',
                    'service' => 'not available',
                    'parent_service'=> 'not available',
                    'routing_protocol' => 'not available',
                    'pe' => 'not available',
                    'pe_vendor' => 'not available',
                    'pe_device_id'=>'not available',
                    'port_detail' => 'not available',
                    'bandwidth' => 'not available',
                    'tm_wan_ipv4' => 'not available',
                    'ce_ip' => 'not available',
                    'tm_wan_ipv6' => 'not available',
                    'ce_ipv6' => 'not available',
                    'nid_upe' => 'not available',
                    'nid_upe_model' => 'not available',
                    'nid_upe_vendor' => 'not available',
                    'nid_upe_device_id' => 'not available',
                    'nid_upe_interface_ul_1' => 'not available',
                    'burstable' => 'not available',
                );

                // if value exist in API, replace the initial values
                $svcInfo['service_id'] = $request->service_id;

                // find L2 Service Id
                foreach ($data['NIGResponse']['layer2'] as $info) {
                    // General Info
                    if ($info['name'] === 'Name' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) $svcInfo['L2_id'] = $info['value'];
                }

                // massage L2 info
                foreach ($data['NIGResponse']['layer2'] as $info) {

                    if ($info['name'] === 'Device Name' && $info['group'] === $svcInfo['L2_id']) $deviceName_layer2 = $info['value'];
                    else if ($info['name'] === 'Interface Name' && $info['type'] === 'Layer2' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $port_detail_layer2 = $info['value'];

                    else if ($info['name'] === 'Device Name' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe'] = $info['value'];
                    else if ($info['name'] === 'Model' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_model'] = $info['value'];
                    else if ($info['name'] === 'Vendor' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_vendor'] = $info['value'];
                    else if ($info['name'] === 'Device IP' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_device_id'] = $info['value'];
                    // for UPE/NID (1)
                    else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK' && $info['group'] === $request->service_id) {
                        if ($svcInfo['nid_upe_interface_ul_1'] == 'not available') {
                            $svcInfo['nid_upe_interface_ul_1'] = $info['value'];
                            continue;
                        }
                    }
                }

                if (isset($data['NIGResponse']['layer3'])) {

                    // massage L3 info
                    foreach ($data['NIGResponse']['layer3'] as $info) {

                        if ($info['name'] === 'Device Name' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $deviceName_layer3 = $info['value'];
                        else if ($info['name'] === 'Parent Services' && $info['type'] === 'IP TRANSIT' && $info['group'] === $request->service_id) $svcInfo['parent_services'] = $info['value'];
                        else if ($info['name'] === 'Device IP' && $info['field'] === 'Container' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_device_id'] = $info['value'];
                        else if ($info['name'] === 'Vendor' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_vendor'] = $info['value'];
                        else if ($info['name'] === 'Interface Name' && $info['type'] === 'PE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $port_detail_layer3 = $info['value'];
                        else if ($info['name'] === 'Logical Interface' && $info['type'] === 'PE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $svcInfo['vlan'] = $info['value'];
                        else if ($info['name'] === 'Bandwidth' && $info['group'] === $request->service_id) $svcInfo['bandwidth'] = $info['value'];
                        else if ($info['name'] === 'Routing Protocol' && $info['type'] === 'L3 Service' && $info['group'] === $request->service_id) $svcInfo['routing_protocol'] = $info['value'];
                        else if ($info['name'] === 'Category' && $info['group'] !== $request->service_id) $svcInfo['service'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['tm_wan_ipv4'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['tm_wan_ipv6'] = $info['value'];
                        else if ($info['name'] === 'CIDR' && $info['group'] === $request->service_id) $cidr = $info['value'];
                        else if ($info['name'] === 'CIDR IPv6' && $info['group'] === $request->service_id) $cidr_ipv6 = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_ip'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_ipv6'] = $info['value'];
                        else if ($info['name'] === 'TAG' && $info['type'] === 'L3 Service' && $info['group'] === $request->service_id) $svcInfo['burstable'] = $info['value'];
                    }

                    // Add CIDR to IPv4 and IPv6
                    if ($svcInfo['tm_wan_ipv4'] !== 'not available') $svcInfo['tm_wan_ipv4'] .= '/' . $cidr;
                    if ($svcInfo['tm_wan_ipv6'] !== 'not available') $svcInfo['tm_wan_ipv6'] .= '/' . $cidr_ipv6;
                    if ($svcInfo['ce_ip'] !== 'not available') $svcInfo['ce_ip'] .= '/' . $cidr;
                    if ($svcInfo['ce_ipv6'] !== 'not available') $svcInfo['ce_ipv6'] .= '/' . $cidr_ipv6;

                }

                // Re-assign PE/AGG information
                if (stripos($deviceName_layer2, 'agg') !== false) {
                    $svcInfo['pe'] = $deviceName_layer2;
                    $svcInfo['port_detail'] = $port_detail_layer2;

                    if (stripos($deviceName_layer2, 'zt') !== false) $svcInfo['pe_vendor'] = 'ZTE';
                    else if (stripos($deviceName_layer2, 'hw') !== false) $svcInfo['pe_vendor'] = 'HUAWEI';
                    else if (stripos($deviceName_layer2, 'nk') !== false) $svcInfo['pe_vendor'] = 'NOKIA';
                    else $svcInfo['pe_vendor'] = $svcInfo['pe_vendor'];

                } else {

                    // Check if $port_detail_layer3 contains a `.`
                    if (strpos($port_detail_layer3, '.') !== false) {
                        $port_parts = explode('.', $port_detail_layer3);
                        $port_detail_layer3 = $port_parts[0];  // Get the part before the `.`
                    }

                    $svcInfo['pe'] = $deviceName_layer3;
                    // Extract the number from the value
                    $vlan = $this->extractNumberFromValue($svcInfo['vlan']);

                    if ($port_detail_layer3 !== 'not available' && $vlan !== null) {
                        $port_detail_layer3 .= '.' . $vlan;
                    }
                    $svcInfo['port_detail'] = $port_detail_layer3;

                }



                // get customer name
                $this->service_id = $svcInfo['service_id'];
                $svcInfo['customer_name'] = $this->getCustomerName();

                // add success message
                $svcInfo['success'] = 'Successfully retrieved info from UNIS';

                // sort array alphabetically
                ksort($svcInfo);

                // testing massage data
                // dd($svcInfo);


                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Searched service info for service id. (" . $request->service_id . ")",
                    'user_id' => Auth::id(),
                    'module' => 'Service Testing'
                ]);

                // if success, redirect with data
                return redirect()->back()->with('data', $svcInfo);

                // Return the data to the client
                // return response()->json([
                //     'status' => 'success',
                //     'message' => 'Matched Service Id.',
                //     'details' => $svcInfo
                // ]);
            } else {
                // Handle non-2xx status code responses
                return redirect()->back()->with('error', $response->body());
            }

        } catch (Throwable $error) {
            return redirect()->back()->with('error', $error->getMessage());
        }

    }

    // get customer name
    public function getCustomerName() {

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_WORKFLOW_API_ENDPOINT').'/database/v1/ogg/get_customer_name', [
            'service_number' => $this->service_id,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['output']==='no data found in db') return 'not available';
            // else return customer name
            else return $response->json()['output']['customer_name'];
        } else {
            // if error return not available
            return 'not available';
        }
    }

    // public function extractNumberFromValue($value)
    // {
    //     $pattern = '/\d+$/';  // This pattern matches the digits at the end of the string
    //     preg_match($pattern, $value, $matches);

    //     return $matches[0] ?? null;  // Return the matched value or null if no match is found
    // }

    public function extractNumberFromValue($value)
    {
        // Check if the string contains `-.`
        if (strpos($value, '-.') !== false) {
            $pattern = '/\d+$/';  // This pattern matches the digits at the end of the string
            preg_match($pattern, $value, $matches);
            
            return $matches[0] ?? null;  // Return the matched value or null if no match is found
        }
        
        // If the string contains `-` but not `-.`, return null
        if (strpos($value, '-') !== false) {
            return null;
        }
        
        // If the string contains a `.` but not `-`, do nothing
        if (strpos($value, '.') !== false) {
            return null;
        }
    
        return null;  // Default return null if no `-.` or `-` is found
    }

    // Call Service Testing API for Juniper
    public function getConfigNeJuniper(Request $request) {

        // Validate the input
        $inputToValidate = [
            'service_id' => ['required', Rule::notIn(['not available'])],
            'customer_name' => ['required', Rule::notIn(['not available'])],
            'service' => ['required', Rule::notIn(['not available'])],
            'pe' => ['required', Rule::notIn(['not available'])],
            'pe_vendor' => ['required', Rule::notIn(['not available'])],
            'pe_device_id' => ['required', Rule::notIn(['not available'])],
            'port_detail' => ['required', Rule::notIn(['not available'])],
            'bandwidth' => ['required', Rule::notIn(['not available'])],
            'tm_wan_ipv4' => ['required', Rule::notIn(['not available'])],
            'ce_ip' => ['required', Rule::notIn(['not available'])],
            // 'burstable' => ['required', Rule::notIn(['not available'])],
            // 'tm_wan_ipv6' => ['required', Rule::notIn(['not available'])],
        ];

        if ($request->nidLoopbackChecking == 'checked_value') {
            $rules = [
                'nid_upe' => ['required', Rule::notIn(['not available'])],
                'nid_upe_model' => ['required', Rule::notIn(['not available'])],
                'nid_upe_vendor' => ['required', Rule::notIn(['not available'])],
                'nid_upe_device_id' => ['required', Rule::notIn(['not available'])],
                // 'nid_upe_interface_ul_1' => ['required', Rule::notIn(['not available'])],
            ];

            // Merge the conditional rules into the base rules
            $inputToValidate = array_merge($inputToValidate, $rules);
        }

        // Define additional validation rules conditionally if traceRoute is checked
        if ($request->traceRoute == 'checked_value') {

            $rules = [
                'total_traceroute_lan_ip' => ['required', 'integer', 'min:1'], // Ensure total_traceroute_lan_ip is an integer and more than 0
                'tracerouteLanIPs' => ['required', 'array'], // Always required, but length will be checked conditionally
                'tracerouteLanIPs' => 'min:1|max:' . $request->input('total_traceroute_lan_ip'), // Dynamically set max length based on total_traceroute_lan_ip value
                // 'tracerouteLanIPs.*' => 'required|ip', // Ensure each LAN IP is a valid IP address
                'tracerouteLanIPs.*' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        // Check if the value is a valid IP address or IP with subnet (e.g., ***********/24)
                        if (!filter_var($value, FILTER_VALIDATE_IP) && !preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$/', $value)) {
                            $fail('The '.$attribute.' must be a valid IP address or an IP address with a subnet mask.');
                        }
                    },
                ],
            ];

            // Merge the conditional rules into the base rules
            $inputToValidate = array_merge($inputToValidate, $rules);
        }

        // Define additional validation rules conditionally if pingLANIP is checked
        if ($request->pingLANIP == 'checked_value') {
            $rules = [
                'total_ping_lan_ip' => ['required', 'integer', 'min:1'], // Ensure total_traceroute_lan_ip is an integer and more than 0
                'pingLanIPs' => ['required', 'array'], // Always required, but length will be checked conditionally
                'pingLanIPs' => 'min:1|max:' . $request->input('total_ping_lan_ip'), // Dynamically set max length based on total_traceroute_lan_ip value
                // 'pingLanIPs.*' => 'required|ip', // Ensure each LAN IP is a valid IP address
                'pingLanIPs.*' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        // Check if the value is a valid IP address or IP with subnet (e.g., ***********/24)
                        if (!filter_var($value, FILTER_VALIDATE_IP) && !preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$/', $value)) {
                            $fail('The '.$attribute.' must be a valid IP address or an IP address with a subnet mask.');
                        }
                    },
                ],
            ];

            // Merge the conditional rules into the base rules
            $inputToValidate = array_merge($inputToValidate, $rules);
        }

        // Define additional validation rules conditionally if advertiseRoute is checked
        if ($request->advertiseRoute == 'checked_value') {
            $rules = [
                'total_advertise_route_lan_ip' => ['required', 'integer', 'min:1'], // Ensure total_traceroute_lan_ip is an integer and more than 0
                'advertiseRouteLanIPs' => ['required', 'array'], // Always required, but length will be checked conditionally
                'advertiseRouteLanIPs' => 'min:1|max:' . $request->input('total_advertise_route_lan_ip'), // Dynamically set max length based on total_traceroute_lan_ip value
                // 'advertiseRouteLanIPs.*' => 'required|ip', // Ensure each LAN IP is a valid IP address
                'advertiseRouteLanIPs.*' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        // Check if the value is a valid IP address or IP with subnet (e.g., ***********/24)
                        if (!filter_var($value, FILTER_VALIDATE_IP) && !preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$/', $value)) {
                            $fail('The '.$attribute.' must be a valid IP address or an IP address with a subnet mask.');
                        }
                    },
                ],
            ];

            // Merge the conditional rules into the base rules
            $inputToValidate = array_merge($inputToValidate, $rules);
        }

        // Define additional validation rules conditionally if showRoutingTable is checked
        if ($request->showRoutingTable == 'checked_value') {
            $rules = [
                'total_show_routing_table_lan_ip' => ['required', 'integer', 'min:1'], // Ensure total_traceroute_lan_ip is an integer and more than 0
                'showRoutingTableLanIPs' => ['required', 'array'], // Always required, but length will be checked conditionally
                'showRoutingTableLanIPs' => 'min:1|max:' . $request->input('total_show_routing_table_lan_ip'), // Dynamically set max length based on total_traceroute_lan_ip value
                // 'showRoutingTableLanIPs.*' => 'required|ip', // Ensure each LAN IP is a valid IP address
                'showRoutingTableLanIPs.*' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        // Check if the value is a valid IP address or IP with subnet (e.g., ***********/24)
                        if (!filter_var($value, FILTER_VALIDATE_IP) && !preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}\/[0-9]{1,2}$/', $value)) {
                            $fail('The '.$attribute.' must be a valid IP address or an IP address with a subnet mask.');
                        }
                    },
                ],
            ];

            // Merge the conditional rules into the base rules
            $inputToValidate = array_merge($inputToValidate, $rules);
        }

        // Validate and sanitize input
        $validatedData = Validator::make($request->all(), $inputToValidate);

        // check if validation failed, send json error messages
        if ($validatedData->fails()) {
            return response()->json([
                'status' => 'failed',
                'output' => $validatedData->errors()->all()
            ]);
        }

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "Running service testing for NE Juniper from ".strtoupper($request->pe)." for service id. (".$request->service_id.")",
            'user_id' => Auth::id(),
            'module' => 'Service Testing'
        ]);


        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->timeout(120)
        ->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/v1/self_serve/service_testing', [
            'network_type' => 'pe',
            'vendor' => strtolower($request->pe_vendor),
            'ne_ip' => $request->pe_device_id,              //'************', 
            'pe_interface' => $request->port_detail,        //'xe-7/1/0.1021',
            'pe_ip' => $request->tm_wan_ipv4,
            'ce_ip' => $request->ce_ip,
            'pe_ipv6' => $request->tm_wan_ipv6,
            'ce_ipv6' => $request->ce_ipv6,
            'nid_model' => $request->nid_upe_vendor ?? 'string',
            'nid_device_id' => $request->nid_upe_device_id ?? 'string',
            'nid_port_uplink' => $request->nid_upe_interface_ul_1 ?? 'string',
            'service_testing' => [
                'interface_status' => $request->interfaceStatus == 'checked_value' ? true : false,
                'interface_power_level' => $request->interfacePowerLevel == 'checked_value' ? true : false,
                'bandwidth_subscription' => $request->bandwidthSubscription == 'checked_value' ? true : false,
                'ping_test_pe_ce' => $request->pingTestPECE == 'checked_value' ? true : false,
                'ping_gipvpn_pe_ce' => $request->pingGIPVPNPECE == 'checked_value' ? true : false,
                'traceroute' => $request->traceRoute == 'checked_value' ? true : false,
                'ping_lan_ip' => $request->pingLANIP == 'checked_value' ? true : false,
                'incoming_route' => $request->incomingRoute == 'checked_value' ? true : false,
                'advertise_route' => $request->advertiseRoute == 'checked_value' ? true : false,
                'route_summary' => $request->routeSummary == 'checked_value' ? true : false,
                'ip_prefix_register' => $request->ipPrefixRegister == 'checked_value' ? true : false,
                'bgp_status' => $request->bgpStatus == 'checked_value' ? true : false,
                'show_routing_table' => $request->showRoutingTable == 'checked_value' ? true : false,
                'nid_loopback_checking' => $request->nidLoopbackChecking == 'checked_value' ? true : false,
                'l2vpn_mpls_ping_domestic_igw' => $request->l2vpnMplsPing == 'checked_value' ? true : false,
            ],
            'traceroute_lan_ip' => $request->traceRoute == 'checked_value' ? $request->tracerouteLanIPs : ['string'],
            'ping_lan_ip' => $request->pingLANIP == 'checked_value' ? $request->pingLanIPs : ['string'],
            'advertise_route_lan_ip' => $request->advertiseRoute == 'checked_value' ? $request->advertiseRouteLanIPs : ['string'],
            'show_routing_table_lan_ip' => $request->showRoutingTable == 'checked_value' ? $request->showRoutingTableLanIPs : ['string']
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {

            // Process the response data
            return response()->json([
                'status' => 'success',
                'output' => $response->json()
            ]);

        } else {
            // send the error message
            return response()->json([
                'status' => 'failed',
                'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
                'data' => $response->json()
            ]);
        }


        // PS1093086947 - ada vlan - Static
        // PS1095929168 - takda vlan - BGP
        // PS1044911569 - takda vlan - global ipvpn
        // PS1018857975 - takda vlan - BGP
        // PS1085553511 - takda vlan - BGP
        // PS1096799617

        // // Command array
        // $command = array();

        // if ($request->interfaceStatus == 'checked_value') {
        //     $command[] = 'show interfaces :pe_interface';
        //     $command[] = 'show interfaces :pe_port | match ae';
        //     // If found interface ae, proceed show interfaces ae
        //     $command[] = 'show interfaces :ae_interface';
        // }
        // if ($request->interfacePowerLevel == 'checked_value') {
        //     $command[] = 'show interfaces diagnostics optics :pe_port';
        // }
        // if ($request->bandwidthSubscription == 'checked_value') {
        //     $command[] = 'show interfaces :pe_interface description';
        //     $command[] = 'show interfaces :pe_port description';
        //     // If ae interface return value
        //     $command[] = 'show configuration interfaces :ae_interface unit :ae_vlan | match policer';
        //     // If ae interface not return value
        //     $command[] = 'show configuration interfaces :pe_interface | match policer';
        //     // Shapping Rate
        //     $command[] = 'show configuration class-of-service interfaces :pe_port unit :pe_vlan shaping-rate';
        // }
        // if ($request->pingTestPECE == 'checked_value') {
        //     $command[] = 'ping :pe_ip source :ce_ip count 10';
        // }
        // if ($request->pingGIPVPNPECE == 'checked_value') {
        //     $command[] = 'ping :pe_ip routing-instance :vpn source :ce_ip count 10';
        // }
        // if ($request->traceRoute == 'checked_value') {
        //     // Assuming $lanIpArray is an array containing LAN IP addresses
        //     $lanIpArray = $request->input('tracerouteLanIPs');
        //     // Iterate over each LAN IP address and add a traceroute command for each
        //     foreach ($lanIpArray as $lanIp) {
        //         $command[] = 'traceroute ' . $lanIp;
        //     }
        // }
        // if ($request->pingLANIP == 'checked_value') {
        //     // Assuming $lanIpArray is an array containing LAN IP addresses
        //     $lanIpArray = $request->input('pingLanIPs');
        //     // Iterate over each LAN IP address and add a traceroute command for each
        //     foreach ($lanIpArray as $lanIp) {
        //         $command[] = 'ping ' . $lanIp . ' source ' . explode("/", $request->tm_wan_ipv4)[0];
        //     }
        // }

        // if ($request->incomingRoute == 'checked_value') {
        //     $command[] = 'show route receive-protocol bgp '.explode("/", $request->ce_ip)[0];
        // }

        // if ($request->advertiseRoute == 'checked_value') {
        //     // Assuming $lanIpArray is an array containing LAN IP addresses
        //     $lanIpArray = $request->input('advertiseRouteLanIPs');
        //     // Iterate over each LAN IP address and add a traceroute command for each
        //     foreach ($lanIpArray as $lanIp) {
        //         $command[] = 'show route advertising-protocol bgp ' . explode("/", $request->ce_ip)[0] . ' match prefix ' . $lanIp;
        //     }
        // }

        // if ($request->routeSummary == 'checked_value') {
        //     $command[] = 'show bgp neighbor :ce_ip | match prefix';
        // }

        // if ($request->ipPrefixRegister == 'checked_value') {
        //     $command[] = 'show configuration | match :ce_ip | match IP_TRANSIT | display set';
        //     $command[] = 'show configuration policy-options policy-statement :policy_name';
        // }

        // if ($request->bgpStatus == 'checked_value') {
        //     $command[] = 'show bgp summary | match :ce_ip';
        // }

        // if ($request->showRoutingTable == 'checked_value') {
        //     // Assuming $lanIpArray is an array containing LAN IP addresses
        //     $lanIpArray = $request->input('showRoutingTableLanIPs');
        //     // Iterate over each LAN IP address and add a traceroute command for each
        //     foreach ($lanIpArray as $lanIp) {
        //         $command[] = 'show route ' . $lanIp;
        //     }
        // }

        // if ($request->nidLoopbackChecking == 'checked_value') {
        //     $command[] = 'show interface :nid_port_uplink loopback';
        // }

        // if ($request->l2vpnMplsPing == 'checked_value') {
        //     $command[] = 'ping mpls l2vpn instance :network_name local-site-id 1 remote-site-id count 25';
        // }

        // // Replace placeholders in $command with values from $validatedData
        // $portDetail = $request->port_detail;

        // if (strpos($portDetail, '.') !== false) {
        //     $portParts = explode('.', $portDetail);
        //     $pe_port = $portParts[0];
        //     $pe_vlan = $portParts[1];
        // } else {
        //     $pe_port = $portDetail;
        //     $pe_vlan = 0;
        // }
        
        // $placeholders = [
        //     ':pe_interface' => $portDetail,
        //     ':pe_port' => $pe_port,
        //     ':pe_vlan' => $pe_vlan,
        //     ':pe_ip' => $request->tm_wan_ipv4,
        //     ':ce_ip' => $request->ce_ip,
        //     ':nid_port_uplink' => $request->nid_upe_interface_ul_1,
        // ];
        

        // $modifiedCommands = [];
        // foreach ($command as $cmd) {
        //     $modifiedCmd = str_replace(array_keys($placeholders), array_values($placeholders), $cmd);
        //     $modifiedCommands[] = $modifiedCmd;
        // }

        // $response = Http::withHeaders([
        //     'Content-Type' => 'application/json',
        // ])
        // ->timeout(120)
        // ->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/v1/self_serve/', [
        //     'network_type' => 'pe',
        //     'vendor' => 'juniper',
        //     'ne_ip' => $request->pe_device_id,
        //     'commands' => $modifiedCommands,        
        // ]);

        // // Check if the request was successful (status code 2xx)
        // if ($response->successful()) {
        //     // Process the response data
        //     return response()->json([
        //         'status' => 'success',
        //         'output' => $response->json()
        //     ]);
        // } else {
        //     // send the error message
        //     return response()->json([
        //         'status' => 'failed',
        //         'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
        //         'data' => $response->json()
        //     ]);
        // }

    }

    // NE Nokia
    public function getConfigNeNokia(Request $request) {
        
    }

    // NE ZTE
    public function getConfigNeZTE(Request $request) {
        
    }

    // NE Huawei
    public function getConfigNeHuawei(Request $request) {
        
    }


    // Save Log Interface Status
    public function saveLogTesting(Request $request) {

        $neResponse = $request->log;
        // Set the timezone to GMT+8 and get the current date and time
        // $currentDateTime = Carbon::now('Asia/Singapore')->format('Ymd_His'); // Asia/Singapore is GMT+8
        $currentDateTime = Carbon::now('Asia/Singapore')->format('Ymd'); // Asia/Singapore is GMT+8
        $filename = "{$request->service_id}_{$request->type}_{$currentDateTime}_log.txt";            // Generate the filename

        // Save the data to a text file in the 'testing' folder
        Storage::disk('public')->put("service_testing/log/{$request->service_id}/{$request->type}/{$filename}", $neResponse);

        return response()->json([
            'status' => 'success',
            'message' => 'File saved successfully',
            'filename' => $filename
        ], 200);

    }


}

    





