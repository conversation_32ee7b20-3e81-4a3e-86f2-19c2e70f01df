<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\Shared\Html;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class DocumentController extends Controller
{
    public function generateHandoverDocumentExternal(Request $request)
    {

        try {

            // Load the template
            // $templatePath = storage_path('app/public/template/template_handover_document_external.docx');
            // $templatePath = resource_path('self-serve/service-testing-handover/service-handover/template_handover_document/template_handover_document_external.docx');
            $templatePath = resource_path('views'.DIRECTORY_SEPARATOR.'self-serve'.DIRECTORY_SEPARATOR.'service-testing-handover'.DIRECTORY_SEPARATOR.'service-handover'.DIRECTORY_SEPARATOR.'template_handover_document'.DIRECTORY_SEPARATOR.'template_handover_document_external.docx');
            $templateProcessor = new \PhpOffice\PhpWord\TemplateProcessor($templatePath);

            if (!file_exists($templatePath)) {
                throw new Exception('Template file not found');
            }

            // Replace placeholders with actual values
            // $templateProcessor->setValue('temp_product_name_title', ucwords($request->product_name));
            // $templateProcessor->setValue('temp_company_name', $request->company_name);
            // $templateProcessor->setValue('temp_attention', $request->attention);
            // $templateProcessor->setValue('temp_typeoforder', $request->typeoforder);
            // $templateProcessor->setValue('temp_email', $request->email);
            // $templateProcessor->setValue('temp_reference', $request->reference);
            // $templateProcessor->setValue('temp_product_name', strtoupper($request->product_name));
            // $templateProcessor->setValue('temp_speed', strtoupper($request->speed));
            // $templateProcessor->setValue('temp_installation_address_leg_a', $request->installation_address_leg_a);
            // $templateProcessor->setValue('temp_installation_address_leg_b', $request->installation_address_leg_b);
            // $templateProcessor->setValue('temp_circuit_id', strtoupper($request->circuit_id));
            // $templateProcessor->setValue('temp_international_circuit_ref', strtoupper($request->international_circuit_ref));
            // $templateProcessor->setValue('temp_bearer_no', $request->bearer_no);
            // $templateProcessor->setValue('temp_handover_date', $request->handover_date);
            // $templateProcessor->setValue('temp_bil_effective_date', $request->bil_effective_date);
            // $templateProcessor->setValue('temp_decription', $request->description);
            // $templateProcessor->setValue('temp_tm_signature_name', $request->signature_name);
            // $templateProcessor->setValue('temp_tm_signature_date', $request->signature_date);

            // Define the styles
            $fontStyle = 'font-family: Arial; font-size: 12pt; color: #000000;';

            // Replace placeholders with actual values and apply styles
            $templateProcessor->setValue('temp_product_name_title', ucwords($request->product_name) ?? 'N/A');
            $templateProcessor->setValue('temp_company_name', ($request->company_name && $request->company_name != 'not available') ? $request->company_name : 'N/A');
            $templateProcessor->setValue('temp_attention', ($request->attention && $request->attention != 'not available') ? $request->attention : 'N/A');
            $templateProcessor->setValue('temp_typeoforder', ($request->typeoforder && $request->typeoforder != 'not available') ? $request->typeoforder : 'N/A');
            $templateProcessor->setValue('temp_email', ($request->email && $request->email != 'not available') ? $request->email : 'N/A');
            $templateProcessor->setValue('temp_reference', ($request->reference && $request->reference != 'not available') ? $request->reference : 'N/A');
            $templateProcessor->setValue('temp_subject', 'TM INTERNATIONAL ' . strtoupper($request->product_name) . ' HANDOVER LETTER');
            $templateProcessor->setValue('temp_product_name', ($request->product_name && strtoupper($request->product_name) != 'NOT AVAILABLE') ? strtoupper($request->product_name) : 'N/A');
            $templateProcessor->setValue('temp_speed', ($request->speed && strtoupper($request->speed) != 'NOT AVAILABLE') ? strtoupper($request->speed) : 'N/A');
            $templateProcessor->setValue('temp_installation_address_leg_a', ($request->installation_address_leg_a && $request->installation_address_leg_a != 'not available') ? $request->installation_address_leg_a : 'N/A');
            $templateProcessor->setValue('temp_installation_address_leg_b', ($request->installation_address_leg_b && $request->installation_address_leg_b != 'not available') ? $request->installation_address_leg_b : 'N/A');
            $templateProcessor->setValue('temp_circuit_id', ($request->circuit_id && strtoupper($request->circuit_id) != 'NOT AVAILABLE') ? strtoupper($request->circuit_id) : 'N/A');
            $templateProcessor->setValue('temp_international_circuit_ref', ($request->international_circuit_ref && strtoupper($request->international_circuit_ref) != 'NOT AVAILABLE') ? strtoupper($request->international_circuit_ref) : 'N/A');
            $templateProcessor->setValue('temp_bearer_no', ($request->bearer_no && $request->bearer_no != 'not available') ? $request->bearer_no : 'N/A');
            $templateProcessor->setValue('temp_handover_date', ($request->handover_date && $request->handover_date != 'not available') ? $request->handover_date : 'N/A');
            $templateProcessor->setValue('temp_bil_effective_date', ($request->bil_effective_date && $request->bil_effective_date != 'not available') ? $request->bil_effective_date : 'N/A');
            $templateProcessor->setValue('temp_decription', ($request->description && $request->description != 'not available') ? $request->description : 'N/A');
            $templateProcessor->setValue('temp_tm_signature_name', ($request->signature_name && $request->signature_name != 'not available') ? $request->signature_name : 'N/A');
            $templateProcessor->setValue('temp_tm_signature_date', ($request->signature_date && $request->signature_date != 'not available') ? $request->signature_date : 'N/A');

            // Save the document to a temporary file
            $fileName = 'document_' . time() . '.docx';
            $tempFilePath = storage_path('app/public/' . $fileName);
            $templateProcessor->saveAs($tempFilePath);

            // Return the file as a download response
            return response()->download($tempFilePath)->deleteFileAfterSend(true);

        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }

    }


    public function generateHandoverDocumentInternal(Request $request)
    {

        try {

            // Load the template
            // $templatePath = storage_path('app/public/template/template_handover_document_internal.docx');
            // Construct the template path
            $templatePath = resource_path('views'.DIRECTORY_SEPARATOR.'self-serve'.DIRECTORY_SEPARATOR.'service-testing-handover'.DIRECTORY_SEPARATOR.'service-handover'.DIRECTORY_SEPARATOR.'template_handover_document'.DIRECTORY_SEPARATOR.'template_handover_document_internal.docx');

            $templateProcessor = new \PhpOffice\PhpWord\TemplateProcessor($templatePath);

            if (!file_exists($templatePath)) {
                throw new Exception('Template file not found');
            }

            if ($request->burstable == 'BURSTABLE') { $temp_bandwidth = $request->min_bandwith . ' BURSTABLE ' . $request->bandwidth; }
            else if ($request->burstable == 'No') { $temp_bandwidth = $request->bandwidth; }

            // Replace placeholders with actual values
            $templateProcessor->setValue('temp_company_name', ($request->company_name && $request->company_name != 'not available') ? $request->company_name : 'N/A');
            $templateProcessor->setValue('temp_service_id_lt', ($request->service_id_lt && $request->service_id_lt != 'not available') ? $request->service_id_lt : 'N/A');
            $templateProcessor->setValue('temp_bandwidth', ($temp_bandwidth && $temp_bandwidth != 'not available') ? $temp_bandwidth : 'N/A');
            $templateProcessor->setValue('temp_pe_node', ($request->pe_node && $request->pe_node != 'not available') ? $request->pe_node : 'N/A');
            $templateProcessor->setValue('temp_interface', ($request->pe_interface && $request->pe_interface != 'not available') ? $request->pe_interface : 'N/A');
            $templateProcessor->setValue('temp_date_generated_document', date('Y-m-d'));
            $templateProcessor->setValue('temp_customer_name', ($request->company_name && $request->company_name != 'not available') ? $request->company_name : 'N/A');
            $templateProcessor->setValue('temp_service_id_ps', ($request->service_id && $request->service_id != 'not available') ? $request->service_id : 'N/A');
            $templateProcessor->setValue('temp_as_number', ($request->as_number && $request->as_number != 'not available') ? $request->as_number : 'N/A');
            $templateProcessor->setValue('temp_prefix', ($request->prefix && $request->prefix != 'not available') ? $request->prefix : 'N/A');
            $templateProcessor->setValue('temp_product', ($request->product && $request->product != 'not available') ? $request->product : 'N/A');
            $templateProcessor->setValue('temp_protocol', ($request->protocol && $request->protocol != 'not available') ? $request->protocol : 'N/A');
            $templateProcessor->setValue('temp_vpn', ($request->vpn && $request->vpn != 'not available') ? $request->vpn : 'N/A');
            $templateProcessor->setValue('temp_tm_wan_ipv4_pe', ($request->tm_wan_ipv4 && $request->tm_wan_ipv4 != 'not available') ? $request->tm_wan_ipv4 : 'N/A');
            $templateProcessor->setValue('temp_tm_wan_ipv4_ce', ($request->ce_wan_ipv4 && $request->ce_wan_ipv4 != 'not available') ? $request->ce_wan_ipv4 : 'N/A');
            $templateProcessor->setValue('temp_tm_wan_ipv6_pe', ($request->tm_wan_ipv6 && $request->tm_wan_ipv6 != 'not available') ? $request->tm_wan_ipv6 : 'N/A');
            $templateProcessor->setValue('temp_tm_wan_ipv6_ce', ($request->ce_wan_ipv6 && $request->ce_wan_ipv6 != 'not available') ? $request->ce_wan_ipv6 : 'N/A');
            $templateProcessor->setValue('temp_tm_lan_ipv4', ($request->lan_ip && $request->lan_ip != 'not available') ? $request->lan_ip : 'N/A');
            $templateProcessor->setValue('temp_tm_lan_ipv6', ($request->lan_ip && $request->lan_ip != 'not available') ? $request->lan_ip : 'N/A');
            $templateProcessor->setValue('temp_lag', ($request->lag && $request->lag != 'not available') ? $request->lag : 'N/A');
            $templateProcessor->setValue('temp_access_detail_layer_1_remark', ($request->access_details_1_remark && $request->access_details_1_remark != 'not available') ? $request->access_details_1_remark : 'N/A');
            $templateProcessor->setValue('temp_access_detail_layer_2_remark', ($request->access_details_2_remark && $request->access_details_2_remark != 'not available') ? $request->access_details_2_remark : 'N/A');
            $templateProcessor->setValue('temp_authentication_key', ($request->authentication_key && $request->authentication_key != 'not available') ? $request->authentication_key : 'N/A');
            $templateProcessor->setValue('temp_blackhole', ($request->blackhole && $request->blackhole != 'not available') ? $request->blackhole : 'N/A');
            $templateProcessor->setValue('temp_antiddos_cleanpipe', ($request->antiddos_cleanpipe && $request->antiddos_cleanpipe != 'not available') ? $request->antiddos_cleanpipe : 'N/A');
            $templateProcessor->setValue('temp_bfd', ($request->bfd && $request->bfd != 'not available') ? $request->bfd : 'N/A');
            $templateProcessor->setValue('temp_special_routing', ($request->special_routing && $request->special_routing != 'not available') ? $request->special_routing : 'N/A');
            // $templateProcessor->setValue('temp_special_routing_remark', ($request->special_routing_remark && $request->special_routing_remark != 'not available') ? $request->special_routing_remark : 'N/A');
            $templateProcessor->setValue('temp_testing_date', date('Y-m-d'));

            // Handle the log interface status
            // $templateProcessor->setValue('temp_output_interface_status', 'N/A');
            // if ($request->log_interface_status) {
            //     $file_path = storage_path('app'.DIRECTORY_SEPARATOR .'public'.DIRECTORY_SEPARATOR.'service_testing'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.$request->service_id.DIRECTORY_SEPARATOR.'interface_status'.DIRECTORY_SEPARATOR.$request->log_interface_status);
            //     if (file_exists($file_path)) {
            //         // Generate the public URL to the file
            //         $public_url = url('storage/service_testing/log/'.$request->service_id.'/interface_status/'.$request->log_interface_status);
    
            //         // Set the placeholder with a hyperlink to the file
            //         $templateProcessor->setValue('temp_output_interface_status', '<a href="'.$public_url.'">Download Interface Status Log</a>');
            //     }
            // }


            // Interface Status
            // if ($request->log_interface_status) {
            //     // Read text file
            //     $text_log_interface_status = '';
            //     $file_path = storage_path('app'.DIRECTORY_SEPARATOR .'public'.DIRECTORY_SEPARATOR.'service_testing'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.$request->service_id.DIRECTORY_SEPARATOR.'interface_status'.DIRECTORY_SEPARATOR.$request->log_interface_status);
            //     if (file_exists($file_path)) {
            //         $text_log_interface_status = file_get_contents($file_path);
            //     }
            //     $templateProcessor->setValue('temp_output_interface_status', $text_log_interface_status);
            // } else {
            //     $templateProcessor->setValue('temp_output_interface_status', 'N/A');
            // }
            if ($request->log_interface_status) {
                // Read text file
                $text_log_interface_status = '';
                $file_path = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $request->service_id . DIRECTORY_SEPARATOR . 'interface_status' . DIRECTORY_SEPARATOR . $request->log_interface_status);
                if (file_exists($file_path)) {
                    $text_log_interface_status = file_get_contents($file_path);
                }
            
                // Preserve formatting
                $formatted_text = $text_log_interface_status;
            
                // Replace newlines with Word line breaks
                $formatted_text = str_replace("\n", '<w:br/>', $formatted_text);
            
                // Set the value in the template processor
                $templateProcessor->setValue('temp_output_interface_status', $formatted_text);
            } else {
                $templateProcessor->setValue('temp_output_interface_status', 'N/A');
            }

            // Ping Test
            if ($request->log_ping_test) {
                // Read text file
                $text_log_ping_test = '';
                $file_path = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $request->service_id . DIRECTORY_SEPARATOR . 'ping_test_pe_ce' . DIRECTORY_SEPARATOR . $request->log_ping_test);
                if (file_exists($file_path)) {
                    $text_log_ping_test = file_get_contents($file_path);
                }
            
                // Preserve formatting
                $formatted_text = $text_log_ping_test;
            
                // Replace newlines with Word line breaks
                $formatted_text = str_replace("\n", '<w:br/>', $formatted_text);
            
                // Set the value in the template processor
                $templateProcessor->setValue('temp_output_ping_test', $formatted_text);
            } else {
                $templateProcessor->setValue('temp_output_ping_test', 'N/A');
            }

            // Handle log_bgp_status
            if ($request->log_bgp_status) {
                // Read text file
                $text_log_bgp_status = '';
                $file_path = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $request->service_id . DIRECTORY_SEPARATOR . 'bgp_status' . DIRECTORY_SEPARATOR . $request->log_bgp_status);
                if (file_exists($file_path)) {
                    $text_log_bgp_status = file_get_contents($file_path);
                }

                // Preserve formatting
                $formatted_text_bgp_status = $text_log_bgp_status;

                // Replace newlines with Word line breaks
                $formatted_text_bgp_status = str_replace("\n", '<w:br/>', $formatted_text_bgp_status);

                // Set the value in the template processor
                $templateProcessor->setValue('temp_output_bgp_status', $formatted_text_bgp_status);
            } else {
                $templateProcessor->setValue('temp_output_bgp_status', 'N/A');
            }

            // Handle log_route_summary
            if ($request->log_route_summary) {
                // Read text file
                $text_log_route_summary = '';
                $file_path = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $request->service_id . DIRECTORY_SEPARATOR . 'route_summary' . DIRECTORY_SEPARATOR . $request->log_route_summary);
                if (file_exists($file_path)) {
                    $text_log_route_summary = file_get_contents($file_path);
                }

                // Preserve formatting
                $formatted_text_route_summary = $text_log_route_summary;

                // Replace newlines with Word line breaks
                $formatted_text_route_summary = str_replace("\n", '<w:br/>', $formatted_text_route_summary);

                // Set the value in the template processor
                $templateProcessor->setValue('temp_output_route_summary', $formatted_text_route_summary);
            } else {
                $templateProcessor->setValue('temp_output_route_summary', 'N/A');
            }

            // // Save the document to a temporary file
            // $fileName = $request->service_id . '_internal_handover_document_' . time() . '.docx';
            // // $tempFilePath = storage_path('app'.DIRECTORY_SEPARATOR.'public'.DIRECTORY_SEPARATOR.'document'.DIRECTORY_SEPARATOR.$fileName);
            // $tempFilePath = resource_path('views'.DIRECTORY_SEPARATOR.'self-serve'.DIRECTORY_SEPARATOR.'service-testing-handover'.DIRECTORY_SEPARATOR.'service-handover'.DIRECTORY_SEPARATOR.'template_handover_document'.DIRECTORY_SEPARATOR.'document'.DIRECTORY_SEPARATOR.$fileName);


            // Save the document to a temporary file
            $fileName = 'document_' . time() . '.docx';
            $tempFilePath = storage_path('app/public/' . $fileName);
            $templateProcessor->saveAs($tempFilePath);

            // Return the file as a download response
            return response()->download($tempFilePath)->deleteFileAfterSend(true);

        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }

    }


}