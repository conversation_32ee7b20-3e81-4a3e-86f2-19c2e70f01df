<?php

namespace App\Http\Controllers;

use App\Models\AuditTrail;
use Carbon\Carbon;
use Illuminate\Http\Request;

class AuditTrailController extends Controller
{

    public $dateTime;
    // format datetime
    public function reformatDateTime() {
        // Convert the default timestamp format to the desired format
        return Carbon::createFromFormat('Y-m-d H:i:s', $this->dateTime)->format('d-m-Y H:i:s');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('self-serve.audit-trail');

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // $actions = AuditTrail::with('user')->get();     // if send variable = all
        $actions = AuditTrail::with('user')->orderBy('created_at', 'desc')->take(5000)->get();

        // dd($users);

        if (sizeof($actions)===0) $data = array();     // check if no data return

        // initialize list numbner
        $no = 0;

        foreach ($actions as $action) {

            // increment no
            $no++;

            $this->dateTime = $action->created_at;
            $createdAt = $this->reformatDateTime();

            $data[] = array(
                'no' => $no,
                'id' => $action->id,
                'staff_id' => $action->user->staff_id,
                'last_created' => $createdAt,
                'action' => $action->action,
                'module' => $action->module
            );
        }

        return array('auditTrailData' => $data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
