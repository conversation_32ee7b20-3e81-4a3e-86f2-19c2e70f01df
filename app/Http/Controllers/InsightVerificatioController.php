<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http; // Missing import
use Illuminate\Support\Facades\Auth; // Missing import
use App\Models\AuditTrail; // Missing import
use Throwable;

class InsightVerificatioController extends Controller
{
    public function show(Request $request)
    {
        // validate input format
        $request->validate([
            'service_id' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('SELFSERVE_UNIS_API_ENDPOINT'), [
                "NIGRequest" => [
                    "Layer" => "BOTH",
                    "graniteid" => $request->service_id,
                    "Qscenario" => "POSITIVE",
                    "serviceType" => ""
                ]
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {
                // Get the JSON response body as an array
                $data = $response->json();

                // Check if UNIS API returns no data
                if (strpos($data['NIGResponse']['ErrorMesage'], 'no data found') !== false) throw new \Exception(__('No matching Service Id. in UNIS'));

                // initialize array value with not available
                $svcInfo = array(
                    'customer_name' => 'not available',
                    'vrf' => 'not available',
                    'network_id' => [],
                    'leg_id' => 'not available',
                    'product' => 'not available',
                    'qos' => 'not available',
                    'vpn' => 'not available',
                    'pe_ip' => 'not available',
                    'ce_ip' => 'not available',
                    'ce_ipv6' => 'not available',
                    'pe_ipv6' => 'not available',
                    'lan_ipv6' => 'not available',
                    'ce' => 'not available',
                    'ce_vendor' => 'not available',
                    'protocol' => 'not available',
                    'bandwidth' => 'not available',
                    'pe' => 'not available1',
                    'pe_layer2' => 'not available2',
                    'pe_vendor' => 'not available',
                    'npe' => 'not available',
                    'npe_vendor' => 'not available',
                    'epe_1' => 'not available',
                    'epe_2' => 'not available',
                    'npe_device_id' => 'not available',
                    'npe_downlink' => 'not available',
                    'npe_downlink_device_id' => 'not available',
                    'npe_downlink_interface' => 'not available',
                    'pe_device_id' => 'not available',
                    'npe_interface' => 'not available',
                    'epe_interface_1' => 'not available',
                    'epe_interface_2' => 'not available',
                    'pe_interface' => 'not available',
                    'epe_device_id_1' => 'not available',
                    'epe_device_id_2' => 'not available',
                    'epe_vendor_1' => 'not available',
                    'epe_vendor_2' => 'not available',
                    'ce_vendor' => 'not available',
                    'nid_upe' => 'not available',
                    'nid_upe_interface_ul_1' => 'not available',
                    'nid_upe_interface_dl' => 'not available',
                    'nid_upe_model' => 'not available',
                    'nid_upe_vendor' => 'not available',
                    'nid_upe_device_id' => 'not available',
                    'nid_upe_interface_ul_2' => 'not available',
                    'sla_slg' => 'not available',
                    'upe_type' => 'not available',
                    'import_map' => 'not available',  // set if available
                    'pe_import_map' => 'not available',  // set if available
                    'epe_ems_service_id' => 'not available',
                    'epe_vcid' => 'not available',
                    'ce_interface' => 'not available',
                    'L2_id' => 'not available',
                    'peer_as_number' => 'not available',
                    // 'pe_agg_interface' => 'not available',
                    'routing_protocol_mode' => 'not available',
                    'category' => 'not available',
                    'prefix_name_v4' => 'not available',
                    'prefix_name_v6' => 'not available',
                    'site_abr' => 'not available',
                );

                // initialize wc info array to determine metro-e type
                $L2wcInfo = array();
                $LanIP = array();

                // if value exist in API, replace the initial values
                $svcInfo['service_id'] = $request->service_id;

                // if service_id has 'PS' set serviceType as 'PRIMARY' else if service id has 'BS' set serviceType as 'Secondary'

                $serviceType = null;

                if (strpos($request->service_id, 'PS') === 0) {
                    $serviceType = 'PRIMARY';
                } else if (strpos($request->service_id, 'BS') === 0) {
                    $serviceType = 'SECONDARY';

                    foreach ($data['NIGResponse']['layer2'] as $info) {
                        // General Info
                        if ($info['name'] === 'Device Name' && $info['type'] === 'Layer2' && $info['field'] === 'ACTUAL AGG DOWNLINK PRIMARY') {
                            $serviceType = 'PRIMARY';
                            // dd($svcInfo);
                        }
                    }
                }


                // if layer 3 info exists
                if (isset($data['NIGResponse']['layer3'])) {

                    // massage L3 info
                    foreach ($data['NIGResponse']['layer3'] as $info) {
                        // General Info
                        // if ($info['name'] === 'ID' && $info['field'] === 'MPLS Network' && $info['group'] === $request->service_id) $svcInfo['network_id'] = $info['value'];
                        if ($info['name'] === 'Parent Services' && $info['field'] === 'Parent Services' && $info['group'] === $request->service_id && ctype_alnum($info['value'])) $svcInfo['network_id'] = $info['value'];
                        else if ($info['name'] === 'Name' && $info['field'] === 'Redundancy Group') $svcInfo['leg_id'] = $info['value'];
                        else if ($info['name'] === 'Category' && $info['field'] === 'Network') $svcInfo['product'] = $info['value'];
                        else if ($info['name'] === 'QoS Package' && $info['group'] === $request->service_id) $svcInfo['qos'] = $info['value'];
                        else if ($info['name'] === 'Name' && $info['field'] === 'MPLS Network' && $info['group'] === $request->service_id) $svcInfo['vpn'] = $info['value'];
                        else if ($info['name'] === 'VRF Name' && $info['field'] === 'MPLSEndPoint' && $info['group'] === $request->service_id) $svcInfo['vrf'] = $info['value'];
                        else if ($info['name'] === 'Routing Protocol' && $info['group'] === $request->service_id) $svcInfo['protocol'] = $info['value'];
                        else if ($info['name'] === 'Bandwidth' && $info['group'] === $request->service_id) $svcInfo['bandwidth'] = $info['value'];
                        else if ($info['name'] === 'Routing Protocol Mode' && $info['type'] === 'L3 Service' && $info['group'] === $request->service_id) $svcInfo['routing_protocol_mode'] = $info['value'];
                        else if ($info['name'] === 'Logical Site Name' && $info['type'] === 'L3 Service' && $info['group'] === $request->service_id) {
                            // Extract the value before the '-'
                            $svcInfo['site_abr'] = explode('-', $info['value'])[0];
                        }                                         
                        // PE
                        else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_ip'] = $info['value'];
                        else if ($info['name'] === 'Vendor' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_vendor'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'PE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $svcInfo['pe_ipv6'] = $info['value'];
                        else if ($info['name'] === 'Network IPv6' && $info['type'] === 'CE' && $info['field'] === 'Network Advertisement' && $info['group'] === $request->service_id) $svcInfo['lan_ipv6'] = $info['value'];
                        else if ($info['name'] === 'CIDR' && $info['group'] === $request->service_id) $cidr = $info['value'];
                        else if ($info['name'] === 'CIDR IPv6' && $info['group'] === $request->service_id) {
                            $cidr_ipv6 = $info['value'];
                            // dd($svcInfo);
                        }
                        else if ($info['name'] === 'Device Name' && $info['type'] === 'PE' && $info['group'] === $request->service_id) {
                            $svcInfo['pe'] = $info['value'];
                            // dd($svcInfo);
                        }
                        // else if ($info['name'] === 'Device Name' && $info['type'] === 'Layer2' && $info['group'] === $request->service_id) $svcInfo['pe'] = $info['value'];

                        else if ($info['name'] === 'Device IP' && $info['type'] === 'PE' && $info['group'] === $request->service_id) {
                            $svcInfo['pe_device_id'] = $info['value'];
                            // dd($svcInfo);
                        }
                        else if ($info['name'] === 'Interface Name' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_interface'] = $info['value'];
                        else if ($info['name'] === 'Remote AS Number' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['peer_as_number'] = $info['value'];
                        else if ($info['name'] === 'Import Map' && $info['type'] === 'PE' && $info['group'] === $request->service_id) {
                            // Store the full import map
                            $svcInfo['import_map'] = $info['value'];
                            
                            // Extract IPv4 prefix - look for v4_GOV_xxx_PRFX_IN or GOV_xxx_PRFX_IN (but not v6_GOV_xxx_PRFX_IN)
                            if (preg_match('/v4_GOV_[A-Za-z0-9_-]+?_PRFX_IN/', $info['value'], $matches)) {
                                $svcInfo['prefix_name_v4'] = $matches[0];
                            } else if (preg_match('/(?<!v6_)GOV_[A-Za-z0-9_-]+?_PRFX_IN/', $info['value'], $matches)) {
                                // Use negative lookbehind to exclude v6_GOV patterns
                                $svcInfo['prefix_name_v4'] = $matches[0];
                            }
                        }
                        else if ($info['name'] === 'Import Map IPv6' && $info['type'] === 'PE' && $info['group'] === $request->service_id) {
                            // Store the IPv6 import map
                            $svcInfo['pe_import_map'] = $info['value'];
                            
                            // Extract IPv6 prefix - look for v6_GOV_xxx_PRFX_IN
                            if (preg_match('/v6_GOV_[A-Za-z0-9_-]+?_PRFX_IN/', $info['value'], $matches)) {
                                $svcInfo['prefix_name_v6'] = $matches[0];
                            }
                        }

                        // else if ($info['name'] === 'Import Map' && $info['type'] === 'PE' && $info['group'] === $request->service_id) {
                        //     // Step 1: Split the value into an array
                        //     $valueArray = explode(',', $info['value']);

                        //                                 // Extract and store the complete GOV_xxxx_PRFX_IN pattern (keep _PRFX_IN)
                        //     if (preg_match('/GOV_1[A-Za-z0-9_-]+?_PRFX_IN/', $info['value'], $matches)) {
                        //         $svcInfo['prefix_name_v4'] = $matches[0];
                        //     } else if (preg_match('/GOV_[A-Za-z0-9_-]+?_PRFX_IN/', $info['value'], $matches)) {
                        //         $svcInfo['prefix_name_v4'] = $matches[0];
                        //     }

                        //     // Step 2: Filter for elements containing the keyword 'tmd' (case-insensitive)
                        //     $filteredElements = array_filter($valueArray, function($element) {
                        //         return stripos($element, 'tmd') !== false;
                        //     });

                        //     // Step 3: Convert the filtered elements back into a string
                        //     $svcInfo['pe_import_map'] = implode(',', $filteredElements);
                        // }

                        // CE
                        else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_ip'] = $info['value'];
                        else if ($info['name'] === 'Interface Name' && $info['type'] === 'CE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $svcInfo['ce_interface'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_ipv6'] = $info['value'];
                        else if ($info['name'] === 'Device Name' && $info['type'] === 'CE' && $info['field'] === 'Container' && $info['group'] === $request->service_id) $svcInfo['ce'] = $info['value'];
                        else if ($info['name'] === 'Device Vendor' && $info['type'] === 'CE' && $info['field'] === 'Container' && $info['group'] === $request->service_id) $svcInfo['ce_vendor'] = $info['value'];
                        else if ($info['name'] === 'Network' && $info['field'] === 'Network Advertisement' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $LanIP[] = $info['value'];
                    }

                    // Assign LAN IP to svcInfo array
                    $svcInfo['lan_ip'] = $LanIP;

                    // Add CIDR to IPv4 and IPv6
                    if ($svcInfo['pe_ip'] !== 'not available') $svcInfo['pe_ip'] .= '/' . $cidr;
                    if ($svcInfo['ce_ip'] !== 'not available') $svcInfo['ce_ip'] .= '/' . $cidr;
                    if ($svcInfo['pe_ipv6'] !== 'not available') $svcInfo['pe_ipv6'] .= '/' . $cidr_ipv6;
                    if ($svcInfo['ce_ipv6'] !== 'not available') $svcInfo['ce_ipv6'] .= '/' . $cidr_ipv6;

                }

                // if layer 2 info exists
                if (isset($data['NIGResponse']['layer2'])) {

                    // find L2 Service Id
                    foreach ($data['NIGResponse']['layer2'] as $info) {
                        // General Info
                        if ($info['name'] === 'Name' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) {
                            $svcInfo['L2_id'] = $info['value'];
                            // dd($svcInfo);
                        }
                        else if ($info['name'] === 'TECHNICAL SLA/SLG' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) $svcInfo['sla_slg'] = $info['value'];
                        else if ($info['name'] === 'UPE Type Physical Group' && $info['field'] === 'UPE' && $info['group'] === $request->service_id && strpos($info['value'], 'TYPE') !== false) $svcInfo['upe_type'] = $info['value'];
                        // For P2P/WSE/NBGH/ELAN
                        else if ($info['name'] === 'Category' && $info['field'] === 'Network' && $info['group'] === $request->service_id) {
                            if ($svcInfo['product'] === 'not available') {
                                $svcInfo['product'] = $info['type'];
                                $svcInfo['network_id'] = $info['value'];
                            }
                        }
                        // Technical SLA/SLG
                        else if ($info['name'] === 'TECHNICAL SLA/SLG' && $info['field'] === 'TM_SERVICE_INFO' && $info['group'] === $request->service_id) {
                            $svcInfo['sla_slg'] = $info['value'];
                        }
                        // UPE Type
                        // else if ($info['name'] === 'UPE TYPE' && $info['field'] === 'UPE Attributes' && $info['group'] === $request->service_id) {
                        //     $svcInfo['upe_type'] = $info['value'];
                        // }

                    }
                    // dd($svcInfo);

                    // If Product = 'Wholesale Ethernet'
                    if (strtolower($svcInfo['product']) === 'wholesale ethernet' || strtolower($svcInfo['product']) === 'metro e') {
                        $svcInfo['L2_id'] = 'not available'; // reassign L2 Service Id because it returns service id
                        foreach ($data['NIGResponse']['layer2'] as $info) {
                            // Leg Id
                            if ($info['name'] === 'REDUNDANCY GROUP ID' && $info['field'] === 'TM_SERVICE_INFO' && $info['group'] === $request->service_id) $svcInfo['leg_id'] = $info['value'];
                            // QOS
                            else if ($info['field'] === 'TM_COS' && $info['group'] === $request->service_id && $info['value']==='100') $svcInfo['qos'] = $info['name']." - ".$info['value'];
                            // Bandwidth
                            else if ($info['name'] === 'Bandwidth' && $info['group'] === $request->service_id && $info['field']==='L2 Service') $svcInfo['bandwidth'] = $info['value'];
                            // EPE 1
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_interface_1'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_device_id_1'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_vendor_1'] = $info['value'];
                            // NPE 1 (UPE directly connected to NPE)
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_interface_1'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_device_id_1'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_vendor_1'] = $info['value'];
                            // EPE 2
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_interface_2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_device_id_2'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_vendor_2'] = $info['value'];
                            // NPE 2 (UPE directly connected to NPE)
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_interface_2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_device_id_2'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_vendor_2'] = $info['value'];
                            // EMS Service Name
                            else if ($info['name'] === 'EMS SERVICE NAME' && $info['group'] === $request->service_id) $svcInfo['epe_ems_service_id'] = $info['value'];
                            // VCID
                            else if ($info['name'] === 'VCID' && $info['group'] === $request->service_id) $svcInfo['epe_vcid'] = $info['value'];
                            // UPE Type Physical Group
                            else if ($info['name'] === 'UPE Type Physical Group' && $info['group'] === $request->service_id) $svcInfo['upe_type'] = $info['value'];
                            // UPE
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe'] = $info['value'];
                            else if ($info['name'] === 'Model' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_model'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_device_id'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_vendor'] = $info['value'];
                            else if ($info['name'] === 'UPE SERVICE INSTANCE ID' && $info['group'] === $request->service_id && $info['field'] === 'UPE Attributes') $svcInfo['nid_upe_svc_instance_id'] = $info['value'];
                            else if ($info['name'] === 'Serial Number' && $info['group'] === $request->service_id && $info['field'] === 'UPE') $svcInfo['nid_upe_serial_no'] = $info['value'];
                            else if ($info['name'] === 'UPE TYPE' && $info['group'] === $request->service_id && $info['field'] === 'UPE Attributes') $svcInfo['nid_upe_type_non_physical'] = $info['value'];
                            // UPE INTERFACE
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE UPLINK 1') $svcInfo['nid_upe_interface_ul_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE UPLINK 2') $svcInfo['nid_upe_interface_ul_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_interface_dl'] = $info['value'];
                           
                        }

                    }
                    else {

                        // massage L2 info
                        foreach ($data['NIGResponse']['layer2'] as $info) {
                            // PE
                            if ($info['name'] === 'Device Name' && $info['field'] === 'ACTUAL AGG DOWNLINK '. $serviceType) {
                                $svcInfo['pe'] = $info['value'];
                                // dd($svcInfo);
                            }
                            // else if ($info['name'] === 'Vendor' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe_vendor'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe_vendor'] = $info['value'];
                            // else if ($info['name'] === 'Device IP' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) {
                            //     $svcInfo['pe_device_id'] = $info['value'];
                            //     // dd($svcInfo);
                            // }
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'ACTUAL AGG DOWNLINK '.$serviceType && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe_interface'] = $info['value'];
                            // if ($info['name'] === 'Device Name' && $info['field'] === 'ACTUAL AGG DOWNLINK'.$serviceType && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe_layer2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'ACTUAL AGG DOWNLINK '. $serviceType && $info['group'] === $svcInfo['L2_id']) {
                                $svcInfo['pe_device_id'] = $info['value'];
                                // dd($svcInfo);
                            }
                            // NPE
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id'])  $svcInfo['npe'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id'])  $svcInfo['npe_vendor'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_device_id'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_interface'] = $info['value'];
                            // else if ($info['name'] === 'Device IP' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) {
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'ACTUAL AGG DOWNLINK '. $serviceType && $info['group'] === $svcInfo['L2_id']) {

                                $svcInfo['npe_downlink_device_id'] = $info['value'];
                            //  dd($svcInfo);
                            }
                            // else if ($info['name'] === 'Device Name' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) {
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'ACTUAL AGG DOWNLINK' && $info['group'] === $svcInfo['L2_id']) {

                                $svcInfo['npe_downlink'] = $info['value'];
                                // dd($svcInfo);
                            }
                            // else if ($info['name'] === 'Interface Name' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) {
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'ACTUAL AGG DOWNLINK' && $info['group'] === $svcInfo['L2_id']) {

                                if ($svcInfo['npe_downlink_interface'] === 'not available') {
                                    $svcInfo['npe_downlink_interface'] = $info['value'];
                                }
                                // dd($svcInfo);
                            }
                            else if ($info['name'] === 'REDUNDANCY GROUP ID' && $info['field'] === 'L2 Service') {
                                // Convert to array if it's not already an array
                                if (!is_array($svcInfo['network_id'])) {
                                    $svcInfo['network_id'] = [];
                                }
                                
                                $groupValue = $info['group'];
                                
                                // Apply PS/BS logic - only keep the opposite prefix
                                if (strpos($request->service_id, 'PS') === 0) {
                                    // If service_id starts with PS, only keep BS values, discard PS values
                                    if (strpos($groupValue, 'BS') === 0) {
                                        // Keep BS value
                                        if (!in_array($groupValue, $svcInfo['network_id'])) {
                                            $svcInfo['network_id'][] = $groupValue;
                                        }
                                    }
                                    // Discard PS values (do nothing)
                                } else if (strpos($request->service_id, 'BS') === 0) {
                                    // If service_id starts with BS, only keep PS values, discard BS values
                                    if (strpos($groupValue, 'PS') === 0) {
                                        // Keep PS value
                                        if (!in_array($groupValue, $svcInfo['network_id'])) {
                                            $svcInfo['network_id'][] = $groupValue;
                                        }
                                    }
                                    // Discard BS values (do nothing)
                                } else {

                                    // For service_id that doesn2 start with PS or BS, keep all values
                                    if (!in_array($groupValue, $svcInfo['network_id'])) {
                                        $svcInfo['network_id'][] = $groupValue;
                                    }
                                }
                            }
                            // if ($info['name'] === 'Parent Services' && $info['field'] === 'Parent Services' && $info['group'] === $request->service_id && ctype_alnum($info['value'])) $svcInfo['network_id'] = $info['value'];


                            // for EPE
                            // EPE 1
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_1'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_device_id_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_interface_1'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_vendor_1'] = $info['value'];
                            // EPE 2
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_device_id_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_interface_2'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_vendor_2'] = $info['value'];                    

                            // for UPE/NID (1)
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 1' && $info['group'] === $request->service_id) {
                                if ($svcInfo['nid_upe_interface_ul_1'] == 'not available') {
                                    $svcInfo['nid_upe_interface_ul_1'] = $info['value'];
                                    continue;
                                }
                            }
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE DOWNLINK' && $info['group'] === $request->service_id) $svcInfo['nid_upe_interface_dl'] = $info['value'];
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe'] = $info['value'];
                            else if ($info['name'] === 'Model' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_model'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_vendor'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_device_id'] = $info['value'];
                            // UPE primary
                            if ($svcInfo['upe_type'] === 'TYPE 2' || $svcInfo['upe_type'] === 'TYPE 4') {
                                // if ($svcInfo['nid_upe_interface_ul_1'] != 'not available' && $svcInfo['nid_upe_interface_ul_2'] == 'not available') if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 2' && $info['group'] === $request->service_id) $svcInfo['nid_upe_interface_ul_2'] = $info['value'];
                                if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 2' && $info['group'] === $request->service_id) $svcInfo['nid_upe_interface_ul_2'] = $info['value'];
                            }
                            
                        }

                        // for AGG
                        // if (stripos($svcInfo['npe_downlink'], 'agg') !== false) {
                        //     // re-assign PE with AGG and NPE info
                        //     // NPE
                        //     $svcInfo['npe'] = 'not available';
                        //     $svcInfo['npe_vendor'] = 'not available';
                        //     $svcInfo['npe_interface'] = 'not available';
                        //     $svcInfo['npe_device_id'] = 'not available';
                        //     $svcInfo['L2_id'] = 'not available';
                        //     // PEy
                        //     $svcInfo['pe'] = $svcInfo['npe_downlink'];
                        //     // dd($svcInfo);
                        //     // $svcInfo['pe_device_id'] = $svcInfo['npe_downlink_device_id'];
                        //     // dd($svcInfo);
                        //     // $svcInfo['pe_interface'] = $svcInfo['npe_downlink_interface'];
                        //     // Assign PE Vendor based on AGG name
                        //     if (stripos($svcInfo['npe_downlink'], 'zt') !== false) $svcInfo['pe_vendor'] = 'ZTE';
                        //     else if (stripos($svcInfo['npe_downlink'], 'hw') !== false) $svcInfo['pe_vendor'] = 'Huawei';
                        //     else $svcInfo['pe_vendor'] = 'not available';
                        // }

                    }

                }

                // massaging vendor based on PE name
                if (isset($svcInfo['pe']) && $svcInfo['pe'] !== 'not available') {
                    // Check if the PE name contains 'ZTE'
                    if (stripos($svcInfo['pe'], 'ZT') !== false) {
                        $svcInfo['pe_vendor'] = 'ZTE';
                    }
                    // Check if the PE name contains 'Huawei'
                    else if (stripos($svcInfo['pe'], 'hw') !== false) {
                        $svcInfo['pe_vendor'] = 'Huawei';
                    }
                    // If neither, set to 'not available'
                    else {
                        $svcInfo['pe_vendor'] = 'not available';
                    }
                }

                // get customer name
                $this->service_id = $svcInfo['service_id'];
                $svcInfo['customer_name'] = $this->getCustomerName();

                // add success message
                $svcInfo['success'] = 'Successfully retrieved info from UNIS';

                // sort array alphabetically
                ksort($svcInfo);

                // dd($svcInfo);

                // testing massage data
                // dd($svcInfo);

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Searched service info for service id. (" . $request->service_id . ") from UNIS",
                    'user_id' => Auth::id(),
                    'module' => 'Insight Verification'
                ]);

                // if success, redirect with data
                return redirect()->back()->with('data', $svcInfo);

                // Return the data to the client
                // return response()->json([
                //     'status' => 'success',
                //     'message' => 'Matched Service Id.',
                //     'details' => $svcInfo
                // ]);
            } else {
                // Handle non-2xx status code responses
                return redirect()->back()->with('error', $response->body());
            }
        } catch (Throwable $error) {
            return redirect()->back()->with('error', $error->getMessage());
        }
    }

    private function getCustomerName()
    {
        // Implementation to fetch customer name based on service_id
        // Example implementation:
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->get(env('SELFSERVE_CUSTOMER_API_ENDPOINT') . '/' . $this->service_id);
        
            if ($response->successful()) {
                $data = $response->json();
                return $data['customer_name'] ?? 'not available';
            }
        
            return 'not available';
        } catch (Throwable $e) {
            return 'not available';
        }
    }

    // Store activity in Audit Trail
    public function neVerification(Request $request)
    {
        set_time_limit(600);
        AuditTrail::create([
            'action' => "Running insight verification for " . strtoupper($request->vendor) . " for service id. (" . $request->service_id . ") from PISA",
            'user_id' => Auth::id(),
            'module' => 'Insight Verification'
        ]);

        // Start with the required fields
        $payload = [
            'network_type' => 'pe',
            'vendor' => strtolower($request->vendor),
            'ne_ip' => $request->ne_ip,
            'interface_name' => $request->interface_name,
            'pe_wan_ip' => $request->pe_wan_ip,
            'ce_wan_ip' => $request->ce_wan_ip,
            'vrf_name' => $request->vrf_name,
            'service_id' => $request->service_id,
            'product' => $request->product,
            'nid_upe_name' => $request->nid_upe_name ?? 'string',
            'site_abr' => $request->site_abr ?? null,
            'vpn_name' => $request->vpn_name ?? null,
        ];
        
        // Add cos only if it's provided
        if ($request->cos) {
            $payload['cos'] = $request->cos;
        }
        
        // Add IPv6 addresses ONLY if both are provided and not empty
        if ($request->pev6_wan_ip && $request->cev6_wan_ip) {
            $payload['pev6_wan_ip'] = $request->pev6_wan_ip;
            $payload['cev6_wan_ip'] = $request->cev6_wan_ip;
        }
        
        // Add prefix fields if provided - with debugging
        if ($request->prefix_name_v4) {
            $payload['prefix_name_v4'] = $request->prefix_name_v4;
            \Log::info('Added prefix_name_v4 to payload:', ['value' => $request->prefix_name_v4]);
        } else {
            \Log::warning('prefix_name_v4 not provided in request');
        }
        
        if ($request->prefix_name_v6) {
            $payload['prefix_name_v6'] = $request->prefix_name_v6;
            \Log::info('Added prefix_name_v6 to payload:', ['value' => $request->prefix_name_v6]);
        } else {
            \Log::warning('prefix_name_v6 not provided in request');
        }
        
        // Log the entire request for debugging
        \Log::info('Full request data received:', $request->all());
        
        // Log the payload for debugging
        \Log::info('PISA API Final Payload:', $payload);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->timeout(600)
        ->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/v1/self_serve/insight_verification', $payload);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // Get the JSON response
            $responseData = $response->json();
            
            // Process the response data
            return response()->json([
                'status' => 'success',
                // Don't try to access ['output'] if it might not exist
                // Just return the entire JSON response as the output
                'output' => $responseData, 
                // Extract the data specifically for the frontend
                'data' => $responseData['data'] ?? $responseData
            ]);
        } else {
            // Send the error message
            return response()->json([
                'status' => 'failed',
                'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
                'data' => $response->json() ?? []
            ]);
        }
    }

    /**
     * Health check for PISA API.
     */
    public function healthCheck(Request $request)
    {
        set_time_limit(600); // Allow up to 10 minutes for this request

        // Build payload from request, fallback to empty string if not present
        $payload = [
            "vendor" => strtolower($request->input('vendor', '')),
            "agg_hostname" => $request->input('agg_hostname', ''),
            "agg_loopback_ip" => $request->input('agg_loopback_ip', ''),
            "vrf" => $request->input('vrf', ''),
            "interface" => $request->input('interface', ''),
            "pe_ipv4" => $request->input('pe_ipv4', ''),
            "ce_ipv4" => $request->input('ce_ipv4', ''),
            "nid_vendor" => $request->input('nid_vendor', ''),
            "nid_model" => $request->input('nid_model', ''),
            "nid_device_id" => $request->input('nid_device_id', ''),
            "nid_interface_1" => $request->input('nid_interface_1', ''),
        ];
        // Optional fields
        if ($request->filled('pe_ipv6')) {
            $payload['pe_ipv6'] = $request->input('pe_ipv6');
        }
        if ($request->filled('ce_ipv6')) {
            $payload['ce_ipv6'] = $request->input('ce_ipv6');
        }
        if ($request->filled('nid_interface_2')) {
            $payload['nid_interface_2'] = $request->input('nid_interface_2');
        }

        $response = \Illuminate\Support\Facades\Http::withHeaders([
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])
        ->timeout(600) // Increase timeout to 600 seconds (10 minutes)
        ->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/v1/self_serve/health_check', $payload);

        if ($response->successful()) {
            $apiData = $response->json();
            // Flatten if nested
            if (isset($apiData['data']['data_pe']) || isset($apiData['data']['data_nid'])) {
                // If the API response is nested, return only the inner data
                return response()->json([
                    'status' => 'success',
                    'data' => $apiData['data']
                ]);
            } else {
                // Otherwise, return as is
                return response()->json([
                    'status' => 'success',
                    'data' => $apiData
                ]);
            }
        } else {
            return response()->json([
                'status' => 'failed',
                'error' => $response->body(),
                'http_status' => $response->status()
            ], $response->status());
        }
    }

    public function logApiError(Request $request)
    {
        \Log::error('API Error from Frontend', [
            'error_type' => $request->error_type,
            'service_id' => $request->service_id,
            'error_status' => $request->error_status,
            'error_message' => $request->error_message,
            'http_status' => $request->http_status,
            'http_status_text' => $request->http_status_text,
            'response_body' => $request->response_body,
            'request_payload' => $request->request_payload,
            'user_agent' => $request->user_agent,
            'timestamp' => $request->timestamp,
            'ip_address' => $request->ip(),
            'url' => $request->headers->get('referer')
        ]);

        return response()->json(['status' => 'logged']);
    }
}
