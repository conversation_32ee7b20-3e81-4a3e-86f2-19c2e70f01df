<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\NotifyNewRegisteredUser;
use App\Models\AuditTrail;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;


class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;
    public $userEmail;
    public $staffId;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Get the login username changed to Staff Id. to be used by the controller.
     *
     * @return string
     */
    public function username()
    {
        return 'staff_id';
    }

    /**
     * Attempt to log the user into the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        return $this->guard()->attempt(
            $this->credentials($request), $request->filled('remember')
        );
    }

    /**
     * Get the needed authorization credentials from the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    // protected function credentials(Request $request)
    // {
    //     // Only use the username for authentication, not the password
    //     return $request->only($this->username());
    // }

    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request) {

        // validate input format
        $request->validate([
            $this->username() => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
            'password' => 'required|string',
        ]);

        try {

            // assign staff id
            $this->staffId = strtoupper($request->{$this->username()});

            // test cache
            Cache::put('username_'.$this->staffId, $this->staffId, 60);

            // Point the selector to user in DB
            $user = User::where('staff_id', $this->staffId);

            // Logic for staff id is not exist in DB
            if ($user->doesntExist()) {

                // If no data returned from ERA API
                if (empty($this->getEraStaffInfo())) { throw new \Exception(__('invalid user')); }

                // Get Staff details from ERA API
                $staffInfo = $this->getEraStaffInfo();
                $contactInfo = $this->getEraContactInfo();
                $this->userEmail = strtolower($contactInfo[0]['Email']);

                // Create a new user in DB
                $newUser = User::create([
                    'staff_id' => $this->staffId,
                    'full_name' => $staffInfo[0]['Name'],
                    'email' => $this->userEmail,
                    'lob' => $this->getEraStaffExtraInfo()[0]['LOB_Desc'],
                    'role' => 'user', 
                    'status' => 'inactive',
                ]);

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Registered as a new user (".$this->staffId.")",
                    'user_id' => $newUser->id,
                    'module' => 'Not Available'
                ]);

                // send email notification to system admin for approval
                $this->notifyApprover();

                throw new \Exception(__('new user'));

            }

            // Logic for staff id exist in DB but with status inactive
            elseif ($user->first()->status==='inactive') {
                throw new \Exception(__('inactive user'));
            }

        }

        catch (\Illuminate\Database\QueryException $error) {
            // Handle database connection error
            throw ValidationException::withMessages([$this->username() => "Database connection error (".$error->getMessage()."). Please contact the system admin."]);
        }
        
        catch (\Exception $error) {
            if ($error->getMessage()==='invalid user') { $errorMessage = __('Invalid Staff Id. Please retry.'); }
            elseif ($error->getMessage()==='new user') { $errorMessage = __('Successfully registered new user, pending system admin approval.'); }
            elseif ($error->getMessage()==='inactive user') { $errorMessage = __('Inactive user, pending system admin approval.'); }
            else {
                // for non-specific error message
                $errorMessage = __('An unexpected error occurred ('.$error->getMessage().'). Please contact the system admin.');
                // $errorMessage = __('An unexpected error occurred. Please contact the system admin.');
                // $errorMessage = __($error->getMessage());       // troubleshooting purpose
            }

            throw ValidationException::withMessages([$this->username() => $errorMessage]);

        }
    }

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request){
        
        // Upon successfully login update last login time
        User::where('staff_id', $this->staffId)->update([
            'last_login' => Carbon::now()->toDateTimeString(),
        ]);

        // Cache User Image
        $cachedData = Cache::get('profile_picture_'.$this->staffId);

        // if not key in cache data
        if (!$cachedData) {
            Cache::put('profile_picture_'.$this->staffId, $this->getEraProfileImage(), 120);
        }

        
    }

    // Get ERA API access token
    public function getEraAccessToken() {

        $response = Http::withHeaders([
            'Authorization' => 'Basic '.env('ERA_BASIC_TOKEN'),
        ])->asForm()->post(env('ERA_API_ENDPOINT2').'token', [
            'grant_type' => 'client_credentials',
        ]);
    
        if ($response->successful()) {
            return $response->json()['access_token'];
        } 
        else {
            throw new \Exception(__('ERA API: Unexpected HTTP status ('.$response->status().', '.$response->reason().')'));
        }
    }

    // Get ERA API contact info
    public function getEraContactInfo() {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->getEraAccessToken(),
        ])->get(env('ERA_API_ENDPOINT1').'/profile/contact/'.$this->staffId);

        return $response->json();

    }

    // Get ERA API staff info
    public function getEraStaffInfo() {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->getEraAccessToken(),
        ])->get(env('ERA_API_ENDPOINT1').'/profile/info/'.$this->staffId);

        return $response->json();
    }

    // Get ERA API staff profile picture
    public function getEraProfileImage() {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->getEraAccessToken(),
        ])->get(env('ERA_API_ENDPOINT1').'/profile/image/'.$this->staffId);

        return $response->body();
    }

    // Get ERA API extra info
    public function getEraStaffExtraInfo() {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->getEraAccessToken(),
        ])->get(env('ERA_API_ENDPOINT1').'/profile/extra/info/'.$this->staffId);

        return $response->json();
    }

    // send email notify system admin for approval
    public function notifyApprover() {
        // get all admin email
        $adminEmail = User::where('role', 'admin')->pluck('email')->toArray();

        Mail::to($adminEmail)
        ->cc($this->userEmail)
        ->send(new NotifyNewRegisteredUser($this->staffId));
    }

    /**
     * Log the user out of the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $this->guard()->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        if ($response = $this->loggedOut($request)) {
            return $response;
        }

        return $request->wantsJson()
            ? new JsonResponse([], 204)
            : redirect('/self-serve');
    }
}
