<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Validation\ValidationException;


class AuthController extends Controller
{
    public function __construct(){
        $this->middleware('auth:sanctum', ['except' => ['login', 'validateAccessLdap']]);
    }
    // Login
    public function login(Request $request){
        $request->validate([
            'staff_id' => 'required|alpha_num',
            'password' => 'required'
        ]);

        // find matched user
        $user = User::where('staff_id', $request->staff_id)->first();
        if (!$user) throw ValidationException::withMessages([
            'staff_id' => ['The provided crendentials are incorrect.'],
        ]);

        // Authenticate with LDAP
        $authenticated = $this->validateAccessLdap($request->staff_id, $request->password);

        // check if LDAP API returns error message
        if ($authenticated['output'] !== 'Successfully authenticated') throw ValidationException::withMessages([
            'staff_id' => ['The provided crendentials are incorrect.'],
        ]);

        // generate and return token
        $token = $user->createToken('api-token')->plainTextToken;
        return response()->json([
            'token' => $token
        ]);

    }

    // Logout
    public function logout(Request $request){
        $request->user()->tokens()->delete();
        return response()->json(['message' => 'Logged out successfully.'], 200);
    }


    // validate LDAP using PISA Workflow API
    public function validateAccessLdap($username, $password){        
        $response = Http::withHeaders([
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/ldap/v2/authenticate', [
            'username' => $username,
            'password' => $password
        ]);

        return $response->json();

        // Failed authentication
        // {
        //     "success": false,
        //     "message": "User not authenticated",
        //     "status": 401,
        //     "data": {}
        // }

        // Failed authentication
        // {
        //      "success": true,
        //      "message": "User authenticated",
        //      "status": 200,
        //      "data": {}
        // }


    }


}
