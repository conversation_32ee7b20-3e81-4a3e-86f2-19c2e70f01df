<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UnisData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class PremiumDashboardController extends Controller
{
    // Add Authentication
    public function __construct() {
        $this->middleware('auth:sanctum');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $list_of_circuit = DB::table('temp_ce_ip')
        ->join('unis_data', 'temp_ce_ip.serviceId', '=', 'unis_data.ServiceId')
        ->distinct()  // Ensure unique rows
        ->select('temp_ce_ip.*', 'unis_data.*')
        ->get();

        // declare a var of CE IP List
        $ceIpList = array();

        foreach ($list_of_circuit as $element) {

            // check if ce wan ip available in list
            if (in_array($element->ceWanIp, $ceIpList)) continue;

            $title = $element->CustAbbr . ',' . $element->RedundancyId . ',' . $element->ServiceId;
            
            if (isset($element->Pe)) {
                $title .= ',' . $element->Pe;
            }
            
            if (isset($element->PeInterface)) {
                $title .= ',' . $element->PeInterface;
            }

            // add ce wan ip into the list
            $ceIpList[] = $element->ceWanIp;
            
            $data[] = [
                'service_id' => $element->ServiceId,
                'menu' => $element->ServiceId . '_' . $element->CustAbbr,
                'title' => $title,
                'host' => $element->ceWanIp
            ];
        }

        return response()->json($data, 200);

    }

    public function getListCircuits(Request $request)
    {
        // Validate request parameters
        $request->validate([
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'pe_node' => 'nullable|string'
        ]);
    
        // Get distinct PE nodes for pagination, excluding IBSE
        $peNodes = DB::table('unis_data')
            ->distinct()
            ->select('Pe', 'PeDeviceId')
            ->whereNotNull('Pe')
            ->whereNotNull('PeDeviceId')
            ->where('Pe', 'not like', 'IBSE%')
            ->orderBy('Pe')
            ->get();
    
        // Use a smaller per_page value to process fewer nodes at a time
        $perPage = $request->input('per_page', 1);
        $currentPage = $request->input('page', 1);
        $offset = ($currentPage - 1) * $perPage;
    
        // Get current PE node info
        $currentPeInfo = $peNodes[$offset] ?? null;
    
        if ($currentPeInfo) {
            // Get circuits for current PE node
            $circuits = DB::table('unis_data')
                ->where('Pe', $currentPeInfo->Pe)
                ->get();
    
            // Get total number of circuits for current PE node only
            $totalCircuits = DB::table('unis_data')
                ->where('Pe', $currentPeInfo->Pe)
                ->count();
    
            $totalPages = ceil($peNodes->count() / $perPage);
    
            return response()->json([
                'status' => 'success',
                'total_rows' => $totalCircuits,  // Total circuits for current PE node
                'page' => $currentPage,
                'last_page' => $totalPages,
                'pe' => $currentPeInfo->Pe,
                'PeDeviceIp' => $currentPeInfo->PeDeviceId,
                'circuits' => $circuits,
                // 'per_page' => $perPage
            ]);
        }
    
        return response()->json([
            'status' => 'error',
            'message' => 'No PE nodes found'
        ], 404);
    }

    public function updateBgpUptimeStatus(Request $request)
    {
        $validated = $request->validate([
            'pe_node' => 'required|string',
            'circuits' => 'required|array',
            'circuits.*.PeDeviceIp' => 'required|string',
            'circuits.*.BGPStatus' => 'required|string',
            'circuits.*.BGPUptime' => 'required|string'
        ]);
    
        try {
            $updatedCount = 0;
            foreach ($validated['circuits'] as $circuit) {
                $affected = DB::table('bgp_uptime_status')
                    ->where('PeNode', $validated['pe_node'])
                    ->where('PeDeviceIp', $circuit['PeDeviceIp'])
                    ->update([
                        'BGPStatus' => $circuit['BGPStatus'],
                        'BGPUptime' => $circuit['BGPUptime'],
                        'updated_at' => now()
                    ]);
                
                $updatedCount += $affected;
                \Illuminate\Support\Facades\Log::info('Update attempt:', [
                  'PeDeviceIp' => $circuit['PeDeviceIp'],
                  'affected_rows' => $affected
              ]);
            }
    
            return response()->json([
                'status' => 'success',
                'message' => 'Update process completed',
                'rows_updated' => $updatedCount,
                'pe_node' => $validated['pe_node']
            ], 200);
    
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function insertBgpUptimeStatus(Request $request)
    {
        $validated = $request->validate([
            'pe_node' => 'required|string',
            'circuits' => 'required|array',
            'circuits.*.CustAbbr' => 'required|string',
            'circuits.*.ServiceType' => 'required|string',
            'circuits.*.Product' => 'required|string',
            'circuits.*.LegId' => 'nullable|string',         // Changed to nullable
            'circuits.*.ServiceId' => 'required|string',
            'circuits.*.PeNode' => 'nullable|string',        // Changed to nullable
            'circuits.*.PeDeviceIp' => 'nullable|string',    // Changed to nullable
            'circuits.*.CeWanIp' => 'required|string',
            'circuits.*.BGPStatus' => 'required|string',
            'circuits.*.BGPUptime' => 'required|string'
        ]);
    
        try {
            DB::beginTransaction();
            
            $insertedCount = 0;
            foreach ($validated['circuits'] as $circuit) {
                DB::table('bgp_uptime_status')->insert([
                    'CustAbbr' => $circuit['CustAbbr'],
                    'ServiceType' => $circuit['ServiceType'],
                    'Product' => $circuit['Product'],
                    'LegId' => $circuit['LegId'] ?: null,           // Convert empty string to null
                    'ServiceId' => $circuit['ServiceId'],
                    'PeNode' => $validated['pe_node'],              // Use the main pe_node value
                    'PeDeviceIp' => $circuit['PeDeviceIp'] ?: null, // Convert empty string to null
                    'CeWanIp' => $circuit['CeWanIp'],
                    'BGPStatus' => $circuit['BGPStatus'],
                    'BGPUptime' => $circuit['BGPUptime'],
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                $insertedCount++;
            }
    
            DB::commit();
    
            return response()->json([
                'status' => 'success',
                'message' => 'Insert process completed',
                'rows_inserted' => $insertedCount,
                'pe_node' => $validated['pe_node']
            ], 201);
    
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'status' => 'failed',
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()  // Added for better debugging
            ], 400);
        }
    }


    
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
