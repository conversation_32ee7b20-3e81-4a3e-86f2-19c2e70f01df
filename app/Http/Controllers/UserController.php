<?php

namespace App\Http\Controllers;

use App\Mail\NotifyNewApprovedUser;
use App\Models\AuditTrail;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;


use Illuminate\Http\Request;

class UserController extends Controller
{
    public $dateTime;
    public $staffId;
    public $userEmail;
    public $fullName;

    // format datetime
    public function reformatDateTime() {
        // Convert the default timestamp format to the desired format
        return Carbon::createFromFormat('Y-m-d H:i:s', $this->dateTime)->format('d-m-Y H:i:s');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('self-serve.users.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('self-serve.users.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        // validate data send
        $validatedData = $request->validate([
            'staff_id' => 'required|alpha_num',
            'role' => 'required',
            'status' => 'required'
        ]);

        // Assign a global variable
        $this->staffId = strtoupper($request->staff_id);

        // Point the selector to user in DB
        $user = User::where('staff_id', $this->staffId);

        // Logic for staff id is not exist in DB
        if ($user->doesntExist()) {

            // If no data returned from ERA API
            if (empty($this->getEraStaffInfo())) {
                // redirect with error message
                return redirect()->back()->with('error', 'Invalid Staff Id., please try again.');
            }

            // Create a new user in DB
            User::create([
                'staff_id' => $this->staffId,
                'full_name' => $this->getEraStaffInfo()[0]['Name'],
                'email' => strtolower($this->getEraContactInfo()[0]['Email']),
                'role' => $request->role, 
                'status' => $request->status,
            ]);

            // Store activity in Audit Trail
            AuditTrail::create([
                'action' => "Created a new user (".$this->staffId.")",
                'user_id' => Auth::id(),
                'module' => 'Manage Users'
            ]);

            // redirect with successful message
            return redirect()->route('user.index')->with('success', "User (".strtoupper($this->staffId).") details is registered successfully.");

        }

        else {
            // redirect with error message
            return redirect()->back()->with('error', 'User is already registered.');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if ($id === "all") $users = User::all();     // if send variable = all
        else $users = User::where('staff_id', strtoupper($id))->get();          // else if send staff 

        // dd($users);

        if (sizeof($users)===0) $data = array();     // check if no data return

        foreach ($users as $user) {

            $this->dateTime = $user->created_at;
            $updatedAt = $this->reformatDateTime();
            $this->dateTime = $user->updated_at;
            $createdAt = $this->reformatDateTime();

            $action = "<td class='text-left'>
                <a type='button' href='" . route('user.edit', ['user' => $user->staff_id]) . "' class='btn btn-soft-info btn-icon-square-sm ms-1'><i class='mdi mdi-pencil-outline'></i></a>
                <button name='btn-delete-user' type='button' class='btn btn-soft-pink btn-icon-square-sm ms-1'><i class='mdi mdi-delete-outline'></i></button>
            </td>";

            // check if status == 'inactive', display approve button
            if ($user->status === 'inactive') $action .= "<button name='btn-approve-user' type='button' class='btn btn-soft-warning btn-icon-square-sm ms-1 btn-approve'><i class='mdi mdi-check-outline'></i><i class='mdi mdi-spin mdi-loading visually-hidden'></i></button>";

            $data[] = array(
                'id' => $user->id,
                'staff_id' => $user->staff_id,
                'full_name' => $user->full_name,
                'email' => $user->email,
                'lob' => $user->lob,
                'role' => $user->role,
                'status' => $user->status==='active'? "<span class='badge rounded-pill bg-success'>".$user->status."</span>":"<span class='badge rounded-pill bg-warning'>".$user->status."</span>",
                'last_login' => $user->last_login,
                'last_updated' => $updatedAt,
                'last_created' => $createdAt,
                'action' => $action
            );
        }

        return array('userData' => $data);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id){

        $user = User::where('staff_id', strtoupper($id))->first(); 

        return view('self-serve.users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        // validate data send
        $validatedData = $request->validate([
            'role' => 'required',
            'status' => 'required',
            'lob' => 'required',
        ]);

        try {
            // Update the user record with the validated data
            User::where('staff_id', strtoupper($id))->update($validatedData);

            // Store activity in Audit Trail
            AuditTrail::create([
                'action' => "Updated user (".$id.") details",
                'user_id' => Auth::id(),
                'module' => 'Manage Users'
            ]);

            // redirect with successful message
            return redirect()->route('user.index')->with('success', "User (".strtoupper($id).") details is updated successfully.");
        } 
        
        catch (\Exception $error) {
            // Handle global exception here
            $errorMessage = 'An error has occurred while updating the user '.strtoupper($id).' details: ' . $error->getMessage();
            // redirect with error message
            return redirect()->back()->with('error', $errorMessage);
        }

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Delete the user by ID
        User::where('staff_id', $id)->delete();

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "Deleted user (".$id.")",
            'user_id' => Auth::id(),
            'module' => 'Manage Users'
        ]);


        // redirect
        return redirect()->route('user.index')->with('success', "Successfully deleted user ($id) access.");
    }

    // Approve user function
    public function approve($id){

        // assign 
        $this->staffId = $id;

        // Point the selector to user in DB
        $user = User::where('staff_id', $this->staffId);

        // assign
        $this->userEmail = $user->first()->email;
        $this->fullName = $user->first()->full_name;

        // update user status to active
        User::where('staff_id', $id)->update(['status' => 'active']);

        // send email notification to user for approved access
        $this->notifyUser();

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "Approved new user (".$this->staffId.")",
            'user_id' => Auth::id(),
            'module' => 'Manage Users'
        ]);

        
        return redirect()->route('user.index')->with('success', "Successfully approved user ($id) access.");

    }


    // Get ERA API access token
    public function getEraAccessToken() {

        $response = Http::withHeaders([
            'Authorization' => 'Basic '.env('ERA_BASIC_TOKEN'),
        ])->asForm()->post(env('ERA_API_ENDPOINT2').'token', [
            'grant_type' => 'client_credentials',
        ]);
    
        if ($response->successful()) {
            return $response->json()['access_token'];
        } 
        else {
            throw new \Exception(__('ERA API: Unexpected HTTP status ('.$response->status().', '.$response->reason().')'));
        }
    }

    // Get ERA API contact info
    public function getEraContactInfo() {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->getEraAccessToken(),
        ])->get(env('ERA_API_ENDPOINT1').'/profile/contact/'.$this->staffId);

        return $response->json();

    }

    // Get ERA API staff info
    public function getEraStaffInfo() {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->getEraAccessToken(),
        ])->get(env('ERA_API_ENDPOINT1').'/profile/info/'.$this->staffId);

        return $response->json();
    }

    // send email notify system admin for approval
    public function notifyUser() {
        // get all admin email
        $adminEmail = User::where('role', 'admin')->pluck('email')->toArray();

        Mail::to($this->userEmail)
        ->cc($adminEmail)
        ->send(new NotifyNewApprovedUser($this->staffId, $this->fullName));
    }
}
