<?php

namespace App\Http\Controllers;

use App\Models\AuditTrail;
use App\Models\UpeNidPortType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;


class UpeAvailabilityController extends Controller
{
    // default class - check authentication
    public function index(){
        return view('self-serve.upe-availability');
    }

    // Get Info from UNIS and process the info
    public function get_info_unis(Request $request){

        try { 
            $rules = [
                'upe_nid_name' => 'required|max:30|regex:/^[A-Za-z0-9-]+$/',  // only allow alphanumeric, symbols are not allowed
            ];

            // Define custom error messages
            $messages = [
                'upe_nid_name.required' => 'The upe/nid name is required',
                'upe_nid_name.regex' => "The upe/nid name should only contain alphanumeric characters and '-'",
                'upe_nid_name.max' => "The upe/nid name must not be greater than 30 characters",
            ];

            // Validate the request using the Validator facade
            $validator = Validator::make($request->all(), $rules, $messages);

            // Check if validation fails
            if ($validator->fails()) {
                // Return JSON response with validation errors
                return response()->json([
                    'status' => 'error',
                    'errors' => $validator->errors(),
                ], 422); // Unprocessable Entity
            }

            $validatedData = $validator->validated(); // Assign variable to validated data

            // Get UPE info from PISA API
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post(env('PISA_WORKFLOW_API_ENDPOINT').'/data/v1/get_upe_nid', [
                'nid_upe_name' => [$validatedData['upe_nid_name']],
            ]);

            // Check the response status
            if ($response->successful()) {
                $data = $response->json(); // Get the response data
                // Handle successful response
            } else {
                // Return JSON response with validation errors
                return response()->json([
                    'status' => 'error',
                    'errors' => "An error has occurred: " . $response->json(),
                ], $response->status()); // Unprocessable Entity
            }

            // Check if no info returned from UNIS
            if ($data['output'] === 'no data found in db') throw new \Exception("No data returned from UNIS for " . $validatedData['upe_nid_name']);

            ////// Process the response //////
            // Get port uplink details from self-serve DB
            $port_ulink_details = UpeNidPortType::where('model', $data['output'][0]['UPE_MODEL'])
                ->where('port_communication', 'uplink')
                ->get()
                ->toArray();

            // Massage port uplink details for multi vendors
            if ($data['output'][0]['UPE_VENDOR'] == 'HUAWEI' || $data['output'][0]['UPE_VENDOR'] == 'ALCATEL-LUCENT') {

                $port_range_details = [];

                foreach ($port_ulink_details as $item) {
                    // Match ports with either two or three numeric parts (e.g., 0/1 or 1/0/1)
                    preg_match('/^(\D+)\s(\d+\/\d+)(\/\d+)?$/', $item['port_range_start'], $start_matches);
                    preg_match('/^(\D+)\s(\d+\/\d+)(\/\d+)?$/', $item['port_range_end'], $end_matches);

                    // Ensure matches are valid and the base port name (e.g., "FastEthernet") is the same
                    if ($start_matches && $end_matches && $start_matches[1] === $end_matches[1]) {
                        $port_name = $start_matches[1];  // E.g., "FastEthernet" or "GigabitEthernet"
                        $prefix = $start_matches[2];     // E.g., "0/1" or "1/0"

                        // Check if there’s a third part in the port format (e.g., "/1" or "/4")
                        $start = isset($start_matches[3]) ? (int) ltrim($start_matches[3], '/') : null;
                        $end = isset($end_matches[3]) ? (int) ltrim($end_matches[3], '/') : null;

                        $range = [];
                        if ($start !== null && $end !== null) {
                            // Three-part format: Generate range for the last part
                            for ($i = $start; $i <= $end; $i++) {
                                $range[] = "{$port_name} {$prefix}/{$i}";
                            }
                        } else {
                            // Two-part format: No need for a third part in the range
                            $start_number = (int) substr(strrchr($prefix, '/'), 1);  // Get the starting number
                            $end_number = (int) substr(strrchr($end_matches[2], '/'), 1); // Get the ending number
                            $main_prefix = strstr($prefix, '/', true); // E.g., "0" from "0/1"

                            for ($i = $start_number; $i <= $end_number; $i++) {
                                $range[] = "{$port_name} {$main_prefix}/{$i}";
                            }
                        }

                        $port_range_details[] = [
                            // "port_name" => $port_name,
                            "range" => $range,
                            "port_type" => $item['port_type'],
                        ];
                    }
                }
                
            }

            else if ($data['output'][0]['UPE_VENDOR'] == 'ZTE') {

                $port_range_details = [];

                foreach ($port_ulink_details as $item) {

                    preg_match('/^(gei_|xgei_)(\d+\/\d+)$/', $item['port_range_start'], $start_matches);
                    preg_match('/^(gei_|xgei_)(\d+\/\d+)$/', $item['port_range_end'], $end_matches);

                    // Ensure matches are valid and the base port name (e.g., "FastEthernet") is the same
                    if ($start_matches && $end_matches && $start_matches[1] === $end_matches[1]) {

                        $port_name = $start_matches[1]; // "gei_"
                        $prefix = $start_matches[2];     // E.g., "0/1" or "1/0"

                        $start_number = (int) ltrim(strrchr($start_matches[2], '/'), '/'); // Start range number (e.g., 1 from "1/1")
                        $end_number = (int) ltrim(strrchr($end_matches[2], '/'), '/');     // End range number (e.g., 4 from "1/4")
                        $main_prefix = strstr($prefix, '/', true); // E.g., "0" from "0/1"


                        $range = [];
                        for ($i = $start_number; $i <= $end_number; $i++) {
                            $range[] = "{$port_name}{$main_prefix}/{$i}";
                        }

                        $port_range_details[] = [
                            // "port_name" => $port_name,
                            "range" => $range,
                            "port_type" => $item['port_type'],
                        ];

                    }
                }
            }

            else if ($data['output'][0]['UPE_VENDOR'] == 'HFR') {

                $port_range_details = [];

                foreach ($port_ulink_details as $item) {
                    if(
                        preg_match('/^(eth-|GigabitEthernet |FastEthernet )(\d+\/\d+)$/', $item['port_range_start'], $start_matches) &&
                        preg_match('/^(eth-|GigabitEthernet |FastEthernet )(\d+\/\d+)$/', $item['port_range_end'], $end_matches) &&
                        $start_matches[1] === $end_matches[1]
                    ){
                        $port_name = $start_matches[1]; // "eth-"
                        $start_number = (int) ltrim(strrchr($start_matches[2], '/'), '/'); // Start range number (e.g., 1 from "1/1")
                        $end_number = (int) ltrim(strrchr($end_matches[2], '/'), '/');     // End range number (e.g., 4 from "1/4")

                        $range = [];
                        for ($i = $start_number; $i <= $end_number; $i++) {
                            $range[] = "{$port_name}1/{$i}";
                        }

                        $port_range_details[] = [
                            "port_name" => $port_name,
                            "range" => $range,
                            "port_type" => $item['port_type'],
                        ];
                        
                    }
                    else if (preg_match('/^(\d+\/\d+)$/', $item['port_range_start']) && preg_match('/^(\d+\/\d+)$/', $item['port_range_end'])) {

                        $port_name = ''; // No specific prefix for `1/1` format
                        $start_number = (int) ltrim(strrchr($item['port_range_start'], '/'), '/');
                        $end_number = (int) ltrim(strrchr($item['port_range_end'], '/'), '/');

                        $range = [];
                        for ($i = $start_number; $i <= $end_number; $i++) {
                            $range[] = "1/{$i}";
                        }

                        $port_range_details[] = [
                            // "port_name" => $port_name,
                            "range" => $range,
                            "port_type" => $item['port_type'],
                        ];
                    }
                }

            }

            else if ($data['output'][0]['UPE_VENDOR'] == 'RAISECOM') {

                $port_range_details = [];

                foreach ($port_ulink_details as $item) {

                    if (preg_match('/^NNI(\d+)$/', $item['port_range_start'], $start_matches) && preg_match('/^NNI(\d+)$/', $item['port_range_end'], $end_matches)) {

                        $port_name = $start_matches[1];

                        // Capture the start and end UNI numbers
                        $start_num = (int) $start_matches[1];
                        $end_num = (int) $end_matches[1];

                        // Generate the range dynamically
                        $range = [];
                        for ($i = $start_num; $i <= $end_num; $i++) {
                            $range[] = "NNI{$i}";
                        }

                        $port_range_details[] = [
                            // "port_name" => $port_name,
                            "range" => $range,
                            "port_type" => $item['port_type'],
                        ];
                    }
                    // Check for GE or TGE format like GE1/1/1 or TGE1/2/3
                    if (preg_match('/^(TGE|GE)(\d+)\/(\d+)\/(\d+)$/', $item['port_range_start'], $start_matches) && preg_match('/^(GE|TGE)(\d+)\/(\d+)\/(\d+)$/', $item['port_range_end'], $end_matches)) {

                        $port_prefix = $start_matches[1]; // "GE" or "TGE"
                        $start_part1 = (int) $start_matches[2]; // First number after GE/TGE
                        $start_part2 = (int) $start_matches[3]; // Second number
                        $start_part3 = (int) $start_matches[4]; // Third number

                        $end_part1 = (int) $end_matches[2];
                        $end_part2 = (int) $end_matches[3];
                        $end_part3 = (int) $end_matches[4];

                        // Generate range for 3-part ports (GE or TGE with 3 numbers)
                        $range = [];
                        for ($i = $start_part3; $i <= $end_part3; $i++) {
                            $range[] = "{$port_prefix}{$start_part1}/{$start_part2}/{$i}";
                        }

                        $port_range_details[] = [
                            // "port_name" => $port_prefix,
                            "range" => $range,
                            "port_type" => $item['port_type'],
                        ];

                    }

                }

            }

            // For main data
            // $output = [
            //     'equipment_id' => $data['output'][0]['EQUIPMENT_ID'],
            //     'upe_service_instance_id' => $data['output'][0]['UPE_SERVICES_INSTANCE_ID'],
            //     'upe_status' => $data['output'][0]['UPE_STATUS'],
            //     'upe_model' => $data['output'][0]['UPE_MODEL'],
            //     'upe_type' => $data['output'][0]['UPE_TYPE'],
            //     'upe_subtype' => $data['output'][0]['UPE_NID_TYPE'],
            // ];

            // Intialize output
            $output = [];

            // For UPE/NID and port uplink data
            foreach ($data['output'] as $port) {

                if ($port['EQUIPMENT_ID'] !== null && !isset($output['equipment_id'])) $output['equipment_id'] = $port['EQUIPMENT_ID'];
                if ($port['UPE_SERVICES_INSTANCE_ID'] !== null && !isset($output['upe_service_instance_id'])) $output['upe_service_instance_id'] = $port['UPE_SERVICES_INSTANCE_ID'];
                if ($port['UPE_STATUS'] !== null && !isset($output['upe_status'])) $output['upe_status'] = $port['UPE_STATUS'];
                if ($port['UPE_MODEL'] !== null && !isset($output['upe_model'])) $output['upe_model'] = $port['UPE_MODEL'];
                if ($port['UPE_TYPE'] !== null && !isset($output['upe_type'])) $output['upe_type'] = $port['UPE_TYPE'];
                if ($port['UPE_NID_TYPE'] !== null && !isset($output['upe_subtype'])) $output['upe_subtype'] = $port['UPE_NID_TYPE'];

                // For EGU
                if (!(
                    stripos($request->upe_nid_name, 'eh') === 0 || 
                    stripos($request->upe_nid_name, 'ea') === 0 || 
                    stripos($request->upe_nid_name, 'nh') === 0 || 
                    stripos($request->upe_nid_name, 'na') === 0
                )) {
                    // For non-EGU and model 7250 SAS-E
                    if ($output['upe_model'] == '7250 SAS-E') {
                        if (!isset($output['me_path_primary']) && $port['ROLE'] == 'PRIMARY') {
                            $output['me_path_primary'] = $port['PATH_NAME'] . "-PRIMARY";
                        }

                        if (!isset($output['me_path_secondary']) && $port['ROLE'] == 'SECONDARY') {
                            $output['me_path_secondary'] = $port['PATH_NAME'] . "-SECONDARY";
                        }
                    }
                    else {
                        foreach ($port_range_details as $port_range) {
                            if (in_array($port['PORT_ACCESS_ID'], $port_range['range'], true) && ($port['ROLE'] == 'PRIMARY' || $port['ROLE'] == 'SECONDARY')) {

                                if (!isset($output['me_path_primary'])) {
                                    $output['me_path_primary'] = ($port['ROLE']!==null) ? $port['PATH_NAME'] . "-" . $port['ROLE'] : $port['PATH_NAME'];
                                }
                                else {
                                    $output['me_path_secondary'] = ($port['ROLE']!==null) ? $port['PATH_NAME'] . "-" . $port['ROLE'] : $port['PATH_NAME'];
                                }

                            }
                        }
                    }
                }
                else {
                    if (!isset($output['me_path_primary']) && $port['ROLE'] == 'PRIMARY') {
                        $output['me_path_primary'] = $port['PATH_NAME'] . "-PRIMARY";
                    }

                    if (!isset($output['me_path_secondary']) && $port['ROLE'] == 'SECONDARY') {
                        $output['me_path_secondary'] = $port['PATH_NAME'] . "-SECONDARY";
                    }
                }
            }

            // Store activity in Audit Trail
            AuditTrail::create([
                'action' => "Searched details of NID/UPE (".$request->upe_nid_name.")",
                'user_id' => Auth::id(),
                'module' => 'Upe Availability'
            ]);

            // Return JSON response
            return response()->json([
                'status' => 'success',
                'data' => $output,
                'ports_uplink_range' => $port_range_details
            ]); // Unprocessable Entity

        } catch (\Exception $e) { 
            // Return JSON response with validation errors
            return response()->json([
                'status' => 'error',
                'errors' => "An error has occurred: " . $e->getMessage(),
                'line' => $e->getLine(),
            ], 500); // Unprocessable Entity
        }
    }

    // Get Port Downlink from UNIS and process the info
    public function get_port_downlink_unis(Request $request){

        try { 

            // Check if upe_nid_name is empty, return no data
            if (!isset($request->upe_nid_name)) return array('downlinkPortData' => []);

            // Get UPE info from PISA API
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post(env('PISA_WORKFLOW_API_ENDPOINT').'/data/v1/get_upe_nid', [
                'nid_upe_name' => [$request->upe_nid_name],
            ]);

            // Check the response status
            if ($response->successful()) {
                $data = $response->json(); // Get the response data
                // Handle successful response
            } else {
                // Return JSON response with validation errors
                return response()->json([
                    'status' => 'error',
                    'errors' => "An error has occurred: " . $response->json(),
                ], $response->status()); // Unprocessable Entity
            }

            // Check if UPE Status not 'In Service', return no data
            if ($data['output'][0]['UPE_STATUS'] != 'In Service') return array('downlinkPortData' => []);

            /////// Evaluate the response
            // Check if ne is not EGU
            if (!(
                stripos($request->upe_nid_name, 'eh') === 0 || 
                stripos($request->upe_nid_name, 'ea') === 0 || 
                stripos($request->upe_nid_name, 'nh') === 0 || 
                stripos($request->upe_nid_name, 'na') === 0
            )) {

                // Get port downlink details from self-serve DB
                $port_dlink_details = UpeNidPortType::where('model', $data['output'][0]['UPE_MODEL'])
                    ->where('port_communication', 'downlink')
                    ->get()
                    ->toArray();

                // Massage port downlink details for multi vendors
                if ($data['output'][0]['UPE_VENDOR'] == 'HUAWEI' || $data['output'][0]['UPE_VENDOR'] == 'ALCATEL-LUCENT') {

                    $port_range_details = [];

                    foreach ($port_dlink_details as $item) {
                        // Match ports with either two or three numeric parts (e.g., 0/1 or 1/0/1)
                        preg_match('/^(\D+)\s(\d+\/\d+)(\/\d+)?$/', $item['port_range_start'], $start_matches);
                        preg_match('/^(\D+)\s(\d+\/\d+)(\/\d+)?$/', $item['port_range_end'], $end_matches);

                        // Ensure matches are valid and the base port name (e.g., "FastEthernet") is the same
                        if ($start_matches && $end_matches && $start_matches[1] === $end_matches[1]) {
                            $port_name = $start_matches[1];  // E.g., "FastEthernet" or "GigabitEthernet"
                            $prefix = $start_matches[2];     // E.g., "0/1" or "1/0"

                            // Check if there’s a third part in the port format (e.g., "/1" or "/4")
                            $start = isset($start_matches[3]) ? (int) ltrim($start_matches[3], '/') : null;
                            $end = isset($end_matches[3]) ? (int) ltrim($end_matches[3], '/') : null;

                            $range = [];
                            if ($start !== null && $end !== null) {
                                // Three-part format: Generate range for the last part
                                for ($i = $start; $i <= $end; $i++) {
                                    $range[] = "{$port_name} {$prefix}/{$i}";
                                }
                            } else {
                                // Two-part format: No need for a third part in the range
                                $start_number = (int) substr(strrchr($prefix, '/'), 1);  // Get the starting number
                                $end_number = (int) substr(strrchr($end_matches[2], '/'), 1); // Get the ending number
                                $main_prefix = strstr($prefix, '/', true); // E.g., "0" from "0/1"

                                for ($i = $start_number; $i <= $end_number; $i++) {
                                    $range[] = "{$port_name} {$main_prefix}/{$i}";
                                }
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                        }
                    }
                    
                }

                else if ($data['output'][0]['UPE_VENDOR'] == 'ZTE') {

                    $port_range_details = [];

                    foreach ($port_dlink_details as $item) {

                        preg_match('/^(gei_|xgei_)(\d+\/\d+)$/', $item['port_range_start'], $start_matches);
                        preg_match('/^(gei_|xgei_)(\d+\/\d+)$/', $item['port_range_end'], $end_matches);

                        // Ensure matches are valid and the base port name (e.g., "FastEthernet") is the same
                        if ($start_matches && $end_matches && $start_matches[1] === $end_matches[1]) {

                            $port_name = $start_matches[1]; // "gei_"
                            $prefix = $start_matches[2];     // E.g., "0/1" or "1/0"

                            $start_number = (int) ltrim(strrchr($start_matches[2], '/'), '/'); // Start range number (e.g., 1 from "1/1")
                            $end_number = (int) ltrim(strrchr($end_matches[2], '/'), '/');     // End range number (e.g., 4 from "1/4")
                            $main_prefix = strstr($prefix, '/', true); // E.g., "0" from "0/1"

                            $range = [];
                            for ($i = $start_number; $i <= $end_number; $i++) {
                                $range[] = "{$port_name}{$main_prefix}/{$i}";
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];

                        }
                    }
                }

                else if ($data['output'][0]['UPE_VENDOR'] == 'HFR') {

                    $port_range_details = [];

                    foreach ($port_dlink_details as $item) {
                        if(
                            preg_match('/^(eth-|GigabitEthernet |FastEthernet )(\d+\/\d+)$/', $item['port_range_start'], $start_matches) &&
                            preg_match('/^(eth-|GigabitEthernet |FastEthernet )(\d+\/\d+)$/', $item['port_range_end'], $end_matches) &&
                            $start_matches[1] === $end_matches[1]
                        ){
                            $port_name = $start_matches[1]; // "eth-"
                            $start_number = (int) ltrim(strrchr($start_matches[2], '/'), '/'); // Start range number (e.g., 1 from "1/1")
                            $end_number = (int) ltrim(strrchr($end_matches[2], '/'), '/');     // End range number (e.g., 4 from "1/4")

                            $range = [];
                            for ($i = $start_number; $i <= $end_number; $i++) {
                                $range[] = "{$port_name}1/{$i}";
                            }

                            $port_range_details[] = [
                                "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                            
                        }
                        else if (preg_match('/^(\d+\/\d+)$/', $item['port_range_start']) && preg_match('/^(\d+\/\d+)$/', $item['port_range_end'])) {

                                $port_name = ''; // No specific prefix for `1/1` format
                                $start_number = (int) ltrim(strrchr($item['port_range_start'], '/'), '/');
                                $end_number = (int) ltrim(strrchr($item['port_range_end'], '/'), '/');

                                $range = [];
                                for ($i = $start_number; $i <= $end_number; $i++) {
                                    $range[] = "1/{$i}";
                                }

                                $port_range_details[] = [
                                    // "port_name" => $port_name,
                                    "range" => $range,
                                    "port_type" => $item['port_type'],
                                ];
                            }
                    }

                }

                else if ($data['output'][0]['UPE_VENDOR'] == 'RAISECOM') {

                    $port_range_details = [];

                    foreach ($port_dlink_details as $item) {

                        if (preg_match('/^UNI(\d+)$/', $item['port_range_start'], $start_matches) && preg_match('/^UNI(\d+)$/', $item['port_range_end'], $end_matches)) {

                            $port_name = $start_matches[1];

                            // Capture the start and end UNI numbers
                            $start_num = (int) $start_matches[1];
                            $end_num = (int) $end_matches[1];

                            // Generate the range dynamically
                            $range = [];
                            for ($i = $start_num; $i <= $end_num; $i++) {
                                $range[] = "UNI{$i}";
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                        }
                        // Check for GE or TGE format like GE1/1/1 or TGE1/2/3
                        if (preg_match('/^(TGE|GE)(\d+)\/(\d+)\/(\d+)$/', $item['port_range_start'], $start_matches) && preg_match('/^(GE|TGE)(\d+)\/(\d+)\/(\d+)$/', $item['port_range_end'], $end_matches)) {

                            $port_prefix = $start_matches[1]; // "GE" or "TGE"
                            $start_part1 = (int) $start_matches[2]; // First number after GE/TGE
                            $start_part2 = (int) $start_matches[3]; // Second number
                            $start_part3 = (int) $start_matches[4]; // Third number

                            $end_part1 = (int) $end_matches[2];
                            $end_part2 = (int) $end_matches[3];
                            $end_part3 = (int) $end_matches[4];

                            // Generate range for 3-part ports (GE or TGE with 3 numbers)
                            $range = [];
                            for ($i = $start_part3; $i <= $end_part3; $i++) {
                                $range[] = "{$port_prefix}{$start_part1}/{$start_part2}/{$i}";
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_prefix,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];

                        }

                    }

                }

                // Evaluate each port conditions
                
                $downlinkPorts = [];

                foreach ($data['output'] as $port) {
                    foreach ($port_range_details as $port_range) {
                        if (in_array($port['PORT_ACCESS_ID'], $port_range['range'], true) && $port['PORT_STATUS'] == 'Available') {
                            $downlinkPorts[] = [
                                "port" => $port['PORT_ACCESS_ID'],
                                "status" => $port['PORT_STATUS'],
                                "bw" => $port['BANDWIDTH'],
                                "connector" => $port['CONNECTOR_TYPE'],
                                "type" => (($port['PORT_ACCESS_ID'] == 'Port 1/3/1' || $port['PORT_ACCESS_ID'] == 'Port 1/3/2' || $port['PORT_ACCESS_ID'] == 'Port 1/6/1' || $port['PORT_ACCESS_ID'] == 'Port 1/6/2') && $port['UPE_MODEL'] == '7250 SAS-E') ? $port_range['port_type']. ' <br /> *If Uplink = Port 1/3/1 or 1/3/2, Downlink = Port 1/6/1 or 1/6/2 <br /> *If Uplink = Port 1/6/1 or 1/6/2, Downlink = Port 1/3/1 or 1/3/2' : $port_range['port_type'],
                                "physical_port" => $port['UPE_PORTPHYSICALBUNDLE'],
                            ];
                        }
                    }
                }
            }
            else {
                // logic for EGU
                $downlinkPorts = [];

                foreach ($data['output'] as $port) {
                        if ($port['PORT_STATUS'] == 'Available') {
                        $downlinkPorts[] = [
                            "port" => $port['PORT_ACCESS_ID'],
                            "status" => $port['PORT_STATUS'],
                            "bw" => $port['BANDWIDTH'],
                            "connector" => $port['CONNECTOR_TYPE'],
                            "type" => '',
                            "physical_port" => $port['UPE_PORTPHYSICALBUNDLE'],
                        ];
                    }
                }

            }

            // Return final output
            return array('downlinkPortData' => $downlinkPorts);

            // Testing purpose
            return response()->json([
                'status' => 'success',
                'data_port_range' => $port_range_details,
                'data_sql' => $port_dlink_details,
                'downlinkPorts' => $downlinkPorts
            ]);


        } catch (\Exception $e) { 
            // Return JSON response with validation errors
            return response()->json([
                'status' => 'error',
                'errors' => "An error has occurred: " . $e->getMessage(),
                'line' => $e->getLine(),
            ], 500); // Unprocessable Entity
        }
    }

    // Get Port Uplink from UNIS and process the info
    public function get_port_uplink_unis(Request $request){

        try {
            // Check if upe_nid_name is empty, return no data
            if (!isset($request->upe_nid_name)) {
                return $request->link_type == 'primary' 
                ? ['uplinkPrimaryPortData' => []] 
                : ['uplinkSecondaryPortData' => []];
            }

            // Check if upe_nid_name is not empty but path_name is empty
            if (isset($request->upe_nid_name) && (!isset($request->path_name) || $request->path_name == 'not available')) {
                return $request->link_type == 'primary' 
                ? ['uplinkPrimaryPortData' => []] 
                : ['uplinkSecondaryPortData' => []];
            }

             // Get UPE info from PISA API
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])->post(env('PISA_WORKFLOW_API_ENDPOINT').'/data/v1/get_upe_nid', [
                'nid_upe_name' => [$request->upe_nid_name],
            ]);

            // Check the response status
            if ($response->successful()) {
                $data = $response->json(); // Get the response data
                // Handle successful response
            } else {
                // Return JSON response with validation errors
                return response()->json([
                    'status' => 'error',
                    'errors' => "An error has occurred: " . $response->json(),
                ], $response->status()); // Unprocessable Entity
            }

            // Check if UPE Status not 'In Service', return no data
            if ($data['output'][0]['UPE_STATUS'] != 'In Service') {
                return $request->link_type == 'primary' 
                ? ['uplinkPrimaryPortData' => []] 
                : ['uplinkSecondaryPortData' => []];
            };

            /////// Evaluate the response
            // Check if ne is not EGU
            if (!(
                stripos($request->upe_nid_name, 'eh') === 0 || 
                stripos($request->upe_nid_name, 'ea') === 0 || 
                stripos($request->upe_nid_name, 'nh') === 0 || 
                stripos($request->upe_nid_name, 'na') === 0
            )) {

                // Get port uplink details from self-serve DB
                $port_ulink_details = UpeNidPortType::where('model', $data['output'][0]['UPE_MODEL'])
                    ->where('port_communication', 'uplink')
                    ->get()
                    ->toArray();

                // Massage port uplink details for multi vendors
                if ($data['output'][0]['UPE_VENDOR'] == 'HUAWEI' || $data['output'][0]['UPE_VENDOR'] == 'ALCATEL-LUCENT') {

                    $port_range_details = [];

                    foreach ($port_ulink_details as $item) {
                        // Match ports with either two or three numeric parts (e.g., 0/1 or 1/0/1)
                        preg_match('/^(\D+)\s(\d+\/\d+)(\/\d+)?$/', $item['port_range_start'], $start_matches);
                        preg_match('/^(\D+)\s(\d+\/\d+)(\/\d+)?$/', $item['port_range_end'], $end_matches);

                        // Ensure matches are valid and the base port name (e.g., "FastEthernet") is the same
                        if ($start_matches && $end_matches && $start_matches[1] === $end_matches[1]) {
                            $port_name = $start_matches[1];  // E.g., "FastEthernet" or "GigabitEthernet"
                            $prefix = $start_matches[2];     // E.g., "0/1" or "1/0"

                            // Check if there’s a third part in the port format (e.g., "/1" or "/4")
                            $start = isset($start_matches[3]) ? (int) ltrim($start_matches[3], '/') : null;
                            $end = isset($end_matches[3]) ? (int) ltrim($end_matches[3], '/') : null;

                            $range = [];
                            if ($start !== null && $end !== null) {
                                // Three-part format: Generate range for the last part
                                for ($i = $start; $i <= $end; $i++) {
                                    $range[] = "{$port_name} {$prefix}/{$i}";
                                }
                            } else {
                                // Two-part format: No need for a third part in the range
                                $start_number = (int) substr(strrchr($prefix, '/'), 1);  // Get the starting number
                                $end_number = (int) substr(strrchr($end_matches[2], '/'), 1); // Get the ending number
                                $main_prefix = strstr($prefix, '/', true); // E.g., "0" from "0/1"

                                for ($i = $start_number; $i <= $end_number; $i++) {
                                    $range[] = "{$port_name} {$main_prefix}/{$i}";
                                }
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                        }
                    }
                    
                }

                else if ($data['output'][0]['UPE_VENDOR'] == 'ZTE') {

                    $port_range_details = [];

                    foreach ($port_ulink_details as $item) {

                        preg_match('/^(gei_|xgei_)(\d+\/\d+)$/', $item['port_range_start'], $start_matches);
                        preg_match('/^(gei_|xgei_)(\d+\/\d+)$/', $item['port_range_end'], $end_matches);

                        // Ensure matches are valid and the base port name (e.g., "FastEthernet") is the same
                        if ($start_matches && $end_matches && $start_matches[1] === $end_matches[1]) {

                            $port_name = $start_matches[1]; // "gei_"
                            $prefix = $start_matches[2];     // E.g., "0/1" or "1/0"

                            $start_number = (int) ltrim(strrchr($start_matches[2], '/'), '/'); // Start range number (e.g., 1 from "1/1")
                            $end_number = (int) ltrim(strrchr($end_matches[2], '/'), '/');     // End range number (e.g., 4 from "1/4")
                            $main_prefix = strstr($prefix, '/', true); // E.g., "0" from "0/1"

                            $range = [];
                            for ($i = $start_number; $i <= $end_number; $i++) {
                                $range[] = "{$port_name}{$main_prefix}/{$i}";
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];

                        }
                    }
                }

                else if ($data['output'][0]['UPE_VENDOR'] == 'HFR') {

                    $port_range_details = [];

                    foreach ($port_ulink_details as $item) {
                        if(
                            preg_match('/^(eth-|GigabitEthernet |FastEthernet )(\d+\/\d+)$/', $item['port_range_start'], $start_matches) &&
                            preg_match('/^(eth-|GigabitEthernet |FastEthernet )(\d+\/\d+)$/', $item['port_range_end'], $end_matches) &&
                            $start_matches[1] === $end_matches[1]
                        ){
                            $port_name = $start_matches[1]; // "eth-"
                            $start_number = (int) ltrim(strrchr($start_matches[2], '/'), '/'); // Start range number (e.g., 1 from "1/1")
                            $end_number = (int) ltrim(strrchr($end_matches[2], '/'), '/');     // End range number (e.g., 4 from "1/4")

                            $range = [];
                            for ($i = $start_number; $i <= $end_number; $i++) {
                                $range[] = "{$port_name}1/{$i}";
                            }

                            $port_range_details[] = [
                                "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                            
                        }
                        else if (preg_match('/^(\d+\/\d+)$/', $item['port_range_start']) && preg_match('/^(\d+\/\d+)$/', $item['port_range_end'])) {

                            $port_name = ''; // No specific prefix for `1/1` format
                            $start_number = (int) ltrim(strrchr($item['port_range_start'], '/'), '/');
                            $end_number = (int) ltrim(strrchr($item['port_range_end'], '/'), '/');

                            $range = [];
                            for ($i = $start_number; $i <= $end_number; $i++) {
                                $range[] = "1/{$i}";
                            }

                            $port_range_details[] = [
                                "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                        }
                    }

                }

                else if ($data['output'][0]['UPE_VENDOR'] == 'RAISECOM') {

                    $port_range_details = [];

                    foreach ($port_ulink_details as $item) {

                        if (preg_match('/^NNI(\d+)$/', $item['port_range_start'], $start_matches) && preg_match('/^NNI(\d+)$/', $item['port_range_end'], $end_matches)) {

                            $port_name = $start_matches[1];

                            // Capture the start and end UNI numbers
                            $start_num = (int) $start_matches[1];
                            $end_num = (int) $end_matches[1];

                            // Generate the range dynamically
                            $range = [];
                            for ($i = $start_num; $i <= $end_num; $i++) {
                                $range[] = "NNI{$i}";
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_name,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];
                        }
                        // Check for GE or TGE format like GE1/1/1 or TGE1/2/3
                        if (preg_match('/^(TGE|GE)(\d+)\/(\d+)\/(\d+)$/', $item['port_range_start'], $start_matches) && preg_match('/^(GE|TGE)(\d+)\/(\d+)\/(\d+)$/', $item['port_range_end'], $end_matches)) {

                            $port_prefix = $start_matches[1]; // "GE" or "TGE"
                            $start_part1 = (int) $start_matches[2]; // First number after GE/TGE
                            $start_part2 = (int) $start_matches[3]; // Second number
                            $start_part3 = (int) $start_matches[4]; // Third number

                            $end_part1 = (int) $end_matches[2];
                            $end_part2 = (int) $end_matches[3];
                            $end_part3 = (int) $end_matches[4];

                            // Generate range for 3-part ports (GE or TGE with 3 numbers)
                            $range = [];
                            for ($i = $start_part3; $i <= $end_part3; $i++) {
                                $range[] = "{$port_prefix}{$start_part1}/{$start_part2}/{$i}";
                            }

                            $port_range_details[] = [
                                // "port_name" => $port_prefix,
                                "range" => $range,
                                "port_type" => $item['port_type'],
                            ];

                        }

                    }

                }

                // Evaluate each port conditions
                
                // For non-EGU and model 7250 SAS-E
                if ($request->model != '7250 SAS-E') {
                    // process data for uplink port
                    foreach ($data['output'] as $port) {
                        foreach ($port_range_details as $port_range) {
                            // $port_name = (strpos($port['PORT_ACCESS_ID'], '.') !== false) ? explode('.', $port['PORT_ACCESS_ID'])[0] : $port['PORT_ACCESS_ID'];
                            // if (in_array($port_name, $port_range['range'], true) && stripos($request->path_name, $port['PATH_NAME']) !== false) {
                            if (in_array($port['PORT_ACCESS_ID'], $port_range['range'], true) && stripos($request->path_name, $port['PATH_NAME']) !== false) {
                                $uplink_port = [
                                    "upe_nid_uplink" => $port['EQUIPMENT_ID'],
                                    "uplink_port" => $port['PORT_ACCESS_ID'],
                                    "epe_npe_name" => $port['EPE_NPE_NAME'],
                                    "epe_npe_port" => $port['EPE_NPE_PORT_ACCESS_ID'],
                                    "me_path_bw" => $port['PATH_BANDWIDTH'],
                                    "total_bw_mbps" => round($port['TOTAL_BPS_AVAIL']/1000000, 0),
                                    "used_bw_mbps" => round($port['BPS_ALLOCATED']/1000000, 0),
                                    "utilization" => $port['UTILIZATION'],
                                    "over_subscription" => ($port['OVER_SUBSCRIBE']==null) ? 0 : $port['OVER_SUBSCRIBE'],
                                    "available_bw" => '',
                                ];
                            }
                        }
                    }
                }
                else {
                    // Get Port for Path Name and Port Uplink Details
                    foreach ($data['output'] as $port) {
                        if (stripos($request->path_name, $port['PATH_NAME']) === 0) {
                            $uplink_port = [
                                "upe_nid_uplink" => $port['EQUIPMENT_ID'],
                                "uplink_port" => $port['PORT_ACCESS_ID'],
                                "epe_npe_name" => $port['EPE_NPE_NAME'],
                                "epe_npe_port" => $port['EPE_NPE_PORT_ACCESS_ID'],
                                "me_path_bw" => $port['PATH_BANDWIDTH'],
                                "total_bw_mbps" => round($port['TOTAL_BPS_AVAIL']/1000000, 0),
                                "used_bw_mbps" => round($port['BPS_ALLOCATED']/1000000, 0),
                                "utilization" => $port['UTILIZATION'],
                                "over_subscription" => ($port['OVER_SUBSCRIBE']==null) ? 0 : $port['OVER_SUBSCRIBE'],
                                "available_bw" => '',
                            ];
                            break;
                        }
                    }
                }

                // process data for uplink port vlan
                foreach ($data['output'] as $port) {
                    // if (strpos($port['PORT_ACCESS_ID'], '.') !== false && stripos($port['PORT_ACCESS_ID'], $uplink_port['uplink_port']) !== false) {
                    if (strpos($port['PORT_ACCESS_ID'], '.') !== false && explode('.', $port['PORT_ACCESS_ID'])[0] == $uplink_port['uplink_port']) {
                        $uplink_port['path_next_path'] = (!isset($uplink_port['path_next_path'])) ? $port['PATH_NAME'] : $uplink_port['path_next_path'] . '<br>' .$port['PATH_NAME'];
                        $uplink_port['ps_bs_name'] = (!isset($uplink_port['ps_bs_name'])) ? $port['PS_BS_NAME'] : $uplink_port['ps_bs_name'] . '<br>' .$port['PS_BS_NAME'];
                        $uplink_port['path_bw'] = (!isset($uplink_port['path_bw'])) ? $port['PATH_BANDWIDTH'] : $uplink_port['path_bw'] . '<br>' .$port['PATH_BANDWIDTH'];
                        $uplink_port['vlan'] = (!isset($uplink_port['vlan'])) ? $port['CHANNEL_NAME'] : $uplink_port['vlan'] . '<br>' .$port['CHANNEL_NAME'];
                    }
                }

                // Check if some variables are empty, assign default value
                if (!isset($uplink_port['path_next_path'])) $uplink_port['path_next_path'] = '';
                if (!isset($uplink_port['ps_bs_name'])) $uplink_port['ps_bs_name'] = '';
                if (!isset($uplink_port['path_bw'])) $uplink_port['path_bw'] = '';
                if (!isset($uplink_port['vlan'])) $uplink_port['vlan'] = '';

                // Count the available bandwidth
                $uplink_port['available_bw'] = $uplink_port['total_bw_mbps'] - $uplink_port['used_bw_mbps'];

            } else {

                // Get Port for Path Name and Port Uplink Details
                foreach ($data['output'] as $port) {
                    if (stripos($request->path_name, $port['PATH_NAME']) === 0) {
                        $uplink_port = [
                            "upe_nid_uplink" => $port['EQUIPMENT_ID'],
                            "uplink_port" => $port['PORT_ACCESS_ID'],
                            "epe_npe_name" => $port['EPE_NPE_NAME'],
                            "epe_npe_port" => $port['EPE_NPE_PORT_ACCESS_ID'],
                            "me_path_bw" => $port['PATH_BANDWIDTH'],
                            "total_bw_mbps" => round($port['TOTAL_BPS_AVAIL']/1000000, 0),
                            "used_bw_mbps" => round($port['BPS_ALLOCATED']/1000000, 0),
                            "utilization" => $port['UTILIZATION'],
                            "over_subscription" => ($port['OVER_SUBSCRIBE']==null) ? 0 : $port['OVER_SUBSCRIBE'],
                            "available_bw" => '',
                        ];
                        break;
                    }
                }

                // process data for uplink port vlan
                foreach ($data['output'] as $port) {
                    // if (strpos($port['PORT_ACCESS_ID'], '.') !== false && stripos($port['PORT_ACCESS_ID'], $uplink_port['uplink_port']) !== false) {
                    if (strpos($port['PORT_ACCESS_ID'], '.') !== false && explode('.', $port['PORT_ACCESS_ID'])[0] == $uplink_port['uplink_port']) {
                        $uplink_port['path_next_path'] = (!isset($uplink_port['path_next_path'])) ? $port['PATH_NAME'] : $uplink_port['path_next_path'] . '<br>' .$port['PATH_NAME'];
                        $uplink_port['ps_bs_name'] = (!isset($uplink_port['ps_bs_name'])) ? $port['PS_BS_NAME'] : $uplink_port['ps_bs_name'] . '<br>' .$port['PS_BS_NAME'];
                        $uplink_port['path_bw'] = (!isset($uplink_port['path_bw'])) ? $port['PATH_BANDWIDTH'] : $uplink_port['path_bw'] . '<br>' .$port['PATH_BANDWIDTH'];
                        $uplink_port['vlan'] = (!isset($uplink_port['vlan'])) ? $port['CHANNEL_NAME'] : $uplink_port['vlan'] . '<br>' .$port['CHANNEL_NAME'];
                    }
                }

                // Check if some variables are empty, assign default value
                if (!isset($uplink_port['path_next_path'])) $uplink_port['path_next_path'] = '';
                if (!isset($uplink_port['ps_bs_name'])) $uplink_port['ps_bs_name'] = '';
                if (!isset($uplink_port['path_bw'])) $uplink_port['path_bw'] = '';
                if (!isset($uplink_port['vlan'])) $uplink_port['vlan'] = '';

                // Count the available bandwidth
                $uplink_port['available_bw'] = $uplink_port['total_bw_mbps'] - $uplink_port['used_bw_mbps'];

                // Temporary for EGU returns no data
                // return $request->link_type == 'primary' 
                // ? ['uplinkPrimaryPortData' => []] 
                // : ['uplinkSecondaryPortData' => []];
            }

            return $request->link_type == 'primary' 
                ? ['uplinkPrimaryPortData' => [$uplink_port]] 
                : ['uplinkSecondaryPortData' => [$uplink_port]];

            // Testing purpose
            // return response()->json([
            //     'status' => 'success',
            //     'data_port_range' => $port_range_details,
            //     'data_sql' => $port_ulink_details,
            //     'uplink_port' => $uplink_port,
            //     'path_name' => $request->path_name
            // ]);

        } catch (\Exception $e) { 
            // Return JSON response with validation errors
            return response()->json([
                'status' => 'error',
                'errors' => "An error has occurred: " . $e->getMessage(),
                'line' => $e->getLine(),
            ], 500); // Unprocessable Entity
        }
            
    }
}
