<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\AuditTrail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Throwable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Carbon;

class FeasibilityCheckController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        ini_set('max_execution_time', 600);
    }


    /**
     * Display the net verification view.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('self-serve.feasibility-check.index');
    }

    protected $service_id;
    protected $ne_ip;
    protected $port;
    // Get Service Info from UNIS
    public function show(Request $request)
    {
        // validate input format
        $request->validate([
            'service_id' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
            'new_bandwidth' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
            'type_of_units' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
        ]);

        try {

            // Make the API request
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('NIG_API_PATHDETAILL2L3_ENDPOINT'), [
                "ServiceID" => $request->service_id // Corrected to associative array
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {
                // Get the JSON response body as an array
                $data = $response->json();

                // Check if UNIS API returns no data
                if (strpos($data['ErrorCode'], '01') !== false) throw new \Exception(__($data['ErrorMessage']));

                // initialize array value with not available
                $svcInfo = array(
                    'service_id' => 'not available',
                    'currentBandwidth' => 'not available',
                    'new_bandwidth' => 'not available',
                    'type_of_units' => 'not available',
                    'serviceId' => 'not available',
                    'nid_upe_oper_speed' => 'not available',
                );


                // User Input
                $svcInfo['service_id'] = $request->service_id;
                $svcInfo['new_bandwidth'] = $request->new_bandwidth;
                $svcInfo['type_of_units'] = $request->type_of_units;
                

                
                $svcInfo['serviceId'] = $request->service_id;
                if (isset($data['Bandwidth'])) $svcInfo['currentBandwidth'] = $data['Bandwidth'];
                $svcInfo['newBandwidth'] = $request->new_bandwidth.$request->type_of_units;


                // Layer 3 Information
                if ($data['Layer3']) {
                    // NNI
                    if (isset($data['Layer3']['PE_EquipmentID'])) { $svcInfo['pe_node'] = $data['Layer3']['PE_EquipmentID']; } else { $svcInfo['pe_node'] = 'not available'; }
                    if (isset($data['Layer3']['PE_PortAccessID'])) { $svcInfo['pe_interface'] = $data['Layer3']['PE_PortAccessID']; } else { $svcInfo['pe_interface'] = 'not available'; }
                }

                // Info Layer 2
                if ($data['Layer2']) {

                    if (isset($data['Layer2']['RedundancyGroupID'])) { $svcInfo['redundancyGroupId'] = $data['Layer2']['RedundancyGroupID']; } else { $svcInfo['redundancyGroupId'] = 'not available'; }
                    // Downlink
                    if (isset($data['Layer2']['UPE_Model'])) { $svcInfo['nid_upe_model'] = $data['Layer2']['UPE_Model']; } else { $svcInfo['nid_upe_model'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_Vendor'])) { $svcInfo['nid_upe_vendor'] = $data['Layer2']['UPE_Vendor']; } else { $svcInfo['nid_upe_vendor'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_LoopbackIP_Downlink'])) { $svcInfo['nid_upe_device_id'] = $data['Layer2']['UPE_LoopbackIP_Downlink']; } else { $svcInfo['nid_upe_device_id'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_EquipmentID_Downlink'])) { $svcInfo['nid_upe_node'] = $data['Layer2']['UPE_EquipmentID_Downlink']; } else { $svcInfo['nid_upe_node'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_PortAccessID_Downlink'])) { $svcInfo['nid_upe_port'] = $data['Layer2']['UPE_PortAccessID_Downlink']; } else { $svcInfo['nid_upe_port'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_PortAccessID_Downlink_Bandwidth'])) { $svcInfo['nid_upe_port_bandwidth'] = $data['Layer2']['UPE_PortAccessID_Downlink_Bandwidth']; } else { $svcInfo['nid_upe_port_bandwidth'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_PortConnector_Downlink'])) { $svcInfo['nid_upe_port_connector'] = $data['Layer2']['UPE_PortConnector_Downlink']; } else { $svcInfo['nid_upe_port_connector'] = 'not available'; }
                    if (isset($data['Layer2']['UPE_Model'])) { $svcInfo['nid_upe_port_setting'] = $data['Layer2']['UPE_Model']; } else { $svcInfo['nid_upe_port_setting'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Connection'])) { $svcInfo['nid_upe_connection_name'] = $data['Layer2']['Path_Connection']; } else { $svcInfo['nid_upe_connection_name'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Connection_Bandwidth'])) { $svcInfo['nid_upe_connection_bw'] = $data['Layer2']['Path_Connection_Bandwidth']; } else { $svcInfo['nid_upe_connection_bw'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Connection_Utilization'])) { $svcInfo['nid_upe_utilization'] = $data['Layer2']['Path_Connection_Utilization']; } else { $svcInfo['nid_upe_utilization'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Connection_Totalbps'])) { $svcInfo['nid_upe_total_bw'] = $data['Layer2']['Path_Connection_Totalbps']; } else { $svcInfo['nid_upe_total_bw'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Connection_Allocatedbps'])) { $svcInfo['nid_upe_usage_bw'] = $data['Layer2']['Path_Connection_Allocatedbps']; } else { $svcInfo['nid_upe_usage_bw'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Connection_OverSub'])) { $svcInfo['nid_upe_oversubscription'] = $data['Layer2']['Path_Connection_OverSub']; } else { $svcInfo['nid_upe_oversubscription'] = 'not available'; }
                    // NNI
                    if (isset($data['Layer2']['NPE_EquipmentID'])) { $svcInfo['npe_node'] = $data['Layer2']['NPE_EquipmentID']; } else { $svcInfo['npe_node'] = 'not available'; }
                    if (isset($data['Layer2']['NPE_PortAccessID'])) { $svcInfo['npe_port'] = $data['Layer2']['NPE_PortAccessID']; } else { $svcInfo['npe_port'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Interconnect'])) { $svcInfo['nni_name'] = $data['Layer2']['Path_Interconnect']; } else { $svcInfo['nni_name'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Interconnect_Bandwidth'])) { $svcInfo['nni_bw'] = $data['Layer2']['Path_Interconnect_Bandwidth']; } else { $svcInfo['nni_bw'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Interconnect_Utilization'])) { $svcInfo['nni_utilization'] = $data['Layer2']['Path_Interconnect_Utilization']; } else { $svcInfo['nni_utilization'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Interconnect_Totalbps'])) { $svcInfo['nni_total_bw'] = $data['Layer2']['Path_Interconnect_Totalbps']; } else { $svcInfo['nni_total_bw'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Interconnect_Allocatedbps'])) { $svcInfo['nni_usage_bw'] = $data['Layer2']['Path_Interconnect_Allocatedbps']; } else { $svcInfo['nni_usage_bw'] = 'not available'; }
                    if (isset($data['Layer2']['Path_Interconnect_OverSub'])) { $svcInfo['nni_overSubscription'] = $data['Layer2']['Path_Interconnect_OverSub']; } else { $svcInfo['nni_overSubscription'] = 'not available'; }
                    // Uplink 1
                    if (isset($data['Layer2']['UPE_PortAccessID_Uplink_Primary'])) { $svcInfo['nid_upe_port_1'] = $data['Layer2']['UPE_PortAccessID_Uplink_Primary']; } else { $svcInfo['nid_upe_port_1'] = 'not available'; }
                    if (isset($data['Layer2']['EPE_EquipmentID_Primary'])) { $svcInfo['epe_node_1'] = $data['Layer2']['EPE_EquipmentID_Primary']; } else { $svcInfo['epe_node_1'] = 'not available'; }
                    if (isset($data['Layer2']['EPE_PortAccessID_Primary'])) { $svcInfo['epe_port_1'] = $data['Layer2']['EPE_PortAccessID_Primary']; } else { $svcInfo['epe_port_1'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MEPrimary'])) { $svcInfo['me_path_name_1'] = $data['Layer2']['Path_MEPrimary']; } else { $svcInfo['me_path_name_1'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MEPrimary_Bandwidth'])) { $svcInfo['me_path_bw_1'] = $data['Layer2']['Path_MEPrimary_Bandwidth']; } else { $svcInfo['me_path_bw_1'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MEPrimary_Utilization'])) { $svcInfo['me_path_utilization_1'] = $data['Layer2']['Path_MEPrimary_Utilization']; } else { $svcInfo['me_path_utilization_1'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MEPrimary_Totalbps'])) { $svcInfo['me_path_total_bw_1'] = $data['Layer2']['Path_MEPrimary_Totalbps']; } else { $svcInfo['me_path_total_bw_1'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MEPrimary_Allocatedbps'])) { $svcInfo['me_path_usage_bw_1'] = $data['Layer2']['Path_MEPrimary_Allocatedbps']; } else { $svcInfo['me_path_usage_bw_1'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MEPrimary_OverSub'])) { $svcInfo['me_path_oversubscription_1'] = $data['Layer2']['Path_MEPrimary_OverSub']; } else { $svcInfo['me_path_oversubscription_1'] = 'not available'; }

                    // Uplink 2
                    if (isset($data['Layer2']['UPE_PortAccessID_Uplink_Secondary'])) { $svcInfo['nid_upe_port_2'] = $data['Layer2']['UPE_PortAccessID_Uplink_Secondary']; } else { $svcInfo['nid_upe_port_2'] = 'not available'; }
                    if (isset($data['Layer2']['EPE_EquipmentID_Secondary'])) { $svcInfo['epe_node_2'] = $data['Layer2']['EPE_EquipmentID_Secondary']; } else { $svcInfo['epe_node_2'] = 'not available'; }
                    if (isset($data['Layer2']['EPE_PortAccessID_Secondary'])) { $svcInfo['epe_port_2'] = $data['Layer2']['EPE_PortAccessID_Secondary']; } else { $svcInfo['epe_port_2'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MESecondary'])) { $svcInfo['me_path_name_2'] = $data['Layer2']['Path_MESecondary']; } else { $svcInfo['me_path_name_2'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MESecondary_Bandwidth'])) { $svcInfo['me_path_bw_2'] = $data['Layer2']['Path_MESecondary_Bandwidth']; } else { $svcInfo['me_path_bw_2'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MESecondary_Utilization'])) { $svcInfo['me_path_utilization_2'] = $data['Layer2']['Path_MESecondary_Utilization']; } else { $svcInfo['me_path_utilization_2'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MESecondary_Totalbps'])) { $svcInfo['me_path_total_bw_2'] = $data['Layer2']['Path_MESecondary_Totalbps']; } else { $svcInfo['me_path_total_bw_2'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MESecondary_Allocatedbps'])) { $svcInfo['me_path_usage_bw_2'] = $data['Layer2']['Path_MESecondary_Allocatedbps']; } else { $svcInfo['me_path_usage_bw_2'] = 'not available'; }
                    if (isset($data['Layer2']['Path_MESecondary_OverSub'])) { $svcInfo['me_path_oversubscription_2'] = $data['Layer2']['Path_MESecondary_OverSub']; } else { $svcInfo['me_path_oversubscription_2'] = 'not available'; }

                }

                if ($svcInfo['nid_upe_vendor'] == 'ALCATEL-LUCENT') {  //UABERNS-01 / ************/ PS1020974557 / BS1020974563 (lepas)

                    $portNumber = preg_replace('/^Port\s+(\d+\/\d+\/\d+)\..*$/', '$1', $svcInfo['nid_upe_port']);
                    $bandwidthMapping = $this->getPortBandwidth($svcInfo['nid_upe_vendor'] . ' ' . $svcInfo['nid_upe_model'], $portNumber);

                    if ($request->new_bandwidth >= 100) {
                        // Flow 1
                        if ($svcInfo['nid_upe_port_bandwidth'] != $bandwidthMapping) {
                            $svcInfo['result'] = 'Error'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }

                        // Flow 2 - only execute if no error from Flow 1
                        if (!isset($svcInfo['result']) && $svcInfo['nid_upe_connection_bw'] != $svcInfo['nid_upe_port_bandwidth']) {
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }
                    }

                    // Flow 3 - only execute if no error from Flow 1 and Flow 2
                    if (!isset($svcInfo['result']) && 
                        ($svcInfo['nid_upe_usage_bw'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['nid_upe_total_bw']) {
                        $svcInfo['result'] = 'Non-Hot';
                        $svcInfo['orderType'] = 'n/a';
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Downlink Port, Requestor to liase with PO';
                    }

                    // Flow 4.1
                    if (!isset($svcInfo['result']) && 
                        (($svcInfo['me_path_usage_bw_1'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units)) > $svcInfo['me_path_total_bw_1']) {

                        $svcInfo['result'] = 'Non-Hot'; 
                        $svcInfo['orderType'] = 'n/a'; 
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';

                    }

                    // Flow 4.2
                    if (!isset($svcInfo['result']) && isset($svcInfo['me_path_usage_bw_2']) && !empty($svcInfo['me_path_usage_bw_2'])) {

                        if ((($svcInfo['me_path_usage_bw_2'] - $this->changetoBps($svcInfo['currentBandwidth'])) + $this->changetoBps($request->new_bandwidth . $request->type_of_units)) > $svcInfo['me_path_total_bw_2']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                        }
                    }

                    // Flow 5
                    if (!isset($svcInfo['result']) && isset($svcInfo['nni_usage_bw']) && !empty($svcInfo['nni_total_bw'])) {

                        if (($svcInfo['nni_usage_bw'] - ($this->changetoBps($svcInfo['currentBandwidth']) + $this->changetoBps($request->new_bandwidth . $request->type_of_units))) > $svcInfo['nni_total_bw']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at NNI, Requestor to liase with CID';

                        }
                    }

                    if (!isset($svcInfo['result'])) {

                        $this->ne_ip = $svcInfo['nid_upe_device_id'];
                        $this->port = preg_replace('/^\S+\s+(\d+\/\d+\/\d+)\.\d+$/', '$1', $svcInfo['nid_upe_port']); // Extracts 1/1/8
                        $svcInfo['nid_upe_oper_speed']  = $this->callAPIupeNokiaShowPortDetail();
    
                        if ($svcInfo['nid_upe_oper_speed'] == ('mismatch_oper_config' || 'not available')) {
    
                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'Modify Downtime'; 
                            $svcInfo['reason'] = 'Change port setting due to mismatch oper & speed config in NE';
    
                        }

                    }


                    if (!isset($svcInfo['result'])) {
                        $result = $this->checkBandwidthCompatibility($request, $svcInfo);
                        echo response()->json($result);

                        // $newBandwidth, $portBandwidth, $operSpeed
                        // $result = $this->checkPortConfiguration('100M', '100M', '');
                        

                    }

                } else if ($svcInfo['nid_upe_vendor'] == 'HUAWEI') {  //UHRHBMM-W0101 / 10.27.144.131 / PS1061686439 / BS1061686444 / Ethernet0/0/1 

                    $portNumber = preg_replace('/^(FastEthernet|GigabitEthernet|TenGigabitEthernet)\s+(\d+\/\d+(?:\/\d+)?).*$/', '$2', $svcInfo['nid_upe_port']);
                    $bandwidthMapping = $this->getPortBandwidth($svcInfo['nid_upe_vendor'] . ' ' . $svcInfo['nid_upe_model'], $portNumber);

                    if ($request->new_bandwidth >= 100) {
                        // Flow 1
                        if ($svcInfo['nid_upe_port_bandwidth'] != $bandwidthMapping) {
                            $svcInfo['result'] = 'Error'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }

                        // Flow 2 - only execute if no error from Flow 1
                        if (!isset($svcInfo['result']) && $svcInfo['nid_upe_connection_bw'] != $svcInfo['nid_upe_port_bandwidth']) {
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }
                    }

                    // Flow 3 - only execute if no error from Flow 1 and Flow 2
                    if (!isset($svcInfo['result']) && 
                        ($svcInfo['nid_upe_usage_bw'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['nid_upe_total_bw']) {
                        $svcInfo['result'] = 'Non-Hot';
                        $svcInfo['orderType'] = 'n/a';
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Downlink Port, Requestor to liase with PO';
                    }

                    // Flow 4.1
                    if (!isset($svcInfo['result']) && 
                        (($svcInfo['me_path_usage_bw_1'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units)) > $svcInfo['me_path_total_bw_1']) {

                        $svcInfo['result'] = 'Non-Hot'; 
                        $svcInfo['orderType'] = 'n/a'; 
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';

                    }

                    // Flow 4.2
                    if (!isset($svcInfo['result']) && isset($svcInfo['me_path_usage_bw_2']) && !empty($svcInfo['me_path_usage_bw_2'])) {

                        if ((($svcInfo['me_path_usage_bw_2'] - $this->changetoBps($svcInfo['currentBandwidth'])) + $this->changetoBps($request->new_bandwidth . $request->type_of_units)) > $svcInfo['me_path_total_bw_2']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                        }
                    }

                    // Flow 5
                    if (!isset($svcInfo['result']) && isset($svcInfo['nni_usage_bw']) && !empty($svcInfo['nni_total_bw'])) {

                        if (($svcInfo['nni_usage_bw'] - ($this->changetoBps($svcInfo['currentBandwidth']) + $this->changetoBps($request->new_bandwidth . $request->type_of_units))) > $svcInfo['nni_total_bw']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at NNI, Requestor to liase with CID';

                        }
                    }

                    if (!isset($svcInfo['result'])) {

                        $this->ne_ip = $svcInfo['nid_upe_device_id'];
                        $this->port = preg_replace('/^\S+\s+(\d+\/\d+\/\d+)\.\d+$/', '$1', $svcInfo['nid_upe_port']); // Extracts 1/1/8
                        echo $svcInfo['nid_upe_oper_speed']  = $this->callAPIupeHuaweiShowPortDetail();
    
                        if ($svcInfo['nid_upe_oper_speed'] == ('mismatch_oper_config' || 'not available')) {
    
                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'Modify Downtime'; 
                            $svcInfo['reason'] = 'Change port setting due to mismatch oper & speed config in NE';
    
                        }

                    }


                    if (!isset($svcInfo['result'])) {
                        $result = $this->checkBandwidthCompatibility($request, $svcInfo);
                        echo response()->json($result);

                        // $newBandwidth, $portBandwidth, $operSpeed
                        // $result = $this->checkPortConfiguration('100M', '100M', '');

                    }


                } else if ($svcInfo['nid_upe_vendor'] == 'ZTE') {   //ZTE - UZICUBR-PP201 / 10.58.200.4 / PS1009328986 / gei_1/3

                    $portNumber = preg_replace('/^(gei|xgei)_(\d+\/\d+).*$/', '$2', $svcInfo['nid_upe_port']);
                    $bandwidthMapping = $this->getPortBandwidth($svcInfo['nid_upe_vendor'] . ' ' . $svcInfo['nid_upe_model'], $portNumber);

                    if ($request->new_bandwidth >= 100) {
                        // Flow 1
                        if ($svcInfo['nid_upe_port_bandwidth'] != $bandwidthMapping) {
                            $svcInfo['result'] = 'Error'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }

                        // Flow 2 - only execute if no error from Flow 1
                        if (!isset($svcInfo['result']) && $svcInfo['nid_upe_connection_bw'] != $svcInfo['nid_upe_port_bandwidth']) {
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }
                    }



                    // Flow 3 - only execute if no error from Flow 1 and Flow 2
                    if (!isset($svcInfo['result']) && 
                        ($svcInfo['nid_upe_usage_bw'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['nid_upe_total_bw']) {
                        $svcInfo['result'] = 'Non-Hot';
                        $svcInfo['orderType'] = 'n/a';
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Downlink Port, Requestor to liase with PO';
                    }

                    // Flow 4.1
                    if (!isset($svcInfo['result'])) {
                        // Check if path values exist, not empty, and not 'not available'
                        if (empty($svcInfo['me_path_usage_bw_1']) || empty($svcInfo['me_path_total_bw_1']) || 
                            $svcInfo['me_path_usage_bw_1'] == 'not available' || $svcInfo['me_path_total_bw_1'] == 'not available') {
                            
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'ME PATH Usage BW (1) value not available';
                        } else {
                            // Proceed with bandwidth check if path values exist and valid
                            if (($svcInfo['me_path_usage_bw_1'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                                $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['me_path_total_bw_1']) {
                                
                                $svcInfo['result'] = 'Non-Hot';
                                $svcInfo['orderType'] = 'n/a';
                                $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                            }
                        }
                    }

                    // Flow 4.2
                    if (!isset($svcInfo['result'])) {
                        // Check if path values exist, not empty, and not 'not available'
                        if (empty($svcInfo['me_path_usage_bw_2']) || empty($svcInfo['me_path_total_bw_2']) || 
                            $svcInfo['me_path_usage_bw_2'] == 'not available' || $svcInfo['me_path_total_bw_2'] == 'not available') {
                            
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'ME PATH Utilization (2) value not available';
                        } else {
                            // Proceed with bandwidth check if path values exist and valid
                            if (($svcInfo['me_path_usage_bw_2'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                                $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['me_path_total_bw_2']) {
                                
                                $svcInfo['result'] = 'Non-Hot';
                                $svcInfo['orderType'] = 'n/a';
                                $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                            }
                        }
                    }

                    // Flow 5
                    if (!isset($svcInfo['result']) && isset($svcInfo['nni_usage_bw']) && !empty($svcInfo['nni_total_bw'])) {

                        if (($svcInfo['nni_usage_bw'] - ($this->changetoBps($svcInfo['currentBandwidth']) + $this->changetoBps($request->new_bandwidth . $request->type_of_units))) > $svcInfo['nni_total_bw']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at NNI, Requestor to liase with CID';

                        }
                    }

                    if (!isset($svcInfo['result'])) {

                        $this->ne_ip = $svcInfo['nid_upe_device_id'];
                        $this->port = $svcInfo['nid_upe_port']; // Extracts 1/1/8
                        $svcInfo['nid_upe_oper_speed']  = $this->callAPIupeZteShowPortDetail();
    
                        if ($svcInfo['nid_upe_oper_speed'] == ('mismatch_oper_config' || 'not available')) {
    
                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'Modify Downtime'; 
                            $svcInfo['reason'] = 'Change port setting due to mismatch oper & speed config in NE';
    
                        }

                    }


                    if (!isset($svcInfo['result'])) {
                        $result = $this->checkBandwidthCompatibility($request, $svcInfo);
                        echo response()->json($result);

                        // $newBandwidth, $portBandwidth, $operSpeed
                        // $result = $this->checkPortConfiguration('100M', '100M', '');

                    }

                } else if ($svcInfo['nid_upe_vendor'] == 'RAISECOM') {  //URCECOM-KCH01 / 10.61.248.1 / PS1080547458

                    $portNumber = preg_replace('/(\d+\/\d+\/\d+).*$/', '$1', $svcInfo['nid_upe_port']);
                    $bandwidthMapping = $this->getPortBandwidth($svcInfo['nid_upe_vendor'] . ' ' . $svcInfo['nid_upe_model'], $portNumber);
                    if ($request->new_bandwidth >= 100) {
                        // Flow 1
                        if ($svcInfo['nid_upe_port_bandwidth'] != $bandwidthMapping) {
                            $svcInfo['result'] = 'Error'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }

                        // Flow 2 - only execute if no error from Flow 1
                        if (!isset($svcInfo['result']) && $svcInfo['nid_upe_connection_bw'] != $svcInfo['nid_upe_port_bandwidth']) {
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }
                    }



                    // Flow 3 - only execute if no error from Flow 1 and Flow 2
                    if (!isset($svcInfo['result']) && 
                        ($svcInfo['nid_upe_usage_bw'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['nid_upe_total_bw']) {
                        $svcInfo['result'] = 'Non-Hot';
                        $svcInfo['orderType'] = 'n/a';
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Downlink Port, Requestor to liase with PO';
                    }

                    // Flow 4.1
                    if (!isset($svcInfo['result'])) {
                        // Check if path values exist, not empty, and not 'not available'
                        if (empty($svcInfo['me_path_usage_bw_1']) || empty($svcInfo['me_path_total_bw_1']) || 
                            $svcInfo['me_path_usage_bw_1'] == 'not available' || $svcInfo['me_path_total_bw_1'] == 'not available') {
                            
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'ME PATH Usage BW (1) value not available';
                        } else {
                            // Proceed with bandwidth check if path values exist and valid
                            if (($svcInfo['me_path_usage_bw_1'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                                $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['me_path_total_bw_1']) {
                                
                                $svcInfo['result'] = 'Non-Hot';
                                $svcInfo['orderType'] = 'n/a';
                                $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                            }
                        }
                    }

                    // Flow 4.2
                    if (!isset($svcInfo['result'])) {
                        // Check if path values exist, not empty, and not 'not available'
                        if (empty($svcInfo['me_path_usage_bw_2']) || empty($svcInfo['me_path_total_bw_2']) || 
                            $svcInfo['me_path_usage_bw_2'] == 'not available' || $svcInfo['me_path_total_bw_2'] == 'not available') {
                            
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'ME PATH Utilization (2) value not available';
                        } else {
                            // Proceed with bandwidth check if path values exist and valid
                            if (($svcInfo['me_path_usage_bw_2'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                                $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['me_path_total_bw_2']) {
                                
                                $svcInfo['result'] = 'Non-Hot';
                                $svcInfo['orderType'] = 'n/a';
                                $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                            }
                        }
                    }

                    // Flow 5
                    if (!isset($svcInfo['result']) && isset($svcInfo['nni_usage_bw']) && !empty($svcInfo['nni_total_bw'])) {

                        if (($svcInfo['nni_usage_bw'] - ($this->changetoBps($svcInfo['currentBandwidth']) + $this->changetoBps($request->new_bandwidth . $request->type_of_units))) > $svcInfo['nni_total_bw']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at NNI, Requestor to liase with CID';

                        }
                    }

                    if (!isset($svcInfo['result'])) {

                        $this->ne_ip = $svcInfo['nid_upe_device_id'];
                        $this->port = $svcInfo['nid_upe_port']; // Extracts 1/1/8
                        $svcInfo['nid_upe_oper_speed']  = $this->callAPIupeNIDRaisecomShowPortDetail();
    
                        if ($svcInfo['nid_upe_oper_speed'] == ('mismatch_oper_config' || 'not available')) {
    
                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'Modify Downtime'; 
                            $svcInfo['reason'] = 'Change port setting due to mismatch oper & speed config in NE';
    
                        }

                    }


                    if (!isset($svcInfo['result'])) {
                        $result = $this->checkBandwidthCompatibility($request, $svcInfo);
                        echo response()->json($result);

                        // $newBandwidth, $portBandwidth, $operSpeed
                        // $result = $this->checkPortConfiguration('100M', '100M', '');

                    }

                } else if ($svcInfo['nid_upe_vendor'] == 'HFR') {  //UNPBBNK-PM301 / 10.53.51.109 / PS1031580713

                    $portNumber = preg_replace('/(\d+\/\d+).*$/', '$1', $svcInfo['nid_upe_port']);
                    $bandwidthMapping = $this->getPortBandwidth($svcInfo['nid_upe_vendor'] . ' ' . $svcInfo['nid_upe_model'], $portNumber);

                    if ($request->new_bandwidth >= 100) {
                        // Flow 1
                        if ($svcInfo['nid_upe_port_bandwidth'] != $bandwidthMapping) {
                            $svcInfo['result'] = 'Error'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }

                        // Flow 2 - only execute if no error from Flow 1
                        if (!isset($svcInfo['result']) && $svcInfo['nid_upe_connection_bw'] != $svcInfo['nid_upe_port_bandwidth']) {
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'Downlink Port Bandwidth not meet, please update manual in unis & retry';
                        }
                    }



                    // Flow 3 - only execute if no error from Flow 1 and Flow 2
                    if (!isset($svcInfo['result']) && 
                        ($svcInfo['nid_upe_usage_bw'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                        $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['nid_upe_total_bw']) {
                        $svcInfo['result'] = 'Non-Hot';
                        $svcInfo['orderType'] = 'n/a';
                        $svcInfo['reason'] = 'Insufficient Bandwidth at Downlink Port, Requestor to liase with PO';
                    }

                    // Flow 4.1
                    if (!isset($svcInfo['result'])) {
                        // Check if path values exist, not empty, and not 'not available'
                        if (empty($svcInfo['me_path_usage_bw_1']) || empty($svcInfo['me_path_total_bw_1']) || 
                            $svcInfo['me_path_usage_bw_1'] == 'not available' || $svcInfo['me_path_total_bw_1'] == 'not available') {
                            
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'ME PATH Usage BW (1) value not available';
                        } else {
                            // Proceed with bandwidth check if path values exist and valid
                            if (($svcInfo['me_path_usage_bw_1'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                                $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['me_path_total_bw_1']) {
                                
                                $svcInfo['result'] = 'Non-Hot';
                                $svcInfo['orderType'] = 'n/a';
                                $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                            }
                        }
                    }

                    // Flow 4.2
                    if (!isset($svcInfo['result'])) {
                        // Check if path values exist, not empty, and not 'not available'
                        if (empty($svcInfo['me_path_usage_bw_2']) || empty($svcInfo['me_path_total_bw_2']) || 
                            $svcInfo['me_path_usage_bw_2'] == 'not available' || $svcInfo['me_path_total_bw_2'] == 'not available') {
                            
                            $svcInfo['result'] = 'Error';
                            $svcInfo['orderType'] = 'n/a';
                            $svcInfo['reason'] = 'ME PATH Utilization (2) value not available';
                        } else {
                            // Proceed with bandwidth check if path values exist and valid
                            if (($svcInfo['me_path_usage_bw_2'] - $this->changetoBps($svcInfo['currentBandwidth'])) + 
                                $this->changetoBps($request->new_bandwidth . $request->type_of_units) > $svcInfo['me_path_total_bw_2']) {
                                
                                $svcInfo['result'] = 'Non-Hot';
                                $svcInfo['orderType'] = 'n/a';
                                $svcInfo['reason'] = 'Insufficient Bandwidth at Uplink Port, Requestor to liase with IPTE/PO';
                            }
                        }
                    }

                    // Flow 5
                    if (!isset($svcInfo['result']) && isset($svcInfo['nni_usage_bw']) && !empty($svcInfo['nni_total_bw'])) {

                        if (($svcInfo['nni_usage_bw'] - ($this->changetoBps($svcInfo['currentBandwidth']) + $this->changetoBps($request->new_bandwidth . $request->type_of_units))) > $svcInfo['nni_total_bw']) {

                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'n/a'; 
                            $svcInfo['reason'] = 'Insufficient Bandwidth at NNI, Requestor to liase with CID';

                        }
                    }
                    
                    if (!isset($svcInfo['result'])) {
                        
                        $this->ne_ip = $svcInfo['nid_upe_device_id'];
                        $this->port = $svcInfo['nid_upe_port']; // Extracts 1/1/8
                        $svcInfo['nid_upe_oper_speed']  = $this->callAPIupeNIDHFRShowPortDetail();
    
                        if ($svcInfo['nid_upe_oper_speed'] == ('mismatch_oper_config' || 'not available')) {
    
                            $svcInfo['result'] = 'Non-Hot'; 
                            $svcInfo['orderType'] = 'Modify Downtime'; 
                            $svcInfo['reason'] = 'Change port setting due to mismatch oper & speed config in NE';
    
                        }

                    }


                    if (!isset($svcInfo['result'])) {
                        $result = $this->checkBandwidthCompatibility($request, $svcInfo);
                        echo response()->json($result);

                        // $newBandwidth, $portBandwidth, $operSpeed
                        // $result = $this->checkPortConfiguration('100M', '100M', '');

                    }

                }


                // sort array alphabetically
                ksort($svcInfo);

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Searched service info for service id. (" . $request->service_id . ")",
                    'user_id' => Auth::id(),
                    'module' => 'Feasibility Check'
                ]);

                // if success, redirect with data
                return redirect()->back()->withInput()->with('data', $svcInfo);


                // Return a view or JSON response with the data
                // return view('service.show', compact('data')); // Adjust the view name as needed
            } else {
                // Handle the case where the response is not successful
                return redirect()->back()->with('error', 'Failed to retrieve service information. Please try again.');
            }

        } catch (Throwable $error) {
            return redirect()->back()->with('error', $error->getMessage());
        }
    }

    // ALU (NOKIA)
    public function callAPIupeNokiaShowPortDetail() {

        // "status": "success",
        // "message": "Successfully retrieved configurations from network element",
        // "data": {
        //   "operational_speed": "100 mbps",
        //   "configured_speed": "100 mbps"
        // },

        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "operational_speed": "N/A",
        //       "configured_speed": "1 Gbps"
        //     },


        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "operational_speed": "Not found",
        //       "configured_speed": "Not found"
        //     },

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_NOKIA_API_ENDPOINT').'/upe/v1/show_port_detail', [
            'ne_ip' => $this->ne_ip,
            'port' => $this->port,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['data']['operational_speed']==='N/A') return 'not available';
            else if ($response->json()['data']['operational_speed']==='Not found') return 'not available';
            // else return customer name
            else if ($response->json()['data']['operational_speed'] != $response->json()['data']['configured_speed']) return 'mismatch_oper_config';
            else return $response->json()['data']['operational_speed'];
        } else {
            // if error return not available
            return 'not available';
        }

    }

    // HUAWEI
    public function callAPIupeHuaweiShowPortDetail() {

        // "status": "success",
        // "message": "Successfully retrieved configurations from network element",
        // "data": {
        //   "speed": "100 mbps",
        // },

        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "N/A",
        //     },


        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "Not found"
        //     },

        $parts = explode('.', $this->port); // Split by the dot
        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_HUAWEI_API_ENDPOINT').'/upe/v1/show_port_detail', [
            'ne_ip' => $this->ne_ip,
            'port' => $parts[0],
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['data']['speed']==='N/A') return 'not available';
            else if ($response->json()['data']['speed']==='Not found') return 'not available';
            // else return customer name
            else return $response->json()['data']['speed'];
        } else {
            // if error return not available
            return 'not available';
        }

    }

    public function callAPIupeHuaweiShowInterface() {

        // "status": "success",
        // "message": "Successfully retrieved configurations from network element",
        // "data": {
        //   "speed": "100 mbps",
        // },

        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "N/A",
        //     },


        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "Not found"
        //     },

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_HUAWEI_API_ENDPOINT').'/upe/v1/show_interface', [
            'ne_ip' => $this->ne_ip,
            'port' => $this->port,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['data']['speed']==='N/A') return 'not available';
            else if ($response->json()['data']['speed']==='Not found') return 'not available';
            // else return customer name
            else return $response->json()['data']['speed'];
        } else {
            // if error return not available
            return 'not available';
        }

    }

    // ZTE
    public function callAPIupeZteShowPortDetail() {

        // "status": "success",
        // "message": "Successfully retrieved configurations from network element",
        // "data": {
        //   "speed": "100 mbps",
        // },

        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "N/A",
        //     },


        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "Not found"
        //     },

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_ZTE_API_ENDPOINT').'/upe/v1/show_interface', [
            'ne_ip' => $this->ne_ip,
            'interface' => $this->port,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['data']['speed']==='N/A') return 'not available';
            else if ($response->json()['data']['speed']==='Not found') return 'not available';
            // else return customer name
            else return $response->json()['data']['speed'];
        } else {
            // if error return not available
            return 'not available';
        }
        
    }

    // NID (HFR)
    public function callAPIupeNIDHFRShowPortDetail() {
    
        // "status": "success",
        // "message": "Successfully retrieved configurations from network element",
        // "data": {
        //   "speed": "100 mbps",
        // },

        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "N/A",
        //     },


        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "Not found"
        //     },

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_HFR_API_ENDPOINT').'/v1/show_interface', [
            'ne_ip' => $this->ne_ip,
            'port' => $this->port,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['data']['speed']==='N/A') return 'not available';
            else if ($response->json()['data']['speed']==='Not found') return 'not available';
            // else return customer name
            else return $response->json()['data']['speed'];
        } else {
            // if error return not available
            return 'not available';
        }
        
    }
    
    // NID (RAISECOM)
    public function callAPIupeNIDRaisecomShowPortDetail() {

        // "status": "success",
        // "output": {
        //   "port": "gigaethernet 1/2/6",
        //   "operate": "up",
        //   "duplex": "duplex",
        //   "speed": "speed",
        //   "speed_value": "1000"
        // },

        // {
        //     "status": "success",
        //     "message": "Successfully retrieved configurations from network element",
        //     "data": {
        //       "speed": "N/A",
        //     },


        // {
        //     "status": "success",
        //     "output": "There is no output returned from NE",
        //     "ne_response": "show interface gigaethernet 10/2/10\r\r\nshow interface gigaethernet 10/2/10\r\r\n                            ^\r\r\nError input in the position marked by '^'.\r\r\nURMUC03-M0101#"
        //   }

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_RAISECOM_API_ENDPOINT').'/v1/show_port', [
            'ne_ip' => $this->ne_ip,
            'port' => $this->port,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['output']==='There is no output returned from NE') return 'not available';
            else if ($response->json()['output']['speed_value']==='Not found') return 'not available';
            // else return customer name
            else return $response->json()['output']['speed_value'];
        } else {
            // if error return not available
            return 'not available';
        }
        
    }

    // EGU - HUAWEI


    // NGU - HUAWEI


    // EGU - NOKIA


    // NGU - NOKIA


    public function changetoBps($currentBandwidth) {
        // Check if the bandwidth value contains specific units
        if (strpos($currentBandwidth, 'G') !== false) {
            // Remove 'Gb' and convert to bps
            $currentBandwidthBps = floatval($currentBandwidth) * 1000000000; // 1 Gbps = 10^9 bps
        } elseif (strpos($currentBandwidth, 'M') !== false) {
            // Remove 'Mb' and convert to bps
            $currentBandwidthBps = floatval($currentBandwidth) * 1000000; // 1 Mbps = 10^6 bps
        } elseif (strpos($currentBandwidth, 'K') !== false) {
            // Remove 'Kb' and convert to bps
            $currentBandwidthBps = floatval($currentBandwidth) * 1000; // 1 Kbps = 10^3 bps
        } else {
            // If no unit is specified, assume the value is already in bps
            $currentBandwidthBps = floatval($currentBandwidth); // Assume input is in bps
        }
    
        return $currentBandwidthBps; // Return the value in bps
    }


    private $portConfigs = [
        'ALCATEL-LUCENT' => [
            '7210 SAS-D' => [['1/1/1', '1/1/10', '1G']],
            '7210 SAS-E' => [['1/1/1', '1/1/24', '1G']],
            '7210 SAS-M' => [
                ['1/1/1', '1/1/24', '1G'],
                ['1/1/25', '1/1/26', '10G']
            ],
            '7250 SAS-E' => [
                ['1/1/1', '1/1/8', '100M'],
                ['1/3/1', '1/3/2', '1G'],
                ['1/6/1', '1/6/2', '1G']
            ]
        ],
        'HUAWEI' => [
            '3328' => [
                ['FastEthernet 0/1', 'FastEthernet 0/24', '100M'],
                ['GigabitEthernet 0/27', 'GigabitEthernet 0/28', '1G']
            ],
            'S3528P-EA' => [
                ['FastEthernet 1/0/1', 'FastEthernet 1/0/24', '100M'],
                ['GigabitEthernet 1/1/1', 'GigabitEthernet 1/1/4', '1G']
            ],
            'S5328C-E1' => [
                ['GigabitEthernet 0/1', 'GigabitEthernet 0/24', '1G'],
                ['TenGigabitEthernet 0/25', 'TenGigabitEthernet 0/26', '10G']
            ]
        ],
        'ZTE' => [
            'ZXR10 5928E' => [
                ['gei_1/1', 'gei_1/24', '1G'],
                ['gei_2/1', 'gei_2/4', '1G']
            ],
            'ZXR10 5928E-F1' => [
                ['gei_1/1', 'gei_1/24', '1G'],
                ['xgei_2/1', 'xgei_2/4', '10G']
            ],
            'ZXR10 8902' => [
                ['gei_2/1', 'gei_2/20', '1G'],
                ['gei_2/21', 'gei_2/24', '1G']
            ]
        ],
        'HFR' => [
            'NID HA-805T' => [
                ['1/1', '1/2', '1G'],
                ['1/3', '1/4', '1G']
            ],
            'NID HA-805' => [['1/1', '1/4', '1G']],
            'NID HA-805-X' => [['1/1', '1/4', '10G']]
        ],
        'RAISECOM' => [
            'RAX711' => [
                ['NNI1', 'NNI2', '1G'],
                ['UNI1', 'UNI1', '1G']
            ],
            'RAX711-L' => [
                ['NNI1', 'NNI2', '1G'],
                ['UNI1', 'UNI4', '1G']
            ],
            'RAX711-C' => [
                ['TGE1/1/1', 'TGE1/1/4', '10G'],
                ['GE1/2/1', 'GE1/2/6', '1G'],
                ['GE1/2/7', 'GE1/2/12', '1G']
            ]
        ]
    ];




    public function getPortBandwidth($vendorModel, $portNumber) 
    {
        foreach ($this->portConfigs as $vendor => $models) {
            if (strpos($vendorModel, $vendor) === 0) {
                foreach ($models as $model => $ranges) {
                    if (strpos($vendorModel, $model) !== false) {
                        foreach ($ranges as [$start, $end, $bandwidth]) {
                            if ($this->isPortInRange($portNumber, $start, $end)) {
                                return $bandwidth;
                            }
                        }
                    }
                }
            }
        }
        return null;
    }
    
    private function isPortInRange($port, $start, $end)
    {
        $startNum = $this->extractPortNumber($start);
        $endNum = $this->extractPortNumber($end);
        $currentNum = $this->extractPortNumber($port);
        
        return $currentNum >= $startNum && $currentNum <= $endNum;
    }
    
    private function extractPortNumber($port)
    {
        preg_match('/\d+$/', $port, $matches);
        return isset($matches[0]) ? (int)$matches[0] : 0;
    }




    

    public function checkPortConfiguration($newBandwidth, $portBandwidth, $operSpeed = null) 
    {
        $newBwInMbps = $this->convertToMbps($newBandwidth);

        if ($newBwInMbps < 100) {
            return ['result' => 'Modify Hot', 'orderType' => 'Modify Hot', 'reason' => '-'];
        }

        if (empty($operSpeed)) {
            if ($portBandwidth === '100M') {
                return ['result' => 'Modify Downtime', 'orderType' => 'Modify Downtime', 'reason' => 'Change port form FE to GE'];
            }
        }

        $result = ['result' => '', 'reason' => ''];

        if ($newBwInMbps === 100) {
            if ($portBandwidth === '100M') {
                if ($operSpeed === '100') {
                    $result = ['result' => 'Modify Hot', 'reason' => '-'];
                } elseif (in_array($operSpeed, ['1000', 'auto', 'a-1000'])) {
                    $result = ['result' => 'Modify Downtime', 'reason' => 'Change Port Setting'];
                }
            } elseif ($portBandwidth === '1G') {
                if ($operSpeed === '100') {
                    $result = ['result' => 'Modify Downtime', 'reason' => 'Change Port Setting'];
                } elseif (in_array($operSpeed, ['1000', 'auto', 'a-1000'])) {
                    $result = ['result' => 'Hot', 'reason' => '-'];
                }
            }
        } elseif ($newBwInMbps > 100) {
            if ($portBandwidth === '100M') {
                $result = ['result' => 'Modify Downtime', 'reason' => 'Change port form FE to GE'];
            } elseif ($portBandwidth === '1G') {
                if (in_array($operSpeed, ['1000', 'auto', 'a-1000'])) {
                    $result = ['result' => 'Hot', 'reason' => '-'];
                } elseif ($operSpeed === '100') {
                    $result = ['result' => 'Modify Downtime', 'reason' => 'Change Port Setting'];
                }
            } elseif ($portBandwidth === '10G') {
                if (in_array($operSpeed, ['10G', '20G'])) {
                    $result = ['result' => 'Modify Hot', 'reason' => '-'];
                } elseif (in_array($operSpeed, ['1000', 'auto', 'a-1000'])) {
                    $result = ['result' => 'Modify Downtime', 'reason' => 'Change port form GE to 10G'];
                }
            }
        }

        if (empty($result['result'])) {
            $result = ['result' => 'Error', 'reason' => 'DL port BW not compatible with port setting'];
        }

        $result['orderType'] = $result['result'] === 'Hot' ? 'Modify Hot' : 
                              ($result['result'] === 'Error' ? 'N/A' : 'Modify Downtime');

        return $result;
    }

    private function convertToMbps($bandwidth) {
        $value = (int)$bandwidth;
        $unit = strtoupper(preg_replace('/[^A-Za-z]/', '', $bandwidth));
        
        if ($unit === 'G') {
            return $value * 1000;
        }
        return $value;
    }




    public function checkBandwidthCompatibility(Request $request, array $svcInfo)
    {
        $newBandwidthBps = $this->changetoBps($request->new_bandwidth . $request->type_of_units);
    
        if ($newBandwidthBps < '100000000') {   //100M
            $svcInfo['result'] = 'N/A';
            $svcInfo['orderType'] = 'Modify Hot';
            $svcInfo['reason'] = 'N/A';
        } 
        else if ($newBandwidthBps >= '100000000') {  // >= 100M
            $portBandwidthBps = $this->changetoBps($svcInfo['nid_upe_port_bandwidth']);
    
            // NID/UPE DL Port BW
            if ($portBandwidthBps == '100000000') {   //100M
                $svcInfo['result'] = 'Non-Hot';
                $svcInfo['orderType'] = 'Modify Downtime';
                $svcInfo['reason'] = 'Change port form FE to GE';
            } 
            else if ($portBandwidthBps == '1000000000') {  //1G
                if ($svcInfo['nid_upe_oper_speed'] == '100 mbps') {
                    $svcInfo['result'] = 'Non-Hot';
                    $svcInfo['orderType'] = 'Modify Downtime';
                    $svcInfo['reason'] = 'Change Port Setting';
                } 
                else if (in_array($svcInfo['nid_upe_oper_speed'], ['1000', 'auto', 'a-1000'])) {
                    $svcInfo['result'] = 'Hot';
                    $svcInfo['orderType'] = 'Modify Hot';
                    $svcInfo['reason'] = 'N/A';
                } 
                else if (in_array($svcInfo['nid_upe_oper_speed'], ['10 Gbps', '20 Gbps'])) {
                    $svcInfo['result'] = 'Error';
                    $svcInfo['orderType'] = 'N/A';
                    $svcInfo['reason'] = 'DL port BW not compatible with port setting';
                } 
                else {
                    $svcInfo['result'] = 'Error';
                    $svcInfo['orderType'] = 'N/A';
                    $svcInfo['reason'] = 'Invalid condition port setting';
                }
            } 
            else if ($portBandwidthBps == '10000000000' || $portBandwidthBps == '20000000000') {  //10G or 20G
                if ($svcInfo['nid_upe_oper_speed'] == '100 mbps') {
                    $svcInfo['result'] = 'Error';
                    $svcInfo['orderType'] = 'N/A';
                    $svcInfo['reason'] = 'DL port BW not compatible with port setting';
                } 
                else if (in_array($svcInfo['nid_upe_oper_speed'], ['1000', 'auto', 'a-1000'])) {
                    $svcInfo['result'] = 'Non-Hot';
                    $svcInfo['orderType'] = 'Modify Downtime';
                    $svcInfo['reason'] = 'Change Port Setting';
                } 
                else if (in_array($svcInfo['nid_upe_oper_speed'], ['10 Gbps', '20 Gbps'])) {
                    $svcInfo['result'] = 'N/A';
                    $svcInfo['orderType'] = 'Modify Hot';
                    $svcInfo['reason'] = 'N/A';
                } 
                else {
                    $svcInfo['result'] = 'Error';
                    $svcInfo['orderType'] = 'N/A';
                    $svcInfo['reason'] = 'Invalid condition port setting';
                }
            } 
            else {
                $svcInfo['result'] = 'Error';
                $svcInfo['orderType'] = 'N/A';
                $svcInfo['reason'] = 'Invalid condition port setting';
            }
        } 
        else {
            $svcInfo['result'] = 'Error';
            $svcInfo['orderType'] = '';
            $svcInfo['reason'] = 'Change port setting due to mismatch oper & speed config in NE';
        }
    
        return $svcInfo;
    }




}
