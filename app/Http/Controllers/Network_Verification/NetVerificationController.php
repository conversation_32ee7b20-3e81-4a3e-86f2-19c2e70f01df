<?php

namespace App\Http\Controllers\Network_Verification;

use App\Models\AuditTrail;
use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Throwable;


class NetVerificationController extends Controller
{
    protected $service_id;
    // Get Service Info from UNIS
    public function show(Request $request)
    {
        // validate input format
        $request->validate([
            'service_id' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('SELFSERVE_UNIS_API_ENDPOINT'), [
                "NIGRequest" => [
                    "Layer" => "BOTH",
                    "graniteid" => $request->service_id,
                    "Qscenario" => "POSITIVE",
                    "serviceType" => ""
                ]
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {
                // Get the JSON response body as an array
                $data = $response->json();

                // Check if UNIS API returns no data
                if (strpos($data['NIGResponse']['ErrorMesage'], 'no data found') !== false) throw new \Exception(__('No matching Service Id. in UNIS'));

                // initialize array value with not available
                $svcInfo = array(
                    'customer_name' => 'not available',
                    'vrf' => 'not available',
                    'network_id' => 'not available',
                    'leg_id' => 'not available',
                    'product' => 'not available',
                    'qos' => 'not available',
                    'vpn' => 'not available',
                    'pe_ip' => 'not available',
                    'ce_ip' => 'not available',
                    'ce_ipv6' => 'not available',
                    'pe_ipv6' => 'not available',
                    'ce' => 'not available',
                    'ce_vendor' => 'not available',
                    'protocol' => 'not available',
                    'bandwidth' => 'not available',
                    'pe' => 'not available',
                    'pe_vendor' => 'not available',
                    'npe' => 'not available',
                    'npe_vendor' => 'not available',
                    'epe_1' => 'not available',
                    'epe_2' => 'not available',
                    'npe_device_id' => 'not available',
                    'npe_downlink' => 'not available',
                    'npe_downlink_device_id' => 'not available',
                    'npe_downlink_interface' => 'not available',
                    'pe_device_id' => 'not available',
                    'npe_interface' => 'not available',
                    'epe_interface_1' => 'not available',
                    'epe_interface_2' => 'not available',
                    'pe_interface' => 'not available',
                    'epe_device_id_1' => 'not available',
                    'epe_device_id_2' => 'not available',
                    'epe_vendor_1' => 'not available',
                    'epe_vendor_2' => 'not available',
                    'ce_vendor' => 'not available',
                    'nid_upe' => 'not available',
                    'nid_upe_interface_ul_1' => 'not available',
                    'nid_upe_interface_dl' => 'not available',
                    'nid_upe_model' => 'not available',
                    'nid_upe_vendor' => 'not available',
                    'nid_upe_device_id' => 'not available',
                    'nid_upe_interface_ul_2' => 'not available',
                    'sla_slg' => 'not available',
                    'upe_type' => 'not available',
                    'pe_import_map' => 'not available',  // set if available
                    'epe_ems_service_id' => 'not available',
                    'epe_vcid' => 'not available',
                    'ce_interface' => 'not available',
                    'L2_id' => 'not available',
                );

                // initialize wc info array to determine metro-e type
                $L2wcInfo = array();
                $LanIP = array();

                // if value exist in API, replace the initial values
                $svcInfo['service_id'] = $request->service_id;

                // if layer 3 info exists
                if (isset($data['NIGResponse']['layer3'])) {

                    // massage L3 info
                    foreach ($data['NIGResponse']['layer3'] as $info) {
                        // General Info
                        // if ($info['name'] === 'ID' && $info['field'] === 'MPLS Network' && $info['group'] === $request->service_id) $svcInfo['network_id'] = $info['value'];
                        if ($info['name'] === 'Parent Services' && $info['field'] === 'Parent Services' && $info['group'] === $request->service_id && ctype_alnum($info['value'])) $svcInfo['network_id'] = $info['value'];
                        else if ($info['name'] === 'Name' && $info['field'] === 'Redundancy Group') $svcInfo['leg_id'] = $info['value'];
                        else if ($info['name'] === 'Category' && $info['field'] === 'Network') $svcInfo['product'] = $info['value'];
                        else if ($info['name'] === 'QoS Package' && $info['group'] === $request->service_id) $svcInfo['qos'] = $info['value'];
                        else if ($info['name'] === 'Name' && $info['field'] === 'MPLS Network' && $info['group'] === $request->service_id) $svcInfo['vpn'] = $info['value'];
                        else if ($info['name'] === 'VRF Name' && $info['field'] === 'MPLSEndPoint' && $info['group'] === $request->service_id) $svcInfo['vrf'] = $info['value'];
                        else if ($info['name'] === 'Routing Protocol' && $info['group'] === $request->service_id) $svcInfo['protocol'] = $info['value'];
                        else if ($info['name'] === 'Bandwidth' && $info['group'] === $request->service_id) $svcInfo['bandwidth'] = $info['value'];

                        // PE
                        else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_ip'] = $info['value'];
                        else if ($info['name'] === 'Vendor' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_vendor'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_ipv6'] = $info['value'];
                        else if ($info['name'] === 'CIDR' && $info['group'] === $request->service_id) $cidr = $info['value'];
                        else if ($info['name'] === 'CIDR IPv6' && $info['group'] === $request->service_id) $cidr_ipv6 = $info['value'];
                        else if ($info['name'] === 'Device Name' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe'] = $info['value'];
                        else if ($info['name'] === 'Device IP' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_device_id'] = $info['value'];
                        else if ($info['name'] === 'Interface Name' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['pe_interface'] = $info['value'];
                        else if ($info['name'] === 'Import Map' && $info['type'] === 'PE' && $info['group'] === $request->service_id) {
                            // Step 1: Split the value into an array
                            $valueArray = explode(',', $info['value']);

                            // Step 2: Filter for elements containing the keyword 'tmd' (case-insensitive)
                            $filteredElements = array_filter($valueArray, function($element) {
                                return stripos($element, 'tmd') !== false;
                            });

                            // Step 3: Convert the filtered elements back into a string
                            $svcInfo['pe_import_map'] = implode(',', $filteredElements);
                        }

                        // CE
                        else if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_ip'] = $info['value'];
                        else if ($info['name'] === 'Interface Name' && $info['type'] === 'CE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $svcInfo['ce_interface'] = $info['value'];
                        else if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_ipv6'] = $info['value'];
                        else if ($info['name'] === 'Device Name' && $info['type'] === 'CE' && $info['field'] === 'Container' && $info['group'] === $request->service_id) $svcInfo['ce'] = $info['value'];
                        else if ($info['name'] === 'Device Vendor' && $info['type'] === 'CE' && $info['field'] === 'Container' && $info['group'] === $request->service_id) $svcInfo['ce_vendor'] = $info['value'];
                        else if ($info['name'] === 'Network' && $info['field'] === 'Network Advertisement' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $LanIP[] = $info['value'];
                    }

                    // Assign LAN IP to svcInfo array
                    $svcInfo['lan_ip'] = $LanIP;

                    // Add CIDR to IPv4 and IPv6
                    if ($svcInfo['pe_ip'] !== 'not available') $svcInfo['pe_ip'] .= '/' . $cidr;
                    if ($svcInfo['ce_ip'] !== 'not available') $svcInfo['ce_ip'] .= '/' . $cidr;
                    if ($svcInfo['pe_ipv6'] !== 'not available') $svcInfo['pe_ipv6'] .= '/' . $cidr_ipv6;
                    if ($svcInfo['ce_ipv6'] !== 'not available') $svcInfo['ce_ipv6'] .= '/' . $cidr_ipv6;

                }

                // if layer 2 info exists
                if (isset($data['NIGResponse']['layer2'])) {

                    // find L2 Service Id
                    foreach ($data['NIGResponse']['layer2'] as $info) {
                        // General Info
                        if ($info['name'] === 'Name' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) $svcInfo['L2_id'] = $info['value'];
                        else if ($info['name'] === 'TECHNICAL SLA/SLG' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) $svcInfo['sla_slg'] = $info['value'];
                        else if ($info['name'] === 'UPE Type Physical Group' && $info['field'] === 'UPE' && $info['group'] === $request->service_id && strpos($info['value'], 'TYPE') !== false) $svcInfo['upe_type'] = $info['value'];
                        // For P2P/WSE/NBGH/ELAN
                        else if ($info['name'] === 'Category' && $info['field'] === 'Network' && $info['group'] === $request->service_id) {
                            if ($svcInfo['product'] === 'not available') {
                                $svcInfo['product'] = $info['type'];
                                $svcInfo['network_id'] = $info['value'];
                            }
                        }
                        // Technical SLA/SLG
                        else if ($info['name'] === 'TECHNICAL SLA/SLG' && $info['field'] === 'TM_SERVICE_INFO' && $info['group'] === $request->service_id) {
                            $svcInfo['sla_slg'] = $info['value'];
                        }
                        // UPE Type
                        // else if ($info['name'] === 'UPE TYPE' && $info['field'] === 'UPE Attributes' && $info['group'] === $request->service_id) {
                        //     $svcInfo['upe_type'] = $info['value'];
                        // }

                    }

                    // If Product = 'Wholesale Ethernet'
                    if (strtolower($svcInfo['product']) === 'wholesale ethernet' || strtolower($svcInfo['product']) === 'metro e') {
                        $svcInfo['L2_id'] = 'not available'; // reassign L2 Service Id because it returns service id
                        foreach ($data['NIGResponse']['layer2'] as $info) {
                            // Leg Id
                            if ($info['name'] === 'REDUNDANCY GROUP ID' && $info['field'] === 'TM_SERVICE_INFO' && $info['group'] === $request->service_id) $svcInfo['leg_id'] = $info['value'];
                            // QOS
                            else if ($info['field'] === 'TM_COS' && $info['group'] === $request->service_id && $info['value']==='100') $svcInfo['qos'] = $info['name']." - ".$info['value'];
                            // Bandwidth
                            else if ($info['name'] === 'Bandwidth' && $info['group'] === $request->service_id && $info['field']==='L2 Service') $svcInfo['bandwidth'] = $info['value'];
                            // EPE 1
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_interface_1'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_device_id_1'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 1') $svcInfo['epe_vendor_1'] = $info['value'];
                            // NPE 1 (UPE directly connected to NPE)
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_interface_1'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_device_id_1'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 1') $svcInfo['epe_vendor_1'] = $info['value'];
                            // EPE 2
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_interface_2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_device_id_2'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'EPE DOWNLINK 2') $svcInfo['epe_vendor_2'] = $info['value'];
                            // NPE 2 (UPE directly connected to NPE)
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_interface_2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_device_id_2'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'NPE DOWNLINK 2') $svcInfo['epe_vendor_2'] = $info['value'];
                            // EMS Service Name
                            else if ($info['name'] === 'EMS SERVICE NAME' && $info['group'] === $request->service_id) $svcInfo['epe_ems_service_id'] = $info['value'];
                            // VCID
                            else if ($info['name'] === 'VCID' && $info['group'] === $request->service_id) $svcInfo['epe_vcid'] = $info['value'];
                            // UPE Type Physical Group
                            else if ($info['name'] === 'UPE Type Physical Group' && $info['group'] === $request->service_id) $svcInfo['upe_type'] = $info['value'];
                            // UPE
                            else if ($info['name'] === 'Device Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe'] = $info['value'];
                            else if ($info['name'] === 'Model' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_model'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_device_id'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_vendor'] = $info['value'];
                            else if ($info['name'] === 'UPE SERVICE INSTANCE ID' && $info['group'] === $request->service_id && $info['field'] === 'UPE Attributes') $svcInfo['nid_upe_svc_instance_id'] = $info['value'];
                            else if ($info['name'] === 'Serial Number' && $info['group'] === $request->service_id && $info['field'] === 'UPE') $svcInfo['nid_upe_serial_no'] = $info['value'];
                            else if ($info['name'] === 'UPE TYPE' && $info['group'] === $request->service_id && $info['field'] === 'UPE Attributes') $svcInfo['nid_upe_type_non_physical'] = $info['value'];
                            // UPE INTERFACE
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE UPLINK 1') $svcInfo['nid_upe_interface_ul_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE UPLINK 2') $svcInfo['nid_upe_interface_ul_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['group'] === $request->service_id && $info['field'] === 'UPE DOWNLINK') $svcInfo['nid_upe_interface_dl'] = $info['value'];
                           
                        }

                    }
                    else {

                        // massage L2 info
                        foreach ($data['NIGResponse']['layer2'] as $info) {
                            // PE
                            if ($info['name'] === 'Device Name' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe_device_id'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'iMSE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['pe_interface'] = $info['value'];

                            // NPE
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id'])  $svcInfo['npe'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id'])  $svcInfo['npe_vendor'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_device_id'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_interface'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_downlink_device_id'] = $info['value'];
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_downlink'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $svcInfo['npe_downlink_interface'] = $info['value'];

                            // for EPE
                            // EPE 1
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_1'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_device_id_1'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_interface_1'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_vendor_1'] = $info['value'];
                            // EPE 2
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_2'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_device_id_2'] = $info['value'];
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_interface_2'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_vendor_2'] = $info['value'];                    

                            // for UPE/NID (1)
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 1' && $info['group'] === $request->service_id) {
                                if ($svcInfo['nid_upe_interface_ul_1'] == 'not available') {
                                    $svcInfo['nid_upe_interface_ul_1'] = $info['value'];
                                    continue;
                                }
                            }
                            else if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE DOWNLINK' && $info['group'] === $request->service_id) $svcInfo['nid_upe_interface_dl'] = $info['value'];
                            else if ($info['name'] === 'Device Name' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe'] = $info['value'];
                            else if ($info['name'] === 'Model' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_model'] = $info['value'];
                            else if ($info['name'] === 'Vendor' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_vendor'] = $info['value'];
                            else if ($info['name'] === 'Device IP' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe_device_id'] = $info['value'];
                            // UPE primary
                            if ($svcInfo['upe_type'] === 'TYPE 2' || $svcInfo['upe_type'] === 'TYPE 4') {
                                // if ($svcInfo['nid_upe_interface_ul_1'] != 'not available' && $svcInfo['nid_upe_interface_ul_2'] == 'not available') if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 2' && $info['group'] === $request->service_id) $svcInfo['nid_upe_interface_ul_2'] = $info['value'];
                                if ($info['name'] === 'Interface Name' && $info['field'] === 'UPE UPLINK 2' && $info['group'] === $request->service_id) $svcInfo['nid_upe_interface_ul_2'] = $info['value'];
                            }
                            
                        }

                        // for AGG
                        if (stripos($svcInfo['npe_downlink'], 'agg') !== false) {
                            // re-assign PE with AGG and NPE info
                            // NPE
                            $svcInfo['npe'] = 'not available';
                            $svcInfo['npe_vendor'] = 'not available';
                            $svcInfo['npe_interface'] = 'not available';
                            $svcInfo['npe_device_id'] = 'not available';
                            $svcInfo['L2_id'] = 'not available';
                            // PE
                            $svcInfo['pe'] = $svcInfo['npe_downlink'];
                            $svcInfo['pe_device_id'] = $svcInfo['npe_downlink_device_id'];
                            $svcInfo['pe_interface'] = $svcInfo['npe_downlink_interface'];
                            // Assign PE Vendor based on AGG name
                            if (stripos($svcInfo['npe_downlink'], 'zt') !== false) $svcInfo['pe_vendor'] = 'ZTE';
                            else if (stripos($svcInfo['npe_downlink'], 'hw') !== false) $svcInfo['pe_vendor'] = 'Huawei';
                            else $svcInfo['pe_vendor'] = 'not available';
                        }

                    }

                }

                // get customer name
                $this->service_id = $svcInfo['service_id'];
                $svcInfo['customer_name'] = $this->getCustomerName();

                // add success message
                $svcInfo['success'] = 'Successfully retrieved info from UNIS';

                // sort array alphabetically
                ksort($svcInfo);

                // testing massage data
                // dd($svcInfo);

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Searched service info for service id. (" . $request->service_id . ")",
                    'user_id' => Auth::id(),
                    'module' => 'Network Verifications'
                ]);

                // if success, redirect with data
                return redirect()->back()->with('data', $svcInfo);

                // Return the data to the client
                // return response()->json([
                //     'status' => 'success',
                //     'message' => 'Matched Service Id.',
                //     'details' => $svcInfo
                // ]);
            } else {
                // Handle non-2xx status code responses
                return redirect()->back()->with('error', $response->body());
            }
        } catch (Throwable $error) {
            return redirect()->back()->with('error', $error->getMessage());
        }
    }

    // generate excel file
    public function download($service_id)
    {
        try {

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('SELFSERVE_UNIS_API_ENDPOINT'), [
                "NIGRequest" => [
                    "Layer" => "BOTH",
                    "graniteid" => $service_id,
                    "Qscenario" => "POSITIVE",
                    "serviceType" => ""
                ]
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {
                // Get the JSON response body as an array
                $data = $response->json();

                // if Service Id not found in UNIS, the size of array is 3
                // if (count($data['NIGResponse']) === 3) {
                //     throw new \Exception(__('No matching Service Id. in UNIS'));
                // }
                if (strpos($data['NIGResponse']['ErrorMesage'], 'no data found') !== false) throw new \Exception(__('No matching Service Id. in UNIS'));


                // combine array for Layer 2 and Layer 3
                // $combineData = $data['NIGResponse']['layer3'] + $data['NIGResponse']['layer2'];

                // Convert JSON response to PHP array
                // $dataArray = [
                //     ['Name', 'Value', 'Type', 'Group', 'Field', 'Status'] // Headers
                // ];

                $spreadsheet = new Spreadsheet();
                $sheet = $spreadsheet->getActiveSheet();

                // Set headers
                $sheet->setCellValue('A1', 'Name');
                $sheet->setCellValue('B1', 'Value');
                $sheet->setCellValue('C1', 'Type');
                $sheet->setCellValue('D1', 'Group');
                $sheet->setCellValue('E1', 'Field');
                $sheet->setCellValue('F1', 'Status');

                // fill the data to excel
                $row = 2;       // start with 2nd row after header
                // for Layer 3
                if (isset($data['NIGResponse']['layer3']))  // check if Layer 3 info exists
                    foreach ($data['NIGResponse']['layer3'] as $item) {
                        $sheet->setCellValue('A' . $row, $item['name']);
                        $sheet->setCellValue('B' . $row, $item['value']);
                        $sheet->setCellValue('C' . $row, $item['type']);
                        $sheet->setCellValue('D' . $row, $item['group']);
                        $sheet->setCellValue('E' . $row, $item['field']);
                        $sheet->setCellValue('F' . $row, $item['status']);
                        $row++;
                    }
                // for Layer 2 
                if (isset($data['NIGResponse']['layer2']))  // check if Layer 3 info exists
                    foreach ($data['NIGResponse']['layer2'] as $item) {
                        $sheet->setCellValue('A' . $row, $item['name']);
                        $sheet->setCellValue('B' . $row, $item['value']);
                        $sheet->setCellValue('C' . $row, $item['type']);
                        $sheet->setCellValue('D' . $row, $item['group']);
                        $sheet->setCellValue('E' . $row, $item['field']);
                        $sheet->setCellValue('F' . $row, $item['status']);
                        $row++;
                    }

                // Create a writer object
                $writer = new Xlsx($spreadsheet);

                // Create temporary file path
                $filePath = tempnam(sys_get_temp_dir(), 'excel');

                // Save the Excel file to the temporary path
                $writer->save($filePath);

                // Set filename
                $filename = strtoupper($service_id) . '_SERVICE_INFO.xlsx';

                // Set headers for Excel download
                $headers = [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'Content-Disposition' => 'attachment; filename=' . $filename,
                ];

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Downloaded service info Excel file for service id. (" . $service_id . ")",
                    'user_id' => Auth::id(),
                    'module' => 'Network Verifications'
                ]);

                // Return Excel file as download response
                return response()->download($filePath, $filename, $headers)->deleteFileAfterSend(true);
            } else {
                // Handle non-2xx status code responses
                return redirect()->back()->with('error', $response->body());
            }
        } catch (Throwable $error) {
            return redirect()->back()->with('error', $error->getMessage());
        }
    }

    // get PE config
    public function getNeConfig(Request $request)
    {
        // Verify input send
        // for PE
        if ($request->network_element == 'pe') {
            $inputToValidate = [
                'pe_device_id' => ['required', Rule::notIn(['not available'])],
                'pe_interface' => ['required', Rule::notIn(['not available'])],
                'pe_ip' => ['required', Rule::notIn(['not available'])],
                'ce_ip' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])],
                'vrf' => ['required', Rule::notIn(['not available'])],
                'vendor' => ['required', Rule::notIn(['not available'])],
                'product' => ['required', Rule::notIn(['not available'])],
                'ce' => ['required', Rule::notIn(['not available'])],
            ];
        }
        // for CE
        else if ($request->network_element == 'ce') {
            $inputToValidate = [
                'ce_ip' => ['required', Rule::notIn(['not available'])], 
                'vendor' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])]
            ];
        }
        // for NID/UPE
        else if ($request->network_element == 'nid' || $request->network_element == 'upe') {
            $inputToValidate = [
                'nid_upe_device_id' => ['required', Rule::notIn(['not available'])],
                'nid_upe_model' => ['required', Rule::notIn(['not available'])],
                'nid_upe_interface_ul_1' => ['required', Rule::notIn(['not available'])],
                'nid_upe_interface_dl' => ['required', Rule::notIn(['not available'])],
                'vendor' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])],
                'upe_type' => ['required', Rule::notIn(['not available']), 'regex:/^TYPE \d+$/']
            ];
            if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') $inputToValidate['nid_upe_interface_ul_2'] = ['required', Rule::notIn(['not available'])];
        }
        // for EPE
        else if ($request->network_element == 'epe') {
            if ($request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 4-1')
                $inputToValidate = [
                    'epe_device_id_1' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_1' => ['required', Rule::notIn(['not available'])],
                    'service_id' => ['required', Rule::notIn(['not available'])],
                    'vendor' => ['required', Rule::notIn(['not available'])]
                ];
            else if ($request->metroe_type == 'TYPE 5-2' || $request->metroe_type == 'TYPE 4-2')
                $inputToValidate = [
                    'epe_device_id_2' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_2' => ['required', Rule::notIn(['not available'])],
                    'service_id' => ['required', Rule::notIn(['not available'])],
                    'vendor' => ['required', Rule::notIn(['not available'])]
                ];
            else if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4')
                $inputToValidate = [
                    'epe_device_id_1' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_1' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_2' => ['required', Rule::notIn(['not available'])],
                    'service_id' => ['required', Rule::notIn(['not available'])],
                    'vendor' => ['required', Rule::notIn(['not available'])]
                ];
        }
        // for NPE
        else if ($request->network_element == 'npe')
            $inputToValidate = [
                'npe_device_id' => ['required', Rule::notIn(['not available'])],
                'npe_interface' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])],
                'vendor' => ['required', Rule::notIn(['not available'])]
            ];

        // Validate and sanitize input
        $validatedData = Validator::make($request->all(), $inputToValidate);

        // check if validation failed, send json error messages
        if ($validatedData->fails()) {
            return response()->json([
                'status' => 'failed',
                'output' => $validatedData->errors()->all()
            ]);
        }

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "Retrieve configurations from ".strtoupper($request->network_element)." for service id. (".$request->service_id.")",
            'user_id' => Auth::id(),
            'module' => 'Network Verifications'
        ]);

        // assign commands to send to ne
        if ($request->network_element == 'pe' && $request->vendor == 'juniper') {
            $ne_ip = $request->pe_device_id;
            $command = array(
                'show interfaces descriptions | match :service_id',
                'show interfaces extensive :pe_interface',
                // 'show interfaces :ae_interface',
                'show configuration interface :pe_interface',
                'show configuration routing-instances :vrf | match :ce_ip | display set',
                'show bgp summary instance :vrf | match :ce_ip',
                // 'show configuration policy-options policy-statement :vrf_:end_customer_name_:logical_site_name',
                'show configuration class-of-service interfaces :port unit :vlan | display set',
                'show configuration routing-instances :vrf | match vrf ',
                'ping :ce_ip source :pe_ip routing-instance :vrf count 5',
                'ping :ce_ip source :pe_ip routing-instance :vrf rapid count 1000'
            );
            // if product is ipvpn
            if ($request->product==='ipvpn') $command[] = 'show configuration routing-instances :vrf | match cast';
            // if product is 1gov
            else if ($request->product==='1gov') $command[] = 'show configuration policy-options policy-statement :policy_name';
        } else if ($request->network_element == 'ce') {
            $ne_ip = explode("/", $request->ce_ip)[0];
            if ($request->vendor == 'cisco') {
                $command = array(
                    'show interface :ce_interface',
                    'show ip interface brief',
                    'show interfaces description',    
                    'show ip bgp summary',
                    'ping :pe_ip re 1000',
                    'show version',
                    'show platform hardware throughput level',
                    'show environment',
                    'show standby',   
                    'show run',
                );
            }
            else {
                $command = array(
                    'show configuration',
                    'show interface descriptions',
                    'show configuration interface :ce_interface'
                );
            }
        } else if ($request->network_element == 'upe') {
            $ne_ip = $request->nid_upe_device_id;
            if ($request->vendor == 'huawei') {
                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'display elabel',
                        'display vlan',
                        'display vlan :nid_upe_vlan_1',
                        'display vlan :nid_upe_vlan_2',
                        'display curr int :nid_upe_port_ul_1',
                        'display curr int :nid_upe_port_ul_2',
                        'display mac-add dyn vlan :nid_upe_vlan_1',
                        'display mac-add dyn vlan :nid_upe_vlan_2',
                        'display int :nid_upe_port_ul_1',
                        'display int :nid_upe_port_ul_2',
                        // 'display interface-statistics :nid_upe_port_dl'
                    );
                else
                    $command = array(
                        'display elabel',
                        'display vlan',
                        'display vlan :nid_upe_vlan',
                        'display curr int :nid_upe_port_ul_1',
                        'display mac-add dyn vlan :nid_upe_vlan',
                        'display int :nid_upe_port_ul_1',
                        // 'display interface-statistics :nid_upe_port_dl'
                    );

            }
            else if ($request->vendor == 'alcatel-lucent') {
                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'show version',
                        'show chassis',
                        'show port :nid_upe_port_ul_1',
                        'show port :nid_upe_port_ul_2',
                        'show port :nid_upe_port_dl'
                    );
                else
                    $command = array(
                        'show version',
                        'show chassis',
                        'show port :nid_upe_port_ul_1',
                        'show port :nid_upe_port_dl'
                    );

            }
            else if ($request->vendor == 'zte') {
                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'show version',
                        'sh vlan id :nid_upe_vlan_1',
                        'sh vlan id :nid_upe_vlan_2',
                        'sh run int :nid_upe_port_ul_1',
                        'sh run int :nid_upe_port_ul_2',
                        'sh int :nid_upe_port_ul_1',
                        'sh int :nid_upe_port_ul_2',
                        'sh optical-inform details tx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details tx-power interface :nid_upe_port_ul_2',
                        'sh optical-inform details rx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details rx-power interface :nid_upe_port_ul_2',
                        'sh optical-inform details tx-power interface :nid_upe_port_dl',
                        'sh optical-inform details rx-power interface :nid_upe_port_dl',
                        'sh mac vlan :nid_upe_vlan_1',
                        'sh mac vlan :nid_upe_vlan_2',
                    );
                else
                    $command = array(
                        'show version',
                        'sh vlan id :nid_upe_vlan_1',
                        'sh run int :nid_upe_port_ul_1',
                        'sh int :nid_upe_port_ul_1',
                        'sh optical-inform details tx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details rx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details tx-power interface :nid_upe_port_dl',
                        'sh mac vlan :nid_upe_vlan_1',
                    );

            }

        } else if ($request->network_element == 'nid') {
            if ($request->vendor == 'hfr') {
                
                $ne_ip = $request->nid_upe_device_id;

                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'show fruinfo system',
                        'show mac address-table',
                        'show interface :nid_upe_port_ul_1',
                        'show interface :nid_upe_port_ul_2',
                        'show interface :nid_upe_port_dl',
                        'show running-config interface :nid_upe_port_ul_1',
                        'show running-config interface :nid_upe_port_ul_2',
                        'show running-config interface :nid_upe_port_dl',
                        'show ip int brief',
                        'show transceiver detail | b :nid_upe_port_ul_1',
                        'show transceiver detail | b :nid_upe_port_ul_2',
                        'show transceiver detail | b :nid_upe_port_dl',
                        'show int stat',
                        'show transceiver :nid_upe_port_ul_1',
                        'show transceiver :nid_upe_port_ul_2',
                    );
                else
                    $command = array(
                        'show fruinfo system',
                        'show mac address-table',
                        'show interface :nid_upe_port_ul_1',
                        'show interface :nid_upe_port_dl',
                        'show running-config interface :nid_upe_port_ul_1',
                        'show running-config interface :nid_upe_port_dl',
                        'show ip int brief',
                        'show transceiver detail | b :nid_upe_port_ul_1',
                        'show transceiver detail | b :nid_upe_port_dl',
                        'show int stat',
                        'show transceiver :nid_upe_port_ul_1',
                    );

            }
            else if ($request->vendor == 'raisecom') {
                $ne_ip = $request->nid_upe_device_id;

                // classify command based on type
                if (stripos($request->nid_upe_model, '-C') !== false) {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $command = array(
                            'show version',
                            'show mac-address all',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_ul_2',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_ul_2 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show interface :nid_upe_port_ul_1 configuration',
                            'show interface :nid_upe_port_ul_2 configuration',
                            'show interface :nid_upe_port_dl configuration',
                            // 'show run int :nid_upe_port_ul_1',
                            // 'show run int :nid_upe_port_ul_2',
                            // 'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_ul_2',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_ul_2',
                            'show transceiver information :nid_upe_port_dl'
                        );
                    else
                        $command = array(
                            'show version',
                            'show mac-address all',
                            'show interface',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show interface :nid_upe_port_ul_1 configuration',
                            'show interface :nid_upe_port_dl configuration',
                            // 'show run int :nid_upe_port_ul_1',
                            // 'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_dl'
                        );

                }
                else {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $command = array(
                            'show version',
                            'show mac-address-table l2-address',
                            'show interface',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_ul_2',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_ul_2 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show run int :nid_upe_port_ul_1',
                            'show run int :nid_upe_port_ul_2',
                            'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_ul_2',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_ul_2',
                            'show transceiver information :nid_upe_port_dl'
                        );
                    else
                        $command = array(
                            'show version',
                            'show mac-address-table l2-address',
                            'show interface',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show run int :nid_upe_port_ul_1',
                            'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_dl'
                        );
                }
            }
        } 
        // for NPE
        else if ($request->network_element == 'npe') {
            $ne_ip = $request->npe_device_id;
            if ($request->vendor == 'alcatel-lucent') $command = array('show :npe_port');
            else if ($request->vendor == 'huawei') $command = array('display interface :npe_port', 'display current-configuration interface :npe_interface');
            else if ($request->vendor == 'zte') $command = array('show interface :npe_port', 'show run interface :npe_port');
        } 
        // for EPE
        else if ($request->network_element == 'epe') {
            // if type 5-2 and type 4-2, use 2nd device id
            if ($request->metroe_type == 'TYPE 5-2' || $request->metroe_type == 'TYPE 4-2') $ne_ip = $request->epe_device_id_2;
            else $ne_ip = $request->epe_device_id_1;
            if ($request->vendor == 'alcatel-lucent') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $command = array(
                        'show :epe_port_1',
                        'show :epe_port_2',
                    );
                else $command = array('show :epe_port');
            }
            else if ($request->vendor == 'huawei') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $command = array(
                        'display interface :epe_port_1',
                        'display interface :epe_port_2',
                        'display current-configuration interface :epe_interface_1',
                        'display current-configuration interface :epe_interface_2',
                    );
                else $command = array('display interface :epe_port', 'display current-configuration interface :epe_interface');
            }
            else if ($request->vendor == 'zte') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $command = array(
                        'show interface :epe_port_1',
                        'show interface :epe_port_2',
                        'show run interface :epe_port_1',
                        'show run interface :epe_port_2',
                        // 'show vlan id :epe_vlan_1',
                        // 'show vlan id :epe_vlan_2',
                    );
                else $command = array('show interface :epe_port', 'show run interface :epe_port', 'show vlan id :epe_vlan');
            }
        }


        // Define the placeholders and their corresponding keys in $validatedData
        if ($request->network_element == 'pe') {
            $placeholders = [
                ':pe_device_id' => $request->pe_device_id,
                ':pe_interface' => $request->pe_interface,
                ':pe_ip' => explode("/", $request->pe_ip)[0],
                ':ce_ip' => explode("/", $request->ce_ip)[0],
                ':service_id' => $request->service_id,
                ':vrf' => $request->vrf,
                ':port' => explode(".", $request->pe_interface)[0],
                ':vlan' => explode(".", $request->pe_interface)[1],
            ];
            // for 1GOV
            if ($request->product==='1gov') $placeholders[':policy_name'] = $request->vrf."_".trim(explode("-", $request->ce)[2])."_PRFX_IN";
        } else if ($request->network_element == 'ce') {
            $placeholders = [
                ':ce_interface' => $request->ce_interface,
                ':pe_ip' => explode("/", $request->pe_ip)[0]
            ];
        }
        else if ($request->network_element == 'upe') {
            if ($request->vendor == 'huawei') {

                $mappingInterface = [
                    'FastEthernet 0/1' => 'Ethernet0/0/1',
                    'FastEthernet 0/2' => 'Ethernet0/0/2',
                    'FastEthernet 0/3' => 'Ethernet0/0/3',
                    'FastEthernet 0/4' => 'Ethernet0/0/4',
                    'FastEthernet 0/5' => 'Ethernet0/0/5',
                    'FastEthernet 0/6' => 'Ethernet0/0/6',
                    'FastEthernet 0/7' => 'Ethernet0/0/7',
                    'FastEthernet 0/8' => 'Ethernet0/0/8',
                    'FastEthernet 0/9' => 'Ethernet0/0/9',
                    'FastEthernet 0/10' => 'Ethernet0/0/10',
                    'FastEthernet 0/11' => 'Ethernet0/0/11',
                    'FastEthernet 0/12' => 'Ethernet0/0/12',
                    'FastEthernet 0/13' => 'Ethernet0/0/13',
                    'FastEthernet 0/14' => 'Ethernet0/0/14',
                    'FastEthernet 0/15' => 'Ethernet0/0/15',
                    'FastEthernet 0/16' => 'Ethernet0/0/16',
                    'FastEthernet 0/17' => 'Ethernet0/0/17',
                    'FastEthernet 0/18' => 'Ethernet0/0/18',
                    'FastEthernet 0/19' => 'Ethernet0/0/19',
                    'FastEthernet 0/20' => 'Ethernet0/0/20',
                    'FastEthernet 0/21' => 'Ethernet0/0/21',
                    'FastEthernet 0/22' => 'Ethernet0/0/22',
                    'FastEthernet 0/23' => 'Ethernet0/0/23',
                    'FastEthernet 0/24' => 'Ethernet0/0/24',
                    'GigabitEthernet 0/25' => 'GigabitEthernet0/0/1',
                    'GigabitEthernet 0/26' => 'GigabitEthernet0/0/2',
                    'GigabitEthernet 0/27' => 'GigabitEthernet0/0/3',
                    'GigabitEthernet 0/28' => 'GigabitEthernet0/0/4',
                    'FastEthernet 1/0/1' => 'Ethernet1/0/1',
                    'FastEthernet 1/0/2' => 'Ethernet1/0/2',
                    'FastEthernet 1/0/3' => 'Ethernet1/0/3',
                    'FastEthernet 1/0/4' => 'Ethernet1/0/4',
                    'FastEthernet 1/0/5' => 'Ethernet1/0/5',
                    'FastEthernet 1/0/6' => 'Ethernet1/0/6',
                    'FastEthernet 1/0/7' => 'Ethernet1/0/7',
                    'FastEthernet 1/0/8' => 'Ethernet1/0/8',
                    'FastEthernet 1/0/9' => 'Ethernet1/0/9',
                    'FastEthernet 1/0/10' => 'Ethernet1/0/10',
                    'FastEthernet 1/0/11' => 'Ethernet1/0/11',
                    'FastEthernet 1/0/12' => 'Ethernet1/0/12',
                    'FastEthernet 1/0/13' => 'Ethernet1/0/13',
                    'FastEthernet 1/0/14' => 'Ethernet1/0/14',
                    'FastEthernet 1/0/15' => 'Ethernet1/0/15',
                    'FastEthernet 1/0/16' => 'Ethernet1/0/16',
                    'FastEthernet 1/0/17' => 'Ethernet1/0/17',
                    'FastEthernet 1/0/18' => 'Ethernet1/0/18',
                    'FastEthernet 1/0/19' => 'Ethernet1/0/19',
                    'FastEthernet 1/0/20' => 'Ethernet1/0/20',
                    'FastEthernet 1/0/21' => 'Ethernet1/0/21',
                    'FastEthernet 1/0/22' => 'Ethernet1/0/22',
                    'FastEthernet 1/0/23' => 'Ethernet1/0/23',
                    'FastEthernet 1/0/24' => 'Ethernet1/0/24',
                    'GigabitEthernet 1/1/1' => 'GigabitEthernet1/1/1',
                    'GigabitEthernet 1/1/2' => 'GigabitEthernet1/1/2',
                    'GigabitEthernet 1/1/3' => 'GigabitEthernet1/1/3',
                    'GigabitEthernet 1/1/4' => 'GigabitEthernet1/1/4',
                    'GigabitEthernet 0/1' => 'GigabitEthernet0/0/1',
                    'GigabitEthernet 0/2' => 'GigabitEthernet0/0/2',
                    'GigabitEthernet 0/3' => 'GigabitEthernet0/0/3',
                    'GigabitEthernet 0/4' => 'GigabitEthernet0/0/4',
                    'GigabitEthernet 0/5' => 'GigabitEthernet0/0/5',
                    'GigabitEthernet 0/6' => 'GigabitEthernet0/0/6',
                    'GigabitEthernet 0/7' => 'GigabitEthernet0/0/7',
                    'GigabitEthernet 0/8' => 'GigabitEthernet0/0/8',
                    'GigabitEthernet 0/9' => 'GigabitEthernet0/0/9',
                    'GigabitEthernet 0/10' => 'GigabitEthernet0/0/10',
                    'GigabitEthernet 0/11' => 'GigabitEthernet0/0/11',
                    'GigabitEthernet 0/12' => 'GigabitEthernet0/0/12',
                    'GigabitEthernet 0/13' => 'GigabitEthernet0/0/13',
                    'GigabitEthernet 0/14' => 'GigabitEthernet0/0/14',
                    'GigabitEthernet 0/15' => 'GigabitEthernet0/0/15',
                    'GigabitEthernet 0/16' => 'GigabitEthernet0/0/16',
                    'GigabitEthernet 0/17' => 'GigabitEthernet0/0/17',
                    'GigabitEthernet 0/18' => 'GigabitEthernet0/0/18',
                    'GigabitEthernet 0/19' => 'GigabitEthernet0/0/19',
                    'GigabitEthernet 0/20' => 'GigabitEthernet0/0/20',
                    'GigabitEthernet 0/21' => 'GigabitEthernet0/0/21',
                    'GigabitEthernet 0/22' => 'GigabitEthernet0/0/22',
                    'GigabitEthernet 0/23' => 'GigabitEthernet0/0/23',
                    'GigabitEthernet 0/24' => 'GigabitEthernet0/0/24',
                    'TenGigabitEthernet 0/25' => 'XGigabitEthernet0/1/1',
                    'TenGigabitEthernet 0/26' => 'XGigabitEthernet0/1/2',
                    'Eth-Trunk1' => 'Eth-Trunk1',
                ];

                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                    $placeholders = [
                            ':nid_upe_vlan_1' => explode(".", $request->nid_upe_interface_ul_1)[1],
                            ':nid_upe_vlan_2' => explode(".", $request->nid_upe_interface_ul_2)[1],
                            ':nid_upe_port_ul_1' => $mappingInterface[explode(".", $request->nid_upe_interface_ul_1)[0]],
                            ':nid_upe_port_ul_2' => $mappingInterface[explode(".", $request->nid_upe_interface_ul_2)[0]],
                            ':nid_upe_port_dl' => $mappingInterface[explode(".", $request->nid_upe_interface_dl)[0]]
                        ];
                else 
                    $placeholders = [
                            ':nid_upe_vlan' => explode(".", $request->nid_upe_interface_ul_1)[1],
                            ':nid_upe_port_ul_1' => $mappingInterface[explode(".", $request->nid_upe_interface_ul_1)[0]],
                            ':nid_upe_port_dl' => $mappingInterface[explode(".", $request->nid_upe_interface_dl)[0]]
                        ];
            }
            else if ($request->vendor == 'alcatel-lucent') {
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                    $placeholders = [
                            ':nid_upe_port_ul_1' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_ul_2' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_ul_2)[0])),
                            ':nid_upe_port_dl' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                else 
                    $placeholders = [
                            ':nid_upe_port_ul_1' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_dl' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
            }
            else if ($request->vendor == 'zte') {
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                    $placeholders = [
                        ':nid_upe_vlan_1' => explode(".", $request->nid_upe_interface_ul_1)[1],
                        ':nid_upe_vlan_2' => explode(".", $request->nid_upe_interface_ul_2)[1],
                        ':nid_upe_port_ul_1' => strtolower(explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_ul_2' => strtolower(explode(".", $request->nid_upe_interface_ul_2)[0]),
                        ':nid_upe_port_dl' => strtolower(explode(".", $request->nid_upe_interface_dl)[0])
                    ];
                else 
                    $placeholders = [
                        ':nid_upe_vlan_1' => explode(".", $request->nid_upe_interface_ul_1)[1],
                        ':nid_upe_port_ul_1' => strtolower(explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_dl' => strtolower(explode(".", $request->nid_upe_interface_dl)[0])
                    ];
            }
        }
        else if ($request->network_element == 'nid') {
            if ($request->vendor == 'hfr') 
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $placeholders = [
                        // value transformation eth-1/1.1001 to eth-0-1
                        ':nid_upe_port_ul_1' => strpos($request->nid_upe_interface_ul_1, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_ul_2' => strpos($request->nid_upe_interface_ul_2, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_2)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_2)[0]),
                        ':nid_upe_port_dl' => strpos($request->nid_upe_interface_dl, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]),
                    ];
                else 
                    $placeholders = [
                        // value transformation eth-1/1.1001 to eth-0-1
                        ':nid_upe_port_ul_1' => strpos($request->nid_upe_interface_ul_1, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_dl' => strpos($request->nid_upe_interface_dl, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]),
                    ];
            else if ($request->vendor == 'raisecom') {
                if (stripos($request->nid_upe_model, '-C') !== false) {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $placeholders = [
                            ':nid_upe_port_ul_1' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_ul_2' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_ul_2)[0])),
                            ':nid_upe_port_dl' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                    else 
                        $placeholders = [
                            // value transformation eth-1/1.1001 to eth-0-1
                            ':nid_upe_port_ul_1' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_dl' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                }
                else {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $placeholders = [
                            // value transformation eth-1/1.1001 to eth-0-1
                            ':nid_upe_port_ul_1' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_ul_2' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_ul_2)[0])),
                            ':nid_upe_port_dl' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                    else 
                        $placeholders = [
                            // value transformation eth-1/1.1001 to eth-0-1
                            ':nid_upe_port_ul_1' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_dl' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];

                }
            }
        } else if ($request->network_element == 'npe') {
            if ($request->vendor == 'alcatel-lucent') 
                $placeholders = [
                    // value transformation Port 10/2/4.2270 to Port 10/2/4
                    ':npe_port' => strtolower(explode(".", $request->npe_interface)[0]),
                    // ':npe_interface' => $request->npe_interface
                ];
            else if ($request->vendor == 'huawei'){
                $removedTen = preg_replace('/ten/i', '', $request->npe_interface);
                $placeholders = [
                    // value transformation TenGigabitEthernet 1/0/2.2323 to GigabitEthernet 1/0/2
                    ':npe_port' => strtolower(explode(".", $removedTen)[0]),
                    ':npe_interface' => $removedTen
                ];
            }
            else 
                $placeholders = [
                    // value transformation Port 10/2/4.2270 to Port 10/2/4
                    ':npe_port' => strtolower(explode(".", $request->npe_interface)[0]),
                    ':npe_vlan' => explode(".", $request->npe_interface)[1]
                ];

        } else if ($request->network_element == 'epe') {
            if ($request->vendor == 'alcatel-lucent') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port_1' => strtolower(explode(".", $request->epe_interface_1)[0]),
                        ':epe_port_2' => strtolower(explode(".", $request->epe_interface_2)[0])
                    ];
                else if ($request->metroe_type == 'TYPE 4-1' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_1)[0])
                    ];
                else if ($request->metroe_type == 'TYPE 4-2' || $request->metroe_type == 'TYPE 5-2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_2)[0])
                    ];
            }
            if ($request->vendor == 'zte') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port_1' => strtolower(explode(".", $request->epe_interface_1)[0]),
                        ':epe_port_2' => strtolower(explode(".", $request->epe_interface_2)[0]),
                        ':epe_vlan_1' => strtolower(explode(".", $request->epe_interface_1)[1]),
                        ':epe_vlan_2' => strtolower(explode(".", $request->epe_interface_2)[1])
                    ];
                else if ($request->metroe_type == 'TYPE 4-1' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_1)[0]),
                        ':epe_vlan' => strtolower(explode(".", $request->epe_interface_1)[1])
                    ];
                else if ($request->metroe_type == 'TYPE 4-2' || $request->metroe_type == 'TYPE 5-2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_2)[0]),
                        ':epe_vlan' => strtolower(explode(".", $request->epe_interface_2)[1])
                    ];
            }
            else {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') {
                    $removedTen_1 = preg_replace('/ten/i', '', $request->epe_interface_1);
                    $removedTen_2 = preg_replace('/ten/i', '', $request->epe_interface_2);
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port_1' => strtolower(explode(".", $removedTen_1)[0]),
                        ':epe_port_2' => strtolower(explode(".", $removedTen_2)[0]),
                        ':epe_interface_1' => $removedTen_1,
                        ':epe_interface_2' => $removedTen_2
                    ];
                }
                else if ($request->metroe_type == 'TYPE 4-1' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2') {
                    $removedTen = preg_replace('/ten/i', '', $request->epe_interface_1);
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $removedTen)[0]),
                        ':epe_interface' => $removedTen
                    ];
                } 
                else if ($request->metroe_type == 'TYPE 4-2' || $request->metroe_type == 'TYPE 5-2') {
                    $removedTen = preg_replace('/ten/i', '', $request->epe_interface_2);
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $removedTen)[0]),
                        ':epe_interface' => $removedTen
                    ];
                }
            }
        }

        // Replace placeholders in $command with values from $validatedData
        $modifiedCommands = [];
        foreach ($command as $cmd) {
            $modifiedCmd = str_replace(array_keys($placeholders), array_values($placeholders), $cmd);
            $modifiedCommands[] = $modifiedCmd;
        }
        // remove duplicate commands
        // $modifiedCommands = array_unique($modifiedCommands);
        // Manually remove duplicate elements
        $uniqueArray = [];
        foreach ($modifiedCommands as $element) {
            if (!in_array($element, $uniqueArray)) {
                $uniqueArray[] = $element;
            }
        }
        $modifiedCommands = $uniqueArray;

        // testing purpose
        // return response()->json([
        //     'network_type' => $request->network_element,
        //     'vendor' => $request->vendor,
        //     'ne_ip' => $ne_ip,
        //     'commands' => $modifiedCommands,
        // ]);

        if ($request->vendor == 'juniper' && $request->network_element == 'pe')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_JUNIPER_API_ENDPOINT') . '/pe/v2/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else if ($request->vendor == 'cisco' && $request->network_element == 'ce')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'accept' => 'application/json'
            ])
            // ->post(env('PISA_CISCO_API_ENDPOINT') . '/ce/v1/show_config_multiple_commands/', [
            ->post('http://pisa-cisco-svc/cisco/ce/v1/show_config_multiple_commands/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else if ($request->vendor == 'huawei' && $request->network_element == 'upe')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_HUAWEI_API_ENDPOINT') . '/upe/v1/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else if ($request->vendor == 'alcatel-lucent' && $request->network_element == 'upe')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_NOKIA_API_ENDPOINT') . '/upe/v1/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else if ($request->vendor == 'zte' && $request->network_element == 'upe')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_ZTE_API_ENDPOINT') . '/upe/v1/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else if ($request->vendor == 'raisecom' && $request->network_element == 'nid')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_RAISECOM_API_ENDPOINT') . '/v2/show_config', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else if ($request->vendor == 'hfr' && $request->network_element == 'nid')
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_HFR_API_ENDPOINT') . '/v2/show_config', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        else
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->timeout(120)
            ->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/v1/self_serve/', [
                'network_type' => $request->network_element,
                'vendor' => $request->vendor,
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // Process the response data
            return response()->json([
                'status' => 'success',
                'output' => $response->json()
            ]);
        } else {
            // send the error message
            return response()->json([
                'status' => 'failed',
                'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
                'data' => $response->json(),
                'details' => [
                    'endpoint' => $response->effectiveUri(),
                    'payload' => [
                        'ne_ip' => $ne_ip,
                        'commands' => $modifiedCommands,  
                    ],
                    'response_data' => $response->json()
                ]
            ]);
        }
    }

    // get CE interface
    public function getCeInterface(Request $request) {

        // field to verify
        $inputToValidate = [
            'ce_ip' => ['required', Rule::notIn(['not available'])],
            'vendor' => ['required', Rule::notIn(['not available'])],
            'service_id' => ['required', Rule::notIn(['not available'])],
        ];

        // Validate and sanitize input
        $validatedData = Validator::make($request->all(), $inputToValidate);

        // check if validation failed, send json error messages
        if ($validatedData->fails()) {
            return response()->json([
                'status' => 'failed',
                'output' => $validatedData->errors()->all()
            ]);
        }

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "Retrieve CE interface for service id. (".$request->service_id.")",
            'user_id' => Auth::id(),
            'module' => 'Network Verifications'
        ]);

        // url based on vendor type
        if ($request->vendor == 'juniper') $url = env('PISA_JUNIPER_API_ENDPOINT').'/ce/noproxy/v1/get_interface/';
        else if ($request->vendor == 'cisco') $url = env('PISA_CISCO_API_ENDPOINT').'/ce/noproxy/v1/get_interface/';

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get($url, [
            'ce_ip' => trim(explode("/", $request->ce_ip)[0])
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if status = success
            if ($response->json()['status'] === 'success')
            return response()->json([
                'status' => 'success',
                'output' => $response->json()['output']
            ]);
            else 
            return response()->json([
                'status' => 'failed',
                'output' => $response->json()['output']
            ]);
        } else {
            // send the error message
            return response()->json([
                'status' => 'failed',
                'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
                'data' => $response->json()
            ]);
        }
    }

    // get customer name
    public function getCustomerName() {

       // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_WORKFLOW_API_ENDPOINT').'/database/v1/ogg/get_customer_name', [
            'service_number' => $this->service_id,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['output']==='no data found in db') return 'not available';
            // else return customer name
            else return $response->json()['output']['customer_name'];
        } else {
            // if error return not available
            return 'not available';
        }
    }
}
