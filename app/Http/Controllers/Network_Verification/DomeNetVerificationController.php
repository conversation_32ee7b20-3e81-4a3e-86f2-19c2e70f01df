<?php

namespace App\Http\Controllers\Network_Verification;

use App\Http\Controllers\Controller;
use App\Models\AuditTrail;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Throwable;

class DomeNetVerificationController extends Controller
{
    //
    public function getNeConfig(Request $request)
    {
        // Verify input send for PE
        if ($request->network_element == 'pe') {
            $inputToValidate = [
                'service_id' => ['required', Rule::notIn(['not available'])],
                'pe_device_id' => ['required', Rule::notIn(['not available'])],
                'pe_interface' => ['required', Rule::notIn(['not available'])],
                'pe_ip' => ['required', Rule::notIn(['not available'])],
                'ce_ip' => ['required', Rule::notIn(['not available'])],
                // 'vrf' => ['required', Rule::notIn(['not available'])],
                'vendor' => ['required', Rule::notIn(['not available'])],
                'product' => ['required', Rule::notIn(['not available'])],
                'ce' => ['required', Rule::notIn(['not available'])],
                'pe_import_map' => ['required']
            ];
        }
        // for CE
        else if ($request->network_element == 'ce') {
            $inputToValidate = [
                'ce_ip' => ['required', Rule::notIn(['not available'])], 
                'vendor' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])]
            ];
        }
        // for NID/UPE
        else if ($request->network_element == 'nid' || $request->network_element == 'upe') {
            $inputToValidate = [
                'nid_upe_device_id' => ['required', Rule::notIn(['not available'])],
                'nid_upe_model' => ['required', Rule::notIn(['not available'])],
                'nid_upe_interface_ul_1' => ['required', Rule::notIn(['not available'])],
                'nid_upe_interface_dl' => ['required', Rule::notIn(['not available'])],
                'vendor' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])],
                'upe_type' => ['required', Rule::notIn(['not available']), 'regex:/^TYPE \d+$/']
            ];
            if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') $inputToValidate['nid_upe_interface_ul_2'] = ['required', Rule::notIn(['not available'])];
        }
        // for EPE
        else if ($request->network_element == 'epe') {
            if ($request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 4-1')
                $inputToValidate = [
                    'epe_device_id_1' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_1' => ['required', Rule::notIn(['not available'])],
                    'service_id' => ['required', Rule::notIn(['not available'])],
                    'vendor' => ['required', Rule::notIn(['not available'])]
                ];
            else if ($request->metroe_type == 'TYPE 5-2' || $request->metroe_type == 'TYPE 4-2')
                $inputToValidate = [
                    'epe_device_id_2' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_2' => ['required', Rule::notIn(['not available'])],
                    'service_id' => ['required', Rule::notIn(['not available'])],
                    'vendor' => ['required', Rule::notIn(['not available'])]
                ];
            else if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4')
                $inputToValidate = [
                    'epe_device_id_1' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_1' => ['required', Rule::notIn(['not available'])],
                    'epe_interface_2' => ['required', Rule::notIn(['not available'])],
                    'service_id' => ['required', Rule::notIn(['not available'])],
                    'vendor' => ['required', Rule::notIn(['not available'])]
                ];
        }
        // for NPE
        else if ($request->network_element == 'npe'){
            $inputToValidate = [
                'npe_device_id' => ['required', Rule::notIn(['not available'])],
                'npe_interface' => ['required', Rule::notIn(['not available'])],
                'service_id' => ['required', Rule::notIn(['not available'])],
                'vendor' => ['required', Rule::notIn(['not available'])]];
        }

        // Validate and sanitize input
        $validatedData = Validator::make($request->all(), $inputToValidate);

        // check if validation failed, send json error messages
        if ($validatedData->fails()) {
            return response()->json([
                'status' => 'failed',
                'output' => $validatedData->errors()->all()
            ]);
        }

        // Store activity in Audit Trail
        AuditTrail::create([
            'action' => "Retrieve configurations from ".strtoupper($request->network_element)." for service id. (".$request->service_id.")",
            // ...

                        'user_id' => Auth::id(),
            'module' => 'Network Verifications'
        ]);

        // assign commands to send to ne
        if ($request->network_element == 'pe') {
        // if ($request->network_element == 'pe' && $request->vendor == 'juniper') {
            $ne_ip = $request->pe_device_id;
            $command = array(
                'show interfaces descriptions | match :service_id',
                'show interfaces extensive :pe_interface',
                'show configuration interface :pe_interface',
                // 'show configuration | match :ce_ip | display set',
                'show configuration class-of-service interfaces :port unit :vlan | display set',
                'ping bypass-routing :ce_ip count 5',
                'show configuration protocols bgp group eBGP_IPv4 neighbor :ce_ip',
                // 'show configuration policy-options policy-statement :pe_import_map'
            );
            // check if $request->pe_import_map is not equal to 'not available' for bgp and otherwise for static
            if ($request->pe_import_map != 'not available') $command[] = 'show configuration policy-options policy-statement :pe_import_map';
            else $command[] = 'show configuration routing-options | match :ce_ip | display set';
        // TODO : check if other needed add or not CE Vendor
        } else if ($request->network_element == 'ce') {
            $ne_ip = explode("/", $request->ce_ip)[0];
            if ($request->vendor == 'cisco') {
                $command = array(
                    'show run',
                    'show ip interface brief',
                    'show interfaces description',
                    'show interface :ce_interface',
                    'show ip bgp summary',
                    'ping :pe_ip re 1000',
                    'ping :ce_ip re 1000',
                    'show version',
                    'show platform hardware throughput level',
                    'show environment',
                    'show standby',
                );
            }
            else if ($request->vendor == 'juniper'){
                $command = array(
                    'show configuration',
                    'show interface descriptions',
                    'show configuration interface :ce_interface'
                );
            }
            else if ($request->vendor == '3com'){
                $command = array(
                    'display current-configuration',
                    'display interface brief',
                    'display current-configuration interface'
                );
            }
            else if ($request->vendor == 'huawei'){
                $command = array(
                    'display current-configuration',
                    'display current-configuration | section include interface :pe_interface',
                );
            }

        } else if ($request->network_element == 'upe') {
            $ne_ip = $request->nid_upe_device_id;
            if ($request->vendor == 'huawei') {
                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'display elabel',
                        'display vlan',
                        'display vlan :nid_upe_vlan_1',
                        'display vlan :nid_upe_vlan_2',
                        'display curr int :nid_upe_port_ul_1',
                        'display curr int :nid_upe_port_ul_2',
                        'display mac-add dyn vlan :nid_upe_vlan_1',
                        'display mac-add dyn vlan :nid_upe_vlan_2',
                        'display int :nid_upe_port_ul_1',
                        'display int :nid_upe_port_ul_2',
                        // 'display interface-statistics :nid_upe_port_dl'
                    );
                else
                    $command = array(
                        'display elabel',
                        'display vlan',
                        'display vlan :nid_upe_vlan',
                        'display curr int :nid_upe_port_ul_1',
                        'display mac-add dyn vlan :nid_upe_vlan',
                        'display int :nid_upe_port_ul_1',
                        // 'display interface-statistics :nid_upe_port_dl'
                    );

            }
            else if ($request->vendor == 'alcatel-lucent') {
                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'show version',
                        'show chassis',
                        'show port :nid_upe_port_ul_1',
                        'show port :nid_upe_port_ul_2',
                        'show port :nid_upe_port_dl'
                    );
                else
                    $command = array(
                        'show version',
                        'show chassis',
                        'show port :nid_upe_port_ul_1',
                        'show port :nid_upe_port_dl'
                    );

            }
            else if ($request->vendor == 'zte') {
                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'show version',
                        'sh vlan id :nid_upe_vlan_1',
                        'sh vlan id :nid_upe_vlan_2',
                        'sh run int :nid_upe_port_ul_1',
                        'sh run int :nid_upe_port_ul_2',
                        'sh int :nid_upe_port_ul_1',
                        'sh int :nid_upe_port_ul_2',
                        'sh optical-inform details tx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details tx-power interface :nid_upe_port_ul_2',
                        'sh optical-inform details rx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details rx-power interface :nid_upe_port_ul_2',
                        'sh optical-inform details tx-power interface :nid_upe_port_dl',
                        'sh optical-inform details rx-power interface :nid_upe_port_dl',
                        'sh mac vlan :nid_upe_vlan_1',
                        'sh mac vlan :nid_upe_vlan_2',
                    );
                else
                    $command = array(
                        'show version',
                        'sh vlan id :nid_upe_vlan_1',
                        'sh run int :nid_upe_port_ul_1',
                        'sh int :nid_upe_port_ul_1',
                        'sh optical-inform details tx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details rx-power interface :nid_upe_port_ul_1',
                        'sh optical-inform details tx-power interface :nid_upe_port_dl',
                        'sh mac vlan :nid_upe_vlan_1',
                    );

            }

        } else if ($request->network_element == 'nid') {
            if ($request->vendor == 'hfr') {
                
                $ne_ip = $request->nid_upe_device_id;

                // classify command based on type
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $command = array(
                        'show fruinfo system',
                        'show mac address-table',
                        'show interface :nid_upe_port_ul_1',
                        'show interface :nid_upe_port_ul_2',
                        'show interface :nid_upe_port_dl',
                        'show ip int brief',
                        'show transceiver detail | b :nid_upe_port_ul_1',
                        'show transceiver detail | b :nid_upe_port_ul_2',
                        'show transceiver detail | b :nid_upe_port_dl',
                        'show int stat',
                        'show transceiver :nid_upe_port_ul_1',
                        'show transceiver :nid_upe_port_ul_2',
                    );
                else
                    $command = array(
                        'show fruinfo system detail',
                        'show mac address-table',
                        'show interface :nid_upe_port_ul_1',
                        'show interface :nid_upe_port_dl',
                        'show ip int brief',
                        'show transceiver detail | b :nid_upe_port_ul_1',
                        'show transceiver detail | b :nid_upe_port_dl',
                        'show int stat',
                        'show transceiver :nid_upe_port_ul_1',
                    );

            }
            else if ($request->vendor == 'raisecom') {
                $ne_ip = $request->nid_upe_device_id;

                // classify command based on type
                if (stripos($request->nid_upe_model, '-C') !== false) {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $command = array(
                            'show version',
                            'show mac-address all',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_ul_2',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_ul_2 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show interface :nid_upe_port_ul_1 configuration',
                            'show interface :nid_upe_port_ul_2 configuration',
                            'show interface :nid_upe_port_dl configuration',
                            // 'show run int :nid_upe_port_ul_1',
                            // 'show run int :nid_upe_port_ul_2',
                            // 'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_ul_2',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_ul_2',
                            'show transceiver information :nid_upe_port_dl'
                        );
                    else
                        $command = array(
                            'show version',
                            'show mac-address all',
                            'show interface',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show interface :nid_upe_port_ul_1 configuration',
                            'show interface :nid_upe_port_dl configuration',
                            // 'show run int :nid_upe_port_ul_1',
                            // 'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_dl'
                        );

                }
                else {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $command = array(
                            'show version',
                            'show mac-address-table l2-address',
                            'show interface',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_ul_2',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_ul_2 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show run int :nid_upe_port_ul_1',
                            'show run int :nid_upe_port_ul_2',
                            'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_ul_2',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_ul_2',
                            'show transceiver information :nid_upe_port_dl'
                        );
                    else
                        $command = array(
                            'show version',
                            'show mac-address-table l2-address',
                            'show interface',
                            'show vlan',
                            'show interface :nid_upe_port_ul_1',
                            'show interface :nid_upe_port_dl',
                            'show interface :nid_upe_port_ul_1 statistics',
                            'show interface :nid_upe_port_dl statistics',
                            'show run int :nid_upe_port_ul_1',
                            'show run int :nid_upe_port_dl',
                            'show transceiver ddm :nid_upe_port_ul_1',
                            'show transceiver ddm :nid_upe_port_dl',
                            'show transceiver information :nid_upe_port_ul_1',
                            'show transceiver information :nid_upe_port_dl'
                        );
                }
            }
        } 
        // for NPE
        else if ($request->network_element == 'npe') {
            $ne_ip = $request->npe_device_id;
            if ($request->vendor == 'alcatel-lucent') $command = array('show :npe_port');
            else if ($request->vendor == 'huawei') $command = array('display interface :npe_port', 'display current-configuration interface :npe_interface');
            else if ($request->vendor == 'zte') $command = array('show interface :npe_port', 'show run interface :npe_port');
        } 
        // for EPE
        else if ($request->network_element == 'epe') {
            // if type 5-2 and type 4-2, use 2nd device id
            if ($request->metroe_type == 'TYPE 5-2' || $request->metroe_type == 'TYPE 4-2') $ne_ip = $request->epe_device_id_2;
            else $ne_ip = $request->epe_device_id_1;
            if ($request->vendor == 'alcatel-lucent') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $command = array(
                        'show :epe_port_1',
                        'show :epe_port_2',
                    );
                else $command = array('show :epe_port');
            }
            else if ($request->vendor == 'huawei') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $command = array(
                        'display interface :epe_port_1',
                        'display interface :epe_port_2',
                        'display current-configuration interface :epe_interface_1',
                        'display current-configuration interface :epe_interface_2',
                    );
                else $command = array('display interface :epe_port', 'display current-configuration interface :epe_interface');
            }
            else if ($request->vendor == 'zte') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $command = array(
                        'show interface :epe_port_1',
                        'show interface :epe_port_2',
                        'show run interface :epe_port_1',
                        'show run interface :epe_port_2',
                        // 'show vlan id :epe_vlan_1',
                        // 'show vlan id :epe_vlan_2',
                    );
                else $command = array('show interface :epe_port', 'show run interface :epe_port', 'show vlan id :epe_vlan');
            }
        }


        // Define the placeholders and their corresponding keys in $validatedData
        if ($request->network_element == 'pe') {
            $placeholders = [
                ':pe_device_id' => $request->pe_device_id,
                ':pe_interface' => $request->pe_interface,
                ':pe_ip' => explode("/", $request->pe_ip)[0],
                ':ce_ip' => explode("/", $request->ce_ip)[0],
                ':service_id' => $request->service_id,
                // ':vrf' => $request->vrf,
                ':port' => explode(".", $request->pe_interface)[0],
                ':vlan' => explode(".", $request->pe_interface)[1],
                ':pe_import_map' => $request->pe_import_map
            ];

        } else if ($request->network_element == 'ce') {
            $placeholders = [
                ':ce_interface' => $request->ce_interface,
                ':pe_ip' => explode("/", $request->pe_ip)[0]
            ];
        }
        else if ($request->network_element == 'upe') {
            if ($request->vendor == 'huawei') {

                $mappingInterface = [
                    'FastEthernet 0/1' => 'Ethernet0/0/1',
                    'FastEthernet 0/2' => 'Ethernet0/0/2',
                    'FastEthernet 0/3' => 'Ethernet0/0/3',
                    'FastEthernet 0/4' => 'Ethernet0/0/4',
                    'FastEthernet 0/5' => 'Ethernet0/0/5',
                    'FastEthernet 0/6' => 'Ethernet0/0/6',
                    'FastEthernet 0/7' => 'Ethernet0/0/7',
                    'FastEthernet 0/8' => 'Ethernet0/0/8',
                    'FastEthernet 0/9' => 'Ethernet0/0/9',
                    'FastEthernet 0/10' => 'Ethernet0/0/10',
                    'FastEthernet 0/11' => 'Ethernet0/0/11',
                    'FastEthernet 0/12' => 'Ethernet0/0/12',
                    'FastEthernet 0/13' => 'Ethernet0/0/13',
                    'FastEthernet 0/14' => 'Ethernet0/0/14',
                    'FastEthernet 0/15' => 'Ethernet0/0/15',
                    'FastEthernet 0/16' => 'Ethernet0/0/16',
                    'FastEthernet 0/17' => 'Ethernet0/0/17',
                    'FastEthernet 0/18' => 'Ethernet0/0/18',
                    'FastEthernet 0/19' => 'Ethernet0/0/19',
                    'FastEthernet 0/20' => 'Ethernet0/0/20',
                    'FastEthernet 0/21' => 'Ethernet0/0/21',
                    'FastEthernet 0/22' => 'Ethernet0/0/22',
                    'FastEthernet 0/23' => 'Ethernet0/0/23',
                    'FastEthernet 0/24' => 'Ethernet0/0/24',
                    'GigabitEthernet 0/25' => 'GigabitEthernet0/0/1',
                    'GigabitEthernet 0/26' => 'GigabitEthernet0/0/2',
                    'GigabitEthernet 0/27' => 'GigabitEthernet0/0/3',
                    'GigabitEthernet 0/28' => 'GigabitEthernet0/0/4',
                    'FastEthernet 1/0/1' => 'Ethernet1/0/1',
                    'FastEthernet 1/0/2' => 'Ethernet1/0/2',
                    'FastEthernet 1/0/3' => 'Ethernet1/0/3',
                    'FastEthernet 1/0/4' => 'Ethernet1/0/4',
                    'FastEthernet 1/0/5' => 'Ethernet1/0/5',
                    'FastEthernet 1/0/6' => 'Ethernet1/0/6',
                    'FastEthernet 1/0/7' => 'Ethernet1/0/7',
                    'FastEthernet 1/0/8' => 'Ethernet1/0/8',
                    'FastEthernet 1/0/9' => 'Ethernet1/0/9',
                    'FastEthernet 1/0/10' => 'Ethernet1/0/10',
                    'FastEthernet 1/0/11' => 'Ethernet1/0/11',
                    'FastEthernet 1/0/12' => 'Ethernet1/0/12',
                    'FastEthernet 1/0/13' => 'Ethernet1/0/13',
                    'FastEthernet 1/0/14' => 'Ethernet1/0/14',
                    'FastEthernet 1/0/15' => 'Ethernet1/0/15',
                    'FastEthernet 1/0/16' => 'Ethernet1/0/16',
                    'FastEthernet 1/0/17' => 'Ethernet1/0/17',
                    'FastEthernet 1/0/18' => 'Ethernet1/0/18',
                    'FastEthernet 1/0/19' => 'Ethernet1/0/19',
                    'FastEthernet 1/0/20' => 'Ethernet1/0/20',
                    'FastEthernet 1/0/21' => 'Ethernet1/0/21',
                    'FastEthernet 1/0/22' => 'Ethernet1/0/22',
                    'FastEthernet 1/0/23' => 'Ethernet1/0/23',
                    'FastEthernet 1/0/24' => 'Ethernet1/0/24',
                    'GigabitEthernet 1/1/1' => 'GigabitEthernet1/1/1',
                    'GigabitEthernet 1/1/2' => 'GigabitEthernet1/1/2',
                    'GigabitEthernet 1/1/3' => 'GigabitEthernet1/1/3',
                    'GigabitEthernet 1/1/4' => 'GigabitEthernet1/1/4',
                    'GigabitEthernet 0/1' => 'GigabitEthernet0/0/1',
                    'GigabitEthernet 0/2' => 'GigabitEthernet0/0/2',
                    'GigabitEthernet 0/3' => 'GigabitEthernet0/0/3',
                    'GigabitEthernet 0/4' => 'GigabitEthernet0/0/4',
                    'GigabitEthernet 0/5' => 'GigabitEthernet0/0/5',
                    'GigabitEthernet 0/6' => 'GigabitEthernet0/0/6',
                    'GigabitEthernet 0/7' => 'GigabitEthernet0/0/7',
                    'GigabitEthernet 0/8' => 'GigabitEthernet0/0/8',
                    'GigabitEthernet 0/9' => 'GigabitEthernet0/0/9',
                    'GigabitEthernet 0/10' => 'GigabitEthernet0/0/10',
                    'GigabitEthernet 0/11' => 'GigabitEthernet0/0/11',
                    'GigabitEthernet 0/12' => 'GigabitEthernet0/0/12',
                    'GigabitEthernet 0/13' => 'GigabitEthernet0/0/13',
                    'GigabitEthernet 0/14' => 'GigabitEthernet0/0/14',
                    'GigabitEthernet 0/15' => 'GigabitEthernet0/0/15',
                    'GigabitEthernet 0/16' => 'GigabitEthernet0/0/16',
                    'GigabitEthernet 0/17' => 'GigabitEthernet0/0/17',
                    'GigabitEthernet 0/18' => 'GigabitEthernet0/0/18',
                    'GigabitEthernet 0/19' => 'GigabitEthernet0/0/19',
                    'GigabitEthernet 0/20' => 'GigabitEthernet0/0/20',
                    'GigabitEthernet 0/21' => 'GigabitEthernet0/0/21',
                    'GigabitEthernet 0/22' => 'GigabitEthernet0/0/22',
                    'GigabitEthernet 0/23' => 'GigabitEthernet0/0/23',
                    'GigabitEthernet 0/24' => 'GigabitEthernet0/0/24',
                    'TenGigabitEthernet 0/25' => 'XGigabitEthernet0/1/1',
                    'TenGigabitEthernet 0/26' => 'XGigabitEthernet0/1/2',
                    'Eth-Trunk1' => 'Eth-Trunk1',
                ];

                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                    $placeholders = [
                            ':nid_upe_vlan_1' => explode(".", $request->nid_upe_interface_ul_1)[1],
                            ':nid_upe_vlan_2' => explode(".", $request->nid_upe_interface_ul_2)[1],
                            ':nid_upe_port_ul_1' => $mappingInterface[explode(".", $request->nid_upe_interface_ul_1)[0]],
                            ':nid_upe_port_ul_2' => $mappingInterface[explode(".", $request->nid_upe_interface_ul_2)[0]],
                            ':nid_upe_port_dl' => $mappingInterface[explode(".", $request->nid_upe_interface_dl)[0]]
                        ];
                else 
                    $placeholders = [
                            ':nid_upe_vlan' => explode(".", $request->nid_upe_interface_ul_1)[1],
                            ':nid_upe_port_ul_1' => $mappingInterface[explode(".", $request->nid_upe_interface_ul_1)[0]],
                            ':nid_upe_port_dl' => $mappingInterface[explode(".", $request->nid_upe_interface_dl)[0]]
                        ];
            }
            else if ($request->vendor == 'alcatel-lucent') {
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                    $placeholders = [
                            ':nid_upe_port_ul_1' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_ul_2' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_ul_2)[0])),
                            ':nid_upe_port_dl' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                else 
                    $placeholders = [
                            ':nid_upe_port_ul_1' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_dl' => preg_replace('/port /i', '', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
            }
            else if ($request->vendor == 'zte') {
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                    $placeholders = [
                        ':nid_upe_vlan_1' => explode(".", $request->nid_upe_interface_ul_1)[1],
                        ':nid_upe_vlan_2' => explode(".", $request->nid_upe_interface_ul_2)[1],
                        ':nid_upe_port_ul_1' => strtolower(explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_ul_2' => strtolower(explode(".", $request->nid_upe_interface_ul_2)[0]),
                        ':nid_upe_port_dl' => strtolower(explode(".", $request->nid_upe_interface_dl)[0])
                    ];
                else 
                    $placeholders = [
                        ':nid_upe_vlan_1' => explode(".", $request->nid_upe_interface_ul_1)[1],
                        ':nid_upe_port_ul_1' => strtolower(explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_dl' => strtolower(explode(".", $request->nid_upe_interface_dl)[0])
                    ];
            }
        }
        else if ($request->network_element == 'nid') {
            if ($request->vendor == 'hfr') 
                if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4') 
                    $placeholders = [
                        // value transformation eth-1/1.1001 to eth-0-1
                        ':nid_upe_port_ul_1' => strpos($request->nid_upe_interface_ul_1, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_ul_2' => strpos($request->nid_upe_interface_ul_2, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_2)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_2)[0]),
                        ':nid_upe_port_dl' => strpos($request->nid_upe_interface_dl, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]),
                    ];
                else 
                    $placeholders = [
                        // value transformation eth-1/1.1001 to eth-0-1
                        ':nid_upe_port_ul_1' => strpos($request->nid_upe_interface_ul_1, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_ul_1)[0]),
                        ':nid_upe_port_dl' => strpos($request->nid_upe_interface_dl, 'eth') !== false ? str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]) : 'eth-'.str_replace('1/', '0-', explode(".", $request->nid_upe_interface_dl)[0]),
                    ];
            else if ($request->vendor == 'raisecom') {
                if (stripos($request->nid_upe_model, '-C') !== false) {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $placeholders = [
                            ':nid_upe_port_ul_1' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_ul_2' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_ul_2)[0])),
                            ':nid_upe_port_dl' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                    else 
                        $placeholders = [
                            // value transformation eth-1/1.1001 to eth-0-1
                            ':nid_upe_port_ul_1' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_dl' => preg_replace(['/tge/', '/ge/'], ['tengigabitethernet ', 'gigaethernet '], strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                }
                else {
                    if ($request->upe_type == 'TYPE 2' || $request->upe_type == 'TYPE 4')
                        $placeholders = [
                            // value transformation eth-1/1.1001 to eth-0-1
                            ':nid_upe_port_ul_1' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_ul_2' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_ul_2)[0])),
                            ':nid_upe_port_dl' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];
                    else 
                        $placeholders = [
                            // value transformation eth-1/1.1001 to eth-0-1
                            ':nid_upe_port_ul_1' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_ul_1)[0])),
                            ':nid_upe_port_dl' => preg_replace('/([a-zA-Z])(\d+)/', '$1 $2', strtolower(explode(".", $request->nid_upe_interface_dl)[0])),
                        ];

                }
            }
        } else if ($request->network_element == 'npe') {
            if ($request->vendor == 'alcatel-lucent') 
                $placeholders = [
                    // value transformation Port 10/2/4.2270 to Port 10/2/4
                    ':npe_port' => strtolower(explode(".", $request->npe_interface)[0]),
                    // ':npe_interface' => $request->npe_interface
                ];
            else if ($request->vendor == 'huawei'){
                $removedTen = preg_replace('/ten/i', '', $request->npe_interface);
                $placeholders = [
                    // value transformation TenGigabitEthernet 1/0/2.2323 to GigabitEthernet 1/0/2
                    ':npe_port' => strtolower(explode(".", $removedTen)[0]),
                    ':npe_interface' => $removedTen
                ];
            }
            else 
                $placeholders = [
                    // value transformation Port 10/2/4.2270 to Port 10/2/4
                    ':npe_port' => strtolower(explode(".", $request->npe_interface)[0]),
                    ':npe_vlan' => explode(".", $request->npe_interface)[1]
                ];

        } else if ($request->network_element == 'epe') {
            if ($request->vendor == 'alcatel-lucent') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port_1' => strtolower(explode(".", $request->epe_interface_1)[0]),
                        ':epe_port_2' => strtolower(explode(".", $request->epe_interface_2)[0])
                    ];
                else if ($request->metroe_type == 'TYPE 4-1' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_1)[0])
                    ];
                else if ($request->metroe_type == 'TYPE 4-2' || $request->metroe_type == 'TYPE 5-2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_2)[0])
                    ];
            }
            if ($request->vendor == 'zte') {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port_1' => strtolower(explode(".", $request->epe_interface_1)[0]),
                        ':epe_port_2' => strtolower(explode(".", $request->epe_interface_2)[0]),
                        ':epe_vlan_1' => strtolower(explode(".", $request->epe_interface_1)[1]),
                        ':epe_vlan_2' => strtolower(explode(".", $request->epe_interface_2)[1])
                    ];
                else if ($request->metroe_type == 'TYPE 4-1' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_1)[0]),
                        ':epe_vlan' => strtolower(explode(".", $request->epe_interface_1)[1])
                    ];
                else if ($request->metroe_type == 'TYPE 4-2' || $request->metroe_type == 'TYPE 5-2') 
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $request->epe_interface_2)[0]),
                        ':epe_vlan' => strtolower(explode(".", $request->epe_interface_2)[1])
                    ];
            }
            else {
                if ($request->metroe_type == 'TYPE 13' || $request->metroe_type == 'TYPE 4') {
                    $removedTen_1 = preg_replace('/ten/i', '', $request->epe_interface_1);
                    $removedTen_2 = preg_replace('/ten/i', '', $request->epe_interface_2);
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port_1' => strtolower(explode(".", $removedTen_1)[0]),
                        ':epe_port_2' => strtolower(explode(".", $removedTen_2)[0]),
                        ':epe_interface_1' => $removedTen_1,
                        ':epe_interface_2' => $removedTen_2
                    ];
                }
                else if ($request->metroe_type == 'TYPE 4-1' || $request->metroe_type == 'TYPE 5-1' || $request->metroe_type == 'TYPE 1' || $request->metroe_type == 'TYPE 2') {
                    $removedTen = preg_replace('/ten/i', '', $request->epe_interface_1);
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $removedTen)[0]),
                        ':epe_interface' => $removedTen
                    ];
                } 
                else if ($request->metroe_type == 'TYPE 4-2' || $request->metroe_type == 'TYPE 5-2') {
                    $removedTen = preg_replace('/ten/i', '', $request->epe_interface_2);
                    $placeholders = [
                        // value transformation Port 10/2/4.2270 to Port 10/2/4
                        ':epe_port' => strtolower(explode(".", $removedTen)[0]),
                        ':epe_interface' => $removedTen
                    ];
                }
            }
        }

        // Replace placeholders in $command with values from $validatedData
        $modifiedCommands = [];
        foreach ($command as $cmd) {
            $modifiedCmd = str_replace(array_keys($placeholders), array_values($placeholders), $cmd);
            $modifiedCommands[] = $modifiedCmd;
        }
        // remove duplicate commands
        // $modifiedCommands = array_unique($modifiedCommands);
        // Manually remove duplicate elements
        $uniqueArray = [];
        foreach ($modifiedCommands as $element) {
            if (!in_array($element, $uniqueArray)) {
                $uniqueArray[] = $element;
            }
        }
        $modifiedCommands = $uniqueArray;

        // testing purpose
        // return response()->json([
        //     'network_type' => $request->network_element,
        //     'vendor' => $request->vendor,
        //     'ne_ip' => $ne_ip,
        //     'commands' => $modifiedCommands,
        // ]);

        if ($request->vendor == 'juniper' && $request->network_element == 'pe'){

            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_JUNIPER_API_ENDPOINT') . '/pe/v2/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        else if ($request->vendor == 'huawei' && $request->network_element == 'upe'){
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_HUAWEI_API_ENDPOINT') . '/upe/v1/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        else if ($request->vendor == 'alcatel-lucent' && $request->network_element == 'upe'){
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_NOKIA_API_ENDPOINT') . '/upe/v1/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        else if ($request->vendor == 'zte' && $request->network_element == 'upe'){
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_ZTE_API_ENDPOINT') . '/upe/v1/show_config/', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        else if ($request->vendor == 'raisecom' && $request->network_element == 'nid'){
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_RAISECOM_API_ENDPOINT') . '/v2/show_config', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        else if ($request->vendor == 'hfr' && $request->network_element == 'nid'){
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post(env('PISA_HFR_API_ENDPOINT') . '/v2/show_config', [
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        else{
            // get config from PISA
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->timeout(120)
            ->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/v1/self_serve/', [
                'network_type' => $request->network_element,
                'vendor' => $request->vendor,
                'ne_ip' => $ne_ip,
                'commands' => $modifiedCommands,        
            ]);
        }

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // Process the response data
            return response()->json([
                'status' => 'success',
                'output' => $response->json()
            ]);
        } else {
            // send the error message
            return response()->json([
                'status' => 'failed',
                'output' => 'PISA API: Unexpected HTTP status (' . $response->status() . ', ' . $response->reason() . ')',
                'data' => $response->json()
            ]);
        }
    }
}
