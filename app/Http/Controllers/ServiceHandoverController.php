<?php

namespace App\Http\Controllers;

use App\Models\AuditTrail;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Throwable;
use Exception;

class ServiceHandoverController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        return view('self-serve.service-testing-handover.service-handover.index');
    }

    protected $service_id;

    // Get Service Info from UNIS
    public function show(Request $request)
    {

        // validate input format
        $request->validate([
            'service_id' => 'required|alpha_num',  // only allow alphanumeric, symbols are not allowed
        ]);

        try {

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . env('SELFSERVE_UNIS_BEARER_TOKEN'),
                'Content-Type' => 'application/json',
            ])->post(env('SELFSERVE_UNIS_API_ENDPOINT'), [
                "NIGRequest" => [
                    "Layer" => "BOTH",
                    "graniteid" => $request->service_id,
                    "Qscenario" => "POSITIVE",
                    "serviceType" => ""
                ]
            ]);

            // Check if the request was successful (status code 2xx)
            if ($response->successful()) {
                // Get the JSON response body as an array
                $data = $response->json();

                // Check if UNIS API returns no data
                if (strpos($data['NIGResponse']['ErrorMesage'], 'no data found') !== false) throw new \Exception(__('No matching Service Id. in UNIS'));

                // PS1095261295
                // PS1093105554
                // PS1097572874
                // PS1093086947 - ada vlan - Static
                // PS1095929168 - takda vlan - BGP
                // PS1044911569 - takda vlan - global ipvpn
                // PS1018857975 - takda vlan - BGP
                // PS1085553511 - takda vlan - BGP
                // PS1096799617
                // PS1093086947


                // initialize array value with not available
                $svcInfo = array(
                    'service_id_ps_bs' => 'not available',
                    'service_id_lt' => 'not available',
                    'parent_services' => 'not available',
                    'vrf_name' => 'not available',
                    'tm_lan_ip' => [],
                    'customer_name' => 'not available',
                    'tm_node' => 'not available',
                    'interface' => 'not available',
                    'tm_wan_ipv4' => 'not available',
                    'ce_wan_ipv4' => 'not available',
                    'tm_wan_ipv6' => 'not available',
                    'ce_wan_ipv6' => 'not available',
                    'bandwidth' => 'not available',
                    'burstable' => 'No',
                    'nid_upe' => 'not available',
                    'epe' => 'not available',
                    'epe_2' => 'not available',
                    'npe' => 'not available',
                    'remote_as_number' => 'not available',
                    'protocol' => 'not available'
                );

                // if value exist in API, replace the initial values
                $svcInfo['service_id_ps_bs'] = $request->service_id;

                // Checking Layer 2 Information
                if (isset($data['NIGResponse']['layer2'])) {

                    foreach ($data['NIGResponse']['layer2'] as $info) {
                        // General Info
                        if ($info['name'] === 'Name' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) $svcInfo['L2_id'] = $info['value'];
                        else if ($info['name'] === 'TECHNICAL SLA/SLG' && $info['field'] === 'L2 Service' && $info['group'] === $request->service_id) $svcInfo['sla_slg'] = $info['value'];
                        else if ($info['name'] === 'UPE Type Physical Group' && $info['field'] === 'UPE' && $info['group'] === $request->service_id && strpos($info['value'], 'TYPE') !== false) $svcInfo['upe_type'] = $info['value'];
                    }

                    foreach ($data['NIGResponse']['layer2'] as $info) {

                        if ($info['name'] === 'Device Name' && $info['group'] === $svcInfo['L2_id']) $deviceName_layer2 = $info['value'];
                        if ($info['name'] === 'Interface Name' && $info['type'] === 'Layer2' && $info['field'] === 'NPE DOWNLINK' && $info['group'] === $svcInfo['L2_id']) $port_detail_layer2 = $info['value'];
                        if ($info['name'] === 'Device Name' && $info['field'] === 'NPE UPLINK' && $info['group'] === $svcInfo['L2_id'])  $svcInfo['npe'] = $info['value'];
                        if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 1' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe'] = $info['value'];
                        if ($info['name'] === 'Device Name' && $info['field'] === 'EPE DOWNLINK 2' && $info['group'] === $svcInfo['L2_id']) $svcInfo['epe_2'] = $info['value'];
                        if ($info['name'] === 'Device Name' && $info['field'] === 'UPE' && $info['group'] === $request->service_id) $svcInfo['nid_upe'] = $info['value'];
                    }

                }

                // Checking Layer 3 Information
                if (isset($data['NIGResponse']['layer3'])) {

                    foreach ($data['NIGResponse']['layer3'] as $info) {
                        
                        if ($info['name'] === 'Name' && $info['field'] === 'Redundancy Group') $svcInfo['service_id_lt'] = $info['value'];
                        if ($info['name'] === 'Parent Services' && $info['type'] === 'IP TRANSIT' && $info['group'] === $request->service_id) $svcInfo['parent_services'] = $info['value'];
                        if ($info['name'] === 'VRF Name' && $info['field'] === 'MPLSEndPoint' && $info['group'] === $request->service_id) $svcInfo['vrf_name'] = $info['value'];
                        if ($info['name'] === 'Device Name' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['tm_node'] = $info['value'];
                        if ($info['name'] === 'Interface Name' && $info['type'] === 'PE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $port_detail_layer3 = $info['value'];
                        if ($info['name'] === 'Logical Interface' && $info['type'] === 'PE' && $info['field'] === 'Logical Interface' && $info['group'] === $request->service_id) $svcInfo['vlan'] = $info['value'];
                        if ($info['name'] === 'Bandwidth' && $info['group'] === $request->service_id) $svcInfo['bandwidth'] = $info['value'];
                        if ($info['name'] === 'TAG' && $info['type'] === 'L3 Service' && $info['group'] === $request->service_id) $svcInfo['burstable'] = $info['value'];
                        if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['tm_wan_ipv4'] = $info['value'];
                        if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'PE' && $info['group'] === $request->service_id) $svcInfo['tm_wan_ipv6'] = $info['value'];
                        if ($info['name'] === 'CIDR' && $info['group'] === $request->service_id) $cidr = $info['value'];
                        if ($info['name'] === 'CIDR IPv6' && $info['group'] === $request->service_id) $cidr_ipv6 = $info['value'];
                        if ($info['name'] === 'IP Address (WAN)' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_wan_ipv4'] = $info['value'];
                        if ($info['name'] === 'IP Address (WAN) IPv6' && $info['type'] === 'CE' && $info['group'] === $request->service_id) $svcInfo['ce_wan_ipv6'] = $info['value'];
                        if ($info['name'] === 'Routing Protocol' && $info['group'] === $request->service_id) $svcInfo['protocol'] = $info['value'];
                        if ($info['name'] === 'Remote AS Number' && $info['group'] === $request->service_id && $info['field'] === 'BGP Neighbor') $svcInfo['remote_as_number'] = $info['value'];
                        if ($info['name'] === 'IP Static Route' && $info['group'] === $request->service_id && $info['field'] === 'IP Static Route') {        // Extract only the IP and mask portion (*******/29) from the value
                            if (preg_match('/(\d+\.\d+\.\d+\.\d+\/\d+)/', $info['value'], $matches)) {
                                $ip_static_route = $matches[1];
                                
                                // Append the IP static route to the tm_lan_ip array
                                $svcInfo['tm_lan_ip'][] = $ip_static_route;
                            }
                        }

                    }

                    // Add CIDR to IPv4 and IPv6
                    if ($svcInfo['tm_wan_ipv4'] !== 'not available') $svcInfo['tm_wan_ipv4'] .= '/' . $cidr;
                    if ($svcInfo['tm_wan_ipv6'] !== 'not available') $svcInfo['tm_wan_ipv6'] .= '/' . $cidr_ipv6;
                    if ($svcInfo['ce_wan_ipv4'] !== 'not available') $svcInfo['ce_wan_ipv4'] .= '/' . $cidr;
                    if ($svcInfo['ce_wan_ipv6'] !== 'not available') $svcInfo['ce_wan_ipv6'] .= '/' . $cidr_ipv6;

                }

                // Re-assign PE/AGG information
                if (stripos($deviceName_layer2, 'agg') !== false) {
                    $svcInfo['tm_node'] = $deviceName_layer2;
                    $svcInfo['interface'] = $port_detail_layer2;

                    if (stripos($deviceName_layer2, 'zt') !== false) $svcInfo['pe_vendor'] = 'ZTE';
                    else if (stripos($deviceName_layer2, 'hw') !== false) $svcInfo['pe_vendor'] = 'HUAWEI';
                    else if (stripos($deviceName_layer2, 'nk') !== false) $svcInfo['pe_vendor'] = 'NOKIA';
                    else $svcInfo['pe_vendor'] = $svcInfo['pe_vendor'];

                } else {

                    // Check if $port_detail_layer3 contains a `.`
                    if (strpos($port_detail_layer3, '.') !== false) {
                        $port_parts = explode('.', $port_detail_layer3);
                        $port_detail_layer3 = $port_parts[0];  // Get the part before the `.`
                    }

                    $svcInfo['tm_node'] = $svcInfo['tm_node'];
                    // Extract the number from the value
                    $vlan = $this->extractNumberFromValue($svcInfo['vlan']);

                    if ($port_detail_layer3 !== 'not available' && $vlan !== null) {
                        $port_detail_layer3 .= '.' . $vlan;
                    }
                    $svcInfo['interface'] = $port_detail_layer3;

                }


                $this->service_id = $svcInfo['service_id_ps_bs'];
                $svcInfo['customer_name'] = $this->getCustomerName();

                // add success message
                $svcInfo['success'] = 'Successfully retrieved info from UNIS';

                // sort array alphabetically
                ksort($svcInfo);

                // Store activity in Audit Trail
                AuditTrail::create([
                    'action' => "Searched service info for service id. (" . $request->service_id . ")",
                    'user_id' => Auth::id(),
                    'module' => 'Service Handover'
                ]);

                // dd($svcInfo);

                // if success, redirect with data
                return redirect()->back()->with('data', $svcInfo);

            } else {
                // Handle non-2xx status code responses
                return redirect()->back()->with('error', $response->body());
            }

        } catch (Throwable $error) {
            return redirect()->back()->with('error', $error->getMessage());
        }

    }


    // get customer name
    public function getCustomerName() {

        // get config from PISA
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
        ])
        ->get(env('PISA_WORKFLOW_API_ENDPOINT').'/database/v1/ogg/get_customer_name', [
            'service_number' => $this->service_id,
        ]);

        // Check if the request was successful (status code 2xx)
        if ($response->successful()) {
            // if not found return not available
            if ($response->json()['output']==='no data found in db') return 'not available';
            // else return customer name
            else return $response->json()['output']['customer_name'];
        } else {
            // if error return not available
            return 'not available';
        }
    }

    public function extractNumberFromValue($value)
    {
        // Check if the string contains `-.`
        if (strpos($value, '-.') !== false) {
            $pattern = '/\d+$/';  // This pattern matches the digits at the end of the string
            preg_match($pattern, $value, $matches);
            
            return $matches[0] ?? null;  // Return the matched value or null if no match is found
        }
        
        // If the string contains `-` but not `-.`, return null
        if (strpos($value, '-') !== false) {
            return null;
        }
        
        // If the string contains a `.` but not `-`, do nothing
        if (strpos($value, '.') !== false) {
            return null;
        }
    
        return null;  // Default return null if no `-.` or `-` is found
    }


    // Get Filename in storage 
    // public function retrieve_log_testing($service_id)
    // {
    //     // Construct the base path
    //     $basePath = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $service_id);
        
    //     // Debug: Print the base path
    //     // You can use Log::info or dd() to output the path
    //     // \Log::info("Checking path: $basePath");
    //     // dd($basePath);
    
    //     if (!File::exists($basePath)) {
    //         return response()->json(['error' => 'Service ID directory not found'], 404);
    //     }
    
    //     // Get all directories under the service_id path
    //     $directories = File::directories($basePath);
    
    //     // Prepare an array to hold the text file names
    //     $textFiles = [];
    
    //     foreach ($directories as $directory) {
    //         // Get all text files in the current directory
    //         $files = File::files($directory);
            
    //         foreach ($files as $file) {
    //             if ($file->getExtension() === 'txt') {
    //                 $textFiles[] = $file->getFilename();
    //             }
    //         }
    //     }
    
    //     if (empty($textFiles)) {
    //         return response()->json(['error' => 'No text files found'], 404);
    //     }
    
    //     return response()->json($textFiles);
    // }

    public function retrieve_log_testing($service_id)
    {
        // Construct the base path
        $basePath = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $service_id);
        
        // Debug: Print the base path
        // You can use Log::info or dd() to output the path
        // \Log::info("Checking path: $basePath");
        // dd($basePath);
    
        if (!File::exists($basePath)) {
            return response()->json(['error' => 'Service ID directory not found'], 404);
        }
    
        // Define the expected directories and corresponding JSON keys
        $expectedDirectories = [
            'interface_status' => null,
            'ping_test_pe_ce' => null,
            'ping_test_gipvpn_pe_ce' => null,
            'route_summary' => null,
            'bgp_status' => null,
            // Add more directories and keys as needed
        ];
    
        // Prepare the array to hold the text file names
        $textFiles = [];
    
        foreach ($expectedDirectories as $key => $value) {
            $directoryPath = $basePath . DIRECTORY_SEPARATOR . $key;
            if (File::exists($directoryPath)) {
                // Get all text files in the current directory
                $files = File::files($directoryPath);
                
                foreach ($files as $file) {
                    if ($file->getExtension() === 'txt') {
                        $textFiles[$key] = $file->getFilename();
                        break; // Assuming you need only one file per directory
                    }
                }
            }
        }
    
        if (empty($textFiles)) {
            return response()->json(['error' => 'No text files found'], 404);
        }
    
        return response()->json($textFiles);
    }


    public function download_log($service_id, $testing_type, $file_name)
    {
        $basePath = storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . 'service_testing' . DIRECTORY_SEPARATOR . 'log' . DIRECTORY_SEPARATOR . $service_id . DIRECTORY_SEPARATOR . $testing_type);
        $filePath = $basePath . DIRECTORY_SEPARATOR . $file_name; // Assuming the file name matches the log type

        if (File::exists($filePath)) {
            return response()->download($filePath);
        } else {
            return response()->json(['error' => 'File not found'], 404);
        }
    }

}
