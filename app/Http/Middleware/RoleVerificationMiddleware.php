<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RoleVerificationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle($request, Closure $next, ...$roles)
    {
        // Check if the user is authenticated
        if (Auth::check()) {
            // Check if the user's role matches any of the specified roles
            if (in_array(Auth::user()->role, $roles)) {
                return $next($request);
            }
        }

        // Redirect to a 403 error page
        abort(403, 'Unauthorized access');

        // The following line will not be executed, as 'abort' will terminate the request
        return redirect('/'); // You can remove this line if 'abort' is used
    }
}
