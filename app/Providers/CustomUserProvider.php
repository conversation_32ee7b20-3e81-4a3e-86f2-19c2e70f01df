<?php

// app/Providers/CustomUserProvider.php

namespace App\Providers;

use Illuminate\Auth\EloquentUserProvider as BaseUserProvider;
use Illuminate\Support\Facades\Http;

class CustomUserProvider extends BaseUserProvider
{
    /**
     * Validate a user against the given credentials.
     *
     * @param  mixed  $user
     * @param  array  $credentials
     * @return bool
     */
    public function validateCredentials($user, array $credentials)
    {
        $username = $credentials['staff_id'];
        $password = $credentials['password'];

        // Authenticate with LDAP
        $authenticated = $this->validateAccessLdap($username, $password);

        // check if LDAP API returns error message
        if ($authenticated['output'] !== 'Successfully authenticated') {
            return false;
        }

        // Add custom logic to validate the user's credentials (e.g., invoke an authentication API)
        // For now, we'll just return true
        return true;
    }

    // validate LDAP using NSE Service API
    public function validateAccessLdap($username, $password)
    {        
        $response = Http::withHeaders([
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post(env('PISA_WORKFLOW_API_ENDPOINT') . '/ldap/v2/authenticate', [
            'username' => $username,
            'password' => $password
        ]);

        return $response->json();

        // Failed authentication
        // {
        //     "success": false,
        //     "message": "User not authenticated",
        //     "status": 401,
        //     "data": {}
        // }

        // Failed authentication
        // {
        //      "success": true,
        //      "message": "User authenticated",
        //      "status": 200,
        //      "data": {}
        // }


    }
}
