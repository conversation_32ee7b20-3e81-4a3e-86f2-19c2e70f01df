<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OggData extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'orderNo',
        'orderType',
        'product',
        'orderStatus',
        'activityName',
        'activityStatus',
        'serviceId',
        'account',
        'activityCreated',
        'aging',
        'owner',
        'ownerUnit'
    ];

}
