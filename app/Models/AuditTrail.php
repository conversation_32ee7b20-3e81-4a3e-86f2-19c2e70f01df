<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AuditTrail extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'module'
        // Add other fillable attributes as needed
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
