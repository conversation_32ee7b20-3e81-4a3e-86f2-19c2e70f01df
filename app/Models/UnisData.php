<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UnisData extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'CustAbbr',
        'ServiceId',
        'Product',
        'Bandwidth',
        'RedundancyId',
        'ParentId',
        'PathStatus',
        'ServiceType',
        'Npe',
        'NpeInterface',
        'Pe',
        'PeIp',
        'PeInterface',

        // Additional columns from JSON
        'L2CustAbbr',
        'ParentServiceId',
        'L2ServiceId',
        'L2Product',
        'L2Bandwidth',
        'L2RedundancyId',
        'L2ParentSvc',
        'L2PathStatus',
        'L2ServiceType',
        'UpeNidDlink',
        'UpeNidDlinkInterface',
        'UpeNidUlinkPri',
        'UpeNidUlinkPriInterface',
        'UpeNidUlinkSec',
        'UpeNidUlinkSecInterface',
        'EpePri',
        'EpePriInterface',
        'EpeSec',
        'EpeSecInterface',

        'CeIp',
        'PeDeviceId',
        'Cidr',
    ];
}
