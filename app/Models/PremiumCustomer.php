<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PremiumCustomer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'abbreviation',
        'path',
        'account',
        'logo'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
