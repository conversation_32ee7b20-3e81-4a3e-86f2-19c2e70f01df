<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NotifyNewApprovedUser extends Mailable
{
    use Queueable, SerializesModels;

    public $username;
    public $fullname;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($username, $fullname)
    {
        $this->username = $username;
        $this->fullname = $fullname;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('email.notify-new-approved-user')
        ->subject('Request to access Self-Serve App. using username ('.strtoupper($this->username).') has been approved')
        ->with([
            'username'=>$this->username,
            'fullname'=>$this->fullname,
        ]);
    }
}
