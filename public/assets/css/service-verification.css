/* public/assets/css/service-verification.css */

/* Verification Button Styles */
.verification-btn, .health-btn {
    border-radius: 5px;
    font-weight: bold;
    padding: 8px 20px;
    min-width: 160px;
    text-align: center;
}

.btn-purple {
    background-color: #4B2E83;
    color: white;
}

/* Service Details Box */
.detail-container {
    padding: 0 15px;
    border-right: 1px dotted #dee2e6;
}

.detail-container:last-child {
    border-right: none;
}

.service-details-box {
    position: relative;
}

.detail-item {
    margin-bottom: 8px;
}

.detail-label {
    display: block;
    font-weight: bold;
    color: #444;
}

.detail-value {
    font-weight: 500;
}

.detail-button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* MVP Badge */
.mvp-badge {
    position: absolute;
    right: 70px;
    top: -20px;
    background-color: #00BFFF;
    color: white;
    padding: 15px;
    border-radius: 50%;
    font-weight: bold;
    font-size: 1.2rem;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(15deg);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.5);
    z-index: 10;
}

.mvp-badge:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #00BFFF;
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.4;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

/* Summary Section */
.summary-list {
    list-style-type: disc;
    padding-left: 20px;
}

.summary-list li {
    margin-bottom: 10px;
}

/* Network Diagram */
.network-topology {
    width: 100%;
    overflow-x: auto;
}

.topology-diagram {
    min-width: 320px;
    padding: 10px 0;
}

.node {
    flex-shrink: 0;
}

.node-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2px solid #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
}

.connection-line {
    flex-shrink: 1;
    min-width: 20px;
    height: 3px;
    background-color: #6c757d;
    align-self: center;
}

/* Media queries for different screen sizes */
@media (max-width: 767px) {
    .topology-diagram {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 15px;
    }
    
    .network-content [style*="border-right"] {
        border-right: none !important;
        border-bottom: 1px dotted #dee2e6;
        margin-bottom: 15px;
        padding-bottom: 15px;
    }
    
    .node-circle {
        width: 40px;
        height: 40px;
        font-size: 10px;
    }
    
    .connection-line {
        min-width: 15px;
        height: 2px;
        background-color: #6c757d;
    }
}

@media (max-width: 575px) {
    .topology-container {
        padding: 0;
    }
    
    .network-topology {
        margin: 0 -15px;
    }
}

/* Text colors */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Form elements */
.service-id-value {
    background-color: #f8f9fa;
    font-weight: bold;
}

.submit-btn {
    min-width: 100px;
}

/* Network Design Section */
.network-header {
    background-color: #3498db;
}

.sla-info p {
    margin-bottom: 5px;
}

/* Modal styles */
.modal-body .table {
    margin-bottom: 0;
}

.modal-body .table td {
    padding: 8px 12px;
    vertical-align: middle;
}

.modal-body .table th {
    background-color: #f8f9fa;
    font-weight: 500;
}

.modal-body .bg-light {
    font-weight: 500;
}


/* Modal responsive styles */
#networkDetailsModal .table-responsive {
    max-height: 70vh;
}

#networkDetailsModal .field-column {
    min-width: 130px;
}

#networkDetailsModal .modal-body {
    padding: 0;
}



#networkDetailsModal .table {
    margin-bottom: 0;
}



#networkDetailsModal .table td {
    padding: 8px 12px;
    vertical-align: middle;
    word-break: break-word;
}

@media (max-width: 767px) {
    #networkDetailsModal .modal-dialog {
        margin: 0.5rem;
        max-width: 95%;
    }
    
    #networkDetailsModal .table td,
    #networkDetailsModal .table th {
        padding: 6px 8px;
        font-size: 0.85rem;
    }
    
    #networkDetailsModal .field-column {
        min-width: 100px;
    }
}

.text-purple {
    color: #6f42c1;
}

.font-weight-bold {
    font-weight: 600;
}

/* Styles for L2/L3 headers */
.header-container {
    font-weight: 500;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #dee2e6;
}

/* L3 Header - Medium Blue */
.header-container.l3-header {
    background-color: #251fb0; /* Medium blue - distinctly blue but not too dark */
    color: #251fb0; /* Dark blue text */
}

/*L2 Header Options - Clearly Different Colors */
/* Option 1: Soft Green */
.header-container.l2-header {
    background-color: #16a085; /* Soft green that pairs well with blue*/
    color: #0f5132; /* Dark green text */
}

/* Option 2: Lavender
.header-container.l2-header {
    background-color: #c5b3e6; /* Distinct lavender - visibly different from blue 
    color: #4c0099; /* Dark purple text
} */

/* Option 3: Soft Amber/Gold
.header-container.l2-header {
    background-color: #ffe69c; /* Soft amber/gold - complementary to blue 
    color: #664d03; /* Dark amber text 
} */

/* Option 4: Muted Cyan */
/* .header-container.l2-header {
    background-color: #9eeaf9; /* Bright cyan - distinct from blue but still in same family 
    color: #055160; /* Dark cyan text
} */

/* Ensure text is white on all headers */
.header-container h5 {
    color: white;
}

/* Update hover effect for better accessibility */
.header-container:hover {
    opacity: 0.95;
}

/* Media query for mobile responsiveness */
@media (max-width: 767px) {
    .service-details-box .d-flex {
        flex-direction: column;
    }
    
    .header-container, 
    .detail-container {
        flex: 0 0 100% !important;
        border-right: none !important;
        border-bottom: 1px dotted #dee2e6;
    }
    
    .header-container {
        border-top: 1px dotted #dee2e6;
        margin-top: 10px;
    }
    
    .header-container:first-child {
        margin-top: 0;
        border-top: none;
    }
}

.detail-item {
    width: 100%;
}

.detail-label, .detail-value {
    display: block;
    text-align: center;
}

.detail-value {
    font-weight: 500;
    min-width: 75px;
    margin: 0 auto;
}

.min-w-75 {
    min-width: 75px;
}

.detail-item + .detail-item {
    margin-top: 0.5rem;
}
