/*
Template Name: Dastone - Admin & Dashboard Template
Author: Mannatthemes
Version: 2.0.0
File: App dark Css
*/
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap");
/*
Template Name: Dastone - Admin & Dashboard Template
Author: Mannatthemes
File: Leftbar
*/
.left-sidenav {
  min-width: 230px;
  max-width: 230px;
  background-color: #292e40;
  min-height: 100vh;
  transition: .3s;
  position: fixed;
  bottom: 0;
  top: 0;
  border-right: 1px solid #2d3552;
  z-index: 1001;
}

.left-sidenav .brand {
  text-align: center;
  height: 52px;
}

.left-sidenav .brand .logo {
  line-height: 52px;
}

.left-sidenav .brand .logo .logo-sm {
  height: 22px;
}

.left-sidenav .brand .logo .logo-lg {
  height: 14px;
  margin-left: 2px;
  display: none;
}

.left-sidenav .brand .logo .logo-lg.logo-light {
  display: inline-block;
}

.left-sidenav hr.hr-dashed.hr-menu {
  border-color: #49516d;
}

.left-sidenav .menu-content {
  height: 100%;
  padding-bottom: 70px;
}

.page-wrapper {
  flex: 1;
  padding: 0;
  display: block;
  margin-left: 230px;
}

.page-wrapper .page-content {
  width: 100%;
  position: relative;
  min-height: calc(100vh - 52px - 2px);
  padding: 0 8px 52px 8px;
  display: inline-block;
}

.left-sidenav-menu {
  padding-left: 0;
  margin-bottom: 0;
  padding: 16px;
}

.left-sidenav-menu .menu-label {
  text-transform: uppercase;
  font-size: 10px;
  font-weight: 400;
  letter-spacing: .5px;
  color: #8491b7;
  padding: 8px 0;
}

.left-sidenav-menu li {
  list-style: none;
  display: block;
  width: 100%;
  margin-top: 6px;
}

.left-sidenav-menu li > a {
  display: flex;
  align-items: center;
  padding: 4px 0px;
  color: #9fa8bf;
  transition: all 0.3s ease-out;
  font-weight: 400;
}

.left-sidenav-menu li > a .menu-icon {
  width: 18px;
  height: 18px;
  color: #7081b9;
  fill: rgba(112, 129, 185, 0.12);
  margin-right: 6px;
  stroke-width: 1px;
}

.left-sidenav-menu li > a.active .menu-icon {
  color: #1761fd;
  fill: rgba(23, 97, 253, 0.12);
}

.left-sidenav-menu li > a:hover {
  color: #2f6bf9;
}

.left-sidenav-menu li > a:hover i {
  color: #2f6bf9;
}

.left-sidenav-menu li > a i {
  width: 25px;
  display: inline-block;
  font-size: 16px;
  opacity: 0.9;
  color: #7081b9;
}

.left-sidenav-menu li > a i.ti-control-record {
  font-size: 8px;
  vertical-align: middle;
  margin-right: 0;
}

.left-sidenav-menu li > a span i {
  color: #7e88a0;
}

.left-sidenav-menu li ul {
  padding: 0 0 0 25px;
}

.left-sidenav-menu li ul li > a {
  padding: 4px 0;
  color: #9fa8bf;
  font-size: 13px;
  border-left: none;
}

.left-sidenav-menu li ul li > a:hover {
  color: #2f6bf9;
}

.left-sidenav-menu li ul li > a:hover i {
  color: #1761fd;
}

.left-sidenav-menu li.mm-active .menu-arrow i:before {
  content: "\F140";
}

.left-sidenav-menu li.mm-active .mm-active a .menu-arrow.left-has-menu i:before {
  content: "\F140";
}

.left-sidenav-menu li.mm-active .menu-arrow.left-has-menu i:before {
  content: "\F142";
}

.left-sidenav-menu li.mm-active li a menu-arrow.left-has-menu i:before {
  content: "\F142";
}

.left-sidenav-menu li.mm-active .mm-active > a {
  color: #6374ff;
  background: #292e40;
}

.left-sidenav-menu li.mm-active .mm-active > a.active {
  color: #e6e9f1;
  background-color: transparent;
}

.left-sidenav-menu li.mm-active .mm-active > a i {
  color: #e6e9f1;
}

.left-sidenav-menu li.mm-active .mm-active .menu-arrow.left-has-menu i:before {
  content: "\F140";
}

.left-sidenav-menu li.mm-active .mm-active .mm-show li a.active {
  color: #6374ff;
  font-weight: 500;
}

.left-sidenav-menu li.mm-active > a {
  color: #e6e9f1;
  border-radius: 4px;
}

.left-sidenav-menu li.mm-active > a i {
  color: #e6e9f1;
}

.left-sidenav-menu li.mm-active .nav-item.active a.nav-link.active {
  background-color: transparent;
  color: #1761fd;
}

.left-sidenav-menu li.mm-active .nav-item.active a.nav-link.active i {
  color: #2f6bf9;
}

.left-sidenav-menu .menu-arrow {
  margin-left: auto;
}

.left-sidenav-menu .menu-arrow i {
  width: 15px;
}

@media (max-width: 1316.98px) {
  .left-sidenav {
    margin-top: 52px;
  }
  .left-sidenav .brand {
    display: none;
  }
  .page-wrapper {
    margin: 0;
  }
}

@media (max-width: 1024px) {
  body {
    display: block !important;
  }
  .left-sidenav {
    overflow-y: auto;
    z-index: 10;
    bottom: 0;
    top: 52px;
    margin-top: 0;
  }
  .left-sidenav .brand {
    display: none;
    width: 70px;
  }
  .left-sidenav .brand .logo-lg {
    display: none !important;
  }
  .page-wrapper {
    margin: 0;
  }
  .page-content {
    min-height: 100vh;
    padding: 0 0 50px 0;
  }
}

.enlarge-menu .left-sidenav {
  display: none;
}

.enlarge-menu .left-sidenav .brand {
  background-color: #292e40;
}

.enlarge-menu .page-wrapper {
  margin: 0;
}

.update-msg {
  border-radius: 5px;
  padding: 16px 10px;
  margin: 50px 16px 16px;
  position: relative;
  background-color: rgba(137, 151, 189, 0.06);
}

.update-msg .img-box {
  width: 46px;
  height: 46px;
  display: block;
  line-height: 46px;
  background: #0c213a;
  text-align: center;
  border-radius: 50%;
  margin: 0 auto;
  position: absolute;
  top: -22px;
  right: 0;
  left: 0;
}

.update-msg h5 {
  color: #8997bd;
}

.update-msg p {
  color: #8997bd;
  font-weight: 400;
}

@media (max-width: 330px) {
  .creat-btn {
    display: none;
  }
}

/*
Template Name: Dastone - Admin & Dashboard Template
Author: Mannatthemes
File: Topbar
*/
.topbar {
  width: 100%;
  z-index: 1001;
}

.navbar-custom {
  background: #292e40;
  min-height: 52px;
  position: relative;
  border-bottom: 1px solid #2d3552;
}

.navbar-custom .nav-link {
  padding: 0 0.75rem;
  color: #b9c4ce;
  line-height: 52px;
  max-height: 52px;
}

.navbar-custom .nav-link .nav-icon {
  font-size: 18px;
}

.navbar-custom .dropdown-toggle:after {
  content: initial;
}

.navbar-custom .topbar-nav {
  height: 52px;
}

.navbar-custom .topbar-nav li {
  float: left;
}

.navbar-custom .topbar-nav li.show .nav-link {
  background-color: #2f3549;
  color: #edf0f5;
}

.navbar-custom .topbar-nav li .topbar-icon {
  height: 20px;
  width: 20px;
}

.button-menu-mobile {
  border: none;
  color: #b9c4ce;
  width: 60px;
  background-color: transparent;
  cursor: pointer;
}

.nav-user .nav-user-name {
  vertical-align: middle;
}

/* Notification */
.notification-list .notification-menu {
  max-height: 220px;
  margin-left: 0;
}

.notification-list .noti-icon-badge {
  display: inline-block;
  position: absolute;
  top: 9px;
  right: 8px;
  border: 2px solid rgba(44, 49, 68, 0.6);
  border-radius: 50%;
  padding: 1px 4px 1px;
  font-size: 8px !important;
}

.app-search-topbar form {
  position: relative;
  z-index: 1;
}

.app-search-topbar button {
  position: absolute;
  width: 30px;
  height: 30px;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  text-align: center;
  background-color: transparent;
  color: #b9c4ce;
}

.app-search-topbar button i {
  display: flex;
}

.app-search-topbar button:focus {
  outline: none;
}

.app-search-topbar input {
  width: 100%;
  height: 56px;
  border: none;
  font-size: 12px;
  border-radius: 4px;
  padding-left: 54px;
  padding-right: 15px;
  background-color: #2f3447;
  color: #edf0f5;
}

.app-search-topbar input:focus {
  box-shadow: none;
  outline: none !important;
}

@media (max-width: 1316.98px) {
  .topbar {
    position: fixed;
  }
  .page-wrapper .page-content {
    margin-top: 52px;
  }
}

@media (max-width: 1024px) {
  .topbar .topbar-left {
    width: 70px;
  }
  .topbar .topbar-left .logo-lg {
    display: none !important;
  }
  .topbar .app-search .form-control,
  .topbar .app-search .form-control:focus {
    width: 230px;
  }
}

@media (max-width: 768px) and (max-width: 1023.98px) {
  .app-search {
    display: none;
  }
}

@media (max-width: 767px) {
  .app-search,
  .hidden-sm {
    display: none;
  }
}

@media (max-width: 375px) {
  .page-title-box .breadcrumb {
    display: none;
  }
}

/*
Template Name: Dastone - Admin & Dashboard Template
Author: Mannatthemes
File: footer Css
*/
.footer {
  border-top: 1px solid #33394e;
  bottom: 0;
  padding: 16px;
  position: absolute;
  right: 0;
  left: 0;
  color: #8c97b7;
}

[data-layout="horizontal"] {
  display: inherit;
}

[data-layout="horizontal"] .footer .boxed-footer {
  width: 1380px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (max-width: 1499.98px) {
  [data-layout="horizontal"] .footer .boxed-footer {
    width: 100%;
  }
}

/*
Template Name: Dastone - Admin & Dashboard Template
Author: Mannatthemes
File: Horizontal-nav
*/
[data-layout="horizontal"] {
  display: inherit;
}

[data-layout="horizontal"] .navbar-custom {
  margin-left: 0;
  width: 100%;
  border-bottom: none;
}

[data-layout="horizontal"] .topbar {
  background-color: #292e40;
  display: flex;
  border-bottom: 1px solid #33394e;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 999;
}

[data-layout="horizontal"] .topbar .topbar-left {
  width: 160px;
}

[data-layout="horizontal"] .topbar .brand {
  text-align: center;
  height: 52px;
  width: 160px;
  background-color: #292e40;
}

[data-layout="horizontal"] .topbar .brand .logo {
  line-height: 52px;
}

[data-layout="horizontal"] .topbar .brand .logo .logo-sm {
  height: 22px;
}

[data-layout="horizontal"] .topbar .brand .logo .logo-lg {
  height: 14px;
  margin-left: 2px;
  display: none;
}

[data-layout="horizontal"] .topbar .brand .logo .logo-lg.logo-light {
  display: inline-block;
}

[data-layout="horizontal"] .navbar-custom-menu {
  min-height: 52px;
  display: flex;
  align-items: center;
}

[data-layout="horizontal"] .page-wrapper {
  margin: 0 auto;
  width: 1380px;
  padding-top: 52px;
}

.navbar-custom-menu .container-fluid {
  width: 1380px;
}

.navbar-custom-menu .has-submenu.active a {
  color: #1761fd;
}

.navbar-custom-menu .has-submenu.active a i {
  color: #b9c4ce;
}

.navbar-custom-menu .has-submenu.active a span {
  background-color: rgba(23, 97, 253, 0.1);
}

.navbar-custom-menu .has-submenu.active .submenu li.active > a {
  color: #2f6bf9;
}

.navbar-toggle {
  border: 0;
  position: relative;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.navbar-toggle:hover {
  background-color: transparent;
}

.navbar-toggle:hover span {
  background-color: #9ba7ca;
}

.navbar-toggle .lines {
  width: 22px;
  display: block;
  position: relative;
  padding-top: 20px;
  margin: 0 10px;
  height: 52px;
  transition: all .5s ease;
}

.navbar-toggle span {
  height: 2px;
  width: 100%;
  background-color: #9ba7ca;
  display: block;
  margin-bottom: 5px;
  transition: transform .5s ease;
}

.navbar-toggle.open span {
  position: absolute;
}

.navbar-toggle.open span:first-child {
  top: 25px;
  transform: rotate(45deg);
  background-color: #9ba7ca;
}

.navbar-toggle.open span:nth-child(2) {
  visibility: hidden;
}

.navbar-toggle.open span:last-child {
  width: 100%;
  top: 25px;
  transform: rotate(-45deg);
}

.navigation-menu {
  list-style: none;
  margin: 0;
  padding: 0;
  display: inline-block;
}

.navigation-menu > li {
  display: inline-block;
  position: relative;
}

.navigation-menu > li:first-child a {
  padding-left: 0px;
}

.navigation-menu > li a {
  display: block;
  font-size: 13px;
  transition: all .1s ease;
  line-height: 18px;
  padding-left: 6px;
  padding-right: 6px;
}

.navigation-menu > li a:hover {
  color: #2a2e40;
  background-color: transparent;
}

.navigation-menu > li a:focus {
  color: #2a2e40;
  background-color: transparent;
}

.navigation-menu > li a:active {
  color: #2a2e40;
}

.navigation-menu > li a i {
  display: inline-block;
  font-size: 10px;
  margin-right: 8px;
  transition: all .1s ease;
  vertical-align: middle;
  color: #b9c4ce;
}

.navigation-menu > li a .hori-menu-icon {
  height: 16px;
  width: 16px;
  margin-right: 3px;
  fill: rgba(155, 167, 202, 0.12);
}

@media (max-width: 1499.98px) {
  body[data-layout="horizontal"] {
    display: flex;
  }
  body[data-layout="horizontal"] .page-wrapper {
    width: 100%;
  }
  body[data-layout="horizontal"] .page-wrapper .page-content {
    margin-top: 0;
  }
}

@media (max-width: 1024px) {
  body[data-layout="horizontal"] {
    display: flex;
  }
  body[data-layout="horizontal"] .page-wrapper {
    width: 100%;
  }
  .topbar .brand {
    width: 60px !important;
    position: fixed;
    z-index: 1;
  }
  .topbar .brand .logo .logo-lg {
    display: none !important;
  }
  .navbar-custom-menu .container-fluid {
    padding: 0 20px;
    width: 100%;
  }
  .navigation-menu > li a {
    padding: 0 2px;
  }
  .page-content {
    min-height: 100vh;
  }
}

@media (max-width: 1024px) and (max-width: 1365.98px) {
  .navigation-menu {
    margin-left: 60px;
  }
  .topbar .navigation-menu > li .submenu.megamenu > li {
    width: 160px !important;
  }
}

@media (min-width: 992px) {
  .topbar .navigation-menu > li > a {
    display: flex;
    align-items: center;
    color: #b9c4ce;
    min-height: 52px;
  }
  .topbar .navigation-menu > li > a span {
    vertical-align: middle;
    padding: 8px 16px;
    border-radius: 4px;
  }
  .topbar .navigation-menu > li > a span .hori-menu-icon {
    height: 16px;
    width: 16px;
    margin-right: 3px;
    fill: rgba(155, 167, 202, 0.12);
    stroke-width: 1px;
    vertical-align: text-bottom;
  }
  .topbar .navigation-menu > li.last-elements .submenu {
    left: auto;
    right: 0;
  }
  .topbar .navigation-menu > li.last-elements .submenu > li.has-submenu .submenu {
    left: auto;
    right: 100%;
    margin-left: 0;
    margin-right: 10px;
  }
  .topbar .navigation-menu > li:hover a {
    color: #2f6bf9;
  }
  .topbar .navigation-menu > li:hover a span {
    background-color: rgba(23, 97, 253, 0.1);
  }
  .topbar .navigation-menu > li .submenu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    padding: 10px 0;
    list-style: none;
    min-width: 200px;
    text-align: left;
    visibility: hidden;
    opacity: 0;
    margin-top: 20px;
    transition: all .1s ease;
    background-color: #2f3447;
    border: 1px solid #343b52;
    border-radius: 4px;
    box-shadow: 0 17px 34px 0 rgba(0, 0, 0, 0.05), 0 8px 14px 0 rgba(0, 0, 0, 0.02);
  }
  .topbar .navigation-menu > li .submenu.megamenu {
    white-space: nowrap;
    width: auto;
  }
  .topbar .navigation-menu > li .submenu.megamenu > li {
    overflow: hidden;
    width: 190px;
    display: inline-block;
    vertical-align: top;
  }
  .topbar .navigation-menu > li .submenu > li.has-submenu > a:after {
    content: "\e649";
    font-family: "themify";
    position: absolute;
    right: 20px;
    top: 9px;
    font-size: 9px;
    color: #6a718e;
  }
  .topbar .navigation-menu > li .submenu > li .submenu {
    left: 100%;
    top: 0;
    margin-top: 10px;
  }
  .topbar .navigation-menu > li .submenu li {
    position: relative;
  }
  .topbar .navigation-menu > li .submenu li ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
  }
  .topbar .navigation-menu > li .submenu li a {
    display: block;
    padding: 8px 12px;
    clear: both;
    white-space: nowrap;
    font-size: 13px;
    color: #b9c4ce;
    margin-bottom: 0;
    transition: all .1s ease;
  }
  .topbar .navigation-menu > li .submenu li a:hover {
    color: #2f6bf9;
    background-color: #2c3144;
  }
  .topbar .navigation-menu > li .submenu li a i {
    font-size: 10px;
    margin: 0;
    margin-right: 8px;
    vertical-align: middle;
    color: #6a718e;
  }
  .topbar .navigation-menu > li .submenu li span {
    display: block;
    padding: 8px 25px;
    clear: both;
    line-height: 1.42857143;
    white-space: nowrap;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
    color: #eaf0f9;
  }
  .topbar .navbar-toggle {
    display: none;
  }
  .topbar #navigation {
    display: block;
  }
}

@media (max-width: 991px) {
  body[data-layout="horizontal"] .page-wrapper {
    width: 100%;
  }
  body[data-layout="horizontal"] .navbar-custom-menu .has-submenu.active a span {
    background-color: transparent;
  }
  .topbar .navigation-menu {
    float: none;
    max-height: 400px;
    text-align: left;
    width: 100%;
  }
  .topbar .navigation-menu > li {
    display: block;
  }
  .topbar .navigation-menu > li > a {
    color: #c0cada;
    padding: 15px;
    font-size: 13px;
  }
  .topbar .navigation-menu > li > a:after {
    position: absolute;
    right: 15px;
  }
  .topbar .navigation-menu > li > a:hover {
    color: #1761fd;
  }
  .topbar .navigation-menu > li .submenu {
    display: none;
    list-style: none;
    padding-left: 20px;
    margin: 0;
  }
  .topbar .navigation-menu > li .submenu li a {
    display: block;
    position: relative;
    padding: 7px 20px;
    font-size: 13px;
    color: #c0cada;
  }
  .topbar .navigation-menu > li .submenu li a:hover {
    color: #1761fd;
  }
  .topbar .navigation-menu > li .submenu li.has-submenu > a:after {
    content: "\e649";
    font-family: "themify";
    position: absolute;
    right: 30px;
  }
  .topbar .navigation-menu > li .submenu.open {
    display: block;
  }
  .topbar .navigation-menu > li .submenu .submenu {
    display: none;
    list-style: none;
  }
  .topbar .navigation-menu > li .submenu .submenu.open {
    display: block;
  }
  .topbar .navigation-menu > li .submenu.megamenu > li > ul {
    list-style: none;
    padding-left: 0;
  }
  .topbar .navigation-menu > li .submenu.megamenu > li > ul > li > span {
    display: block;
    position: relative;
    padding: 15px;
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 2px;
    color: #eaf0f9;
  }
  .topbar .navigation-menu > li.has-submenu.open > a {
    color: #1761fd;
  }
  .topbar .navbar-header {
    float: left;
  }
  .topbar .container-fluid {
    width: 100%;
    padding: 0;
  }
  #navigation {
    position: absolute;
    top: 52px;
    left: 0;
    right: 0;
    display: none;
    height: auto;
    max-height: 500px;
    padding-bottom: 0;
    overflow: auto;
    background-color: #2c3144;
    z-index: 1000;
    border-bottom: 1px solid #2d3552;
  }
  #navigation.open {
    display: block;
    overflow-y: auto;
  }
  .navbar-custom-menu {
    min-height: 0;
  }
}

@media (min-width: 768px) {
  .topbar .navigation-menu > li.has-submenu:hover > .submenu {
    visibility: visible;
    opacity: 1;
    margin-top: 0;
  }
  .topbar .navigation-menu > li.has-submenu:hover > .submenu > li.has-submenu:hover > .submenu {
    visibility: visible;
    opacity: 1;
    margin-top: -1px;
    margin-right: 0;
  }
  .navbar-toggle {
    display: block;
  }
}

@media (max-width: 1023.98px) {
  .topbar .navigation-menu {
    margin-left: 0;
  }
}

@media (max-width: 767.98px) {
  .topbar .brand {
    margin-right: 0;
  }
  .topbar .brand .logo .logo-sm {
    height: 32px;
  }
  .topbar .brand .logo .logo-lg {
    display: none;
  }
  .topbar .creat-btn {
    display: none !important;
  }
  .page-content {
    width: 100%;
    margin-top: 0;
    min-height: 100vh;
    padding: 0 0 60px 0;
  }
  .page-title-box {
    margin-top: 5px;
  }
  .navbar-custom-menu .has-submenu.active a {
    color: #1761fd;
  }
}

@media (max-width: 767px) {
  .page-title-box .breadcrumb,
  .page-title-box .state-information {
    display: none;
  }
}

@media (max-width: 620px) {
  .logo-large {
    display: none;
  }
  .logo-small {
    display: inline-block !important;
  }
  .topbar .has-submenu.active a {
    color: #1761fd;
  }
}

/*
Template Name: Dastone - Admin & Dashboard Template
Author: Mannatthemes
File: Dark Sidenav & Topbar
*/
body.dark-topbar {
  /* Notification */
}

body.dark-topbar .topbar .brand {
  background-color: #0c213a;
}

body.dark-topbar .topbar .brand .logo .logo-lg {
  display: none;
}

body.dark-topbar .topbar .brand .logo .logo-lg.logo-light {
  display: inline-block;
  margin-left: 2px;
}

body.dark-topbar .navbar-custom {
  background: #0c213a;
}

body.dark-topbar .navbar-custom .nav-link {
  color: #c2cbe2;
}

body.dark-topbar .navbar-custom .topbar-nav li.show .nav-link {
  background-color: #0f2847;
  color: #edf0f5;
}

body.dark-topbar .navbar-custom-menu .has-submenu:hover a span {
  color: #ffffff;
  background-color: #0f2847;
}

body.dark-topbar .navbar-custom-menu .has-submenu:hover.active a i {
  color: #2f6bf9;
}

body.dark-topbar .navbar-custom-menu .has-submenu:hover.active a span {
  background-color: #0f2847;
  color: #ffffff;
}

body.dark-topbar .button-menu-mobile {
  color: #c2cbe2;
}

body.dark-topbar .notification-list .noti-icon-badge {
  border: 2px solid rgba(255, 255, 255, 0.6);
}

@media (min-width: 992px) {
  body.dark-topbar .topbar .navigation-menu > li > a {
    color: #c2cbe2;
  }
  body.dark-topbar .topbar .navbar-custom-menu .has-submenu.active a span {
    background-color: #0f2847;
    color: #ffffff;
  }
}

body.dark-sidenav .left-sidenav {
  background-color: #0c213a;
}

body.dark-sidenav .left-sidenav .brand .logo .logo-lg {
  display: none;
}

body.dark-sidenav .left-sidenav .brand .logo .logo-lg.logo-light {
  display: inline-block;
}

body.dark-sidenav .left-sidenav hr.hr-dashed.hr-menu {
  border-color: #20446f;
}

body.dark-sidenav .left-sidenav-menu li > a {
  color: #a9baca;
}

body.dark-sidenav .left-sidenav-menu li > a.active {
  color: #ffffff;
}

body.dark-sidenav .left-sidenav-menu li > a:hover {
  color: #e6e6e6;
}

body.dark-sidenav .left-sidenav-menu li > a:hover i {
  color: #5180ff;
}

body.dark-sidenav .left-sidenav-menu li > a:hover span i {
  color: #e6e6e6;
}

body.dark-sidenav .left-sidenav-menu li > a i {
  color: #7683a5;
}

body.dark-sidenav .left-sidenav-menu li ul li > a {
  color: #a9baca;
}

body.dark-sidenav .left-sidenav-menu li ul li > a:hover {
  color: #e6e6e6;
}

body.dark-sidenav .left-sidenav-menu li ul li > a:hover i {
  color: #e6e6e6;
}

body.dark-sidenav .left-sidenav-menu li.mm-active .mm-active > a {
  color: #007cfb;
  background: #0c213a;
}

body.dark-sidenav .left-sidenav-menu li.mm-active .mm-active > a.active {
  color: #a9baca;
  background-color: transparent;
  font-weight: 500;
}

body.dark-sidenav .left-sidenav-menu li.mm-active .mm-active > a i {
  color: #a9baca;
}

body.dark-sidenav .left-sidenav-menu li.mm-active .mm-active .mm-show li a.active {
  color: #6374ff;
}

body.dark-sidenav .left-sidenav-menu li.mm-active > a {
  color: #ffffff;
}

body.dark-sidenav .left-sidenav-menu li.mm-active > a i {
  color: #ffffff;
}

body.dark-sidenav .left-sidenav-menu li.mm-active .nav-item.active a.nav-link.active {
  background-color: transparent;
  color: #6374ff;
}

body.dark-sidenav .left-sidenav-menu li.mm-active .nav-item.active a.nav-link.active i {
  color: #6374ff;
}

body.dark-sidenav .left-sidenav-menu .menu-arrow i {
  width: 15px;
  color: #a9baca;
}

body.dark-sidenav.navy-sidenav .left-sidenav {
  background-color: #244f96;
}

body.dark-sidenav.navy-sidenav .left-sidenav .left-sidenav-menu li > a.active .menu-icon {
  color: #ffffff;
}

body.dark-sidenav.navy-sidenav .left-sidenav .left-sidenav-menu li.mm-active .nav-item.active a.nav-link.active {
  color: #ffffff;
}

body.dark-sidenav.navy-sidenav .left-sidenav .left-sidenav-menu li.mm-active .mm-active .mm-show li a.active {
  color: #ffffff;
}

body.dark-sidenav.navy-sidenav .left-sidenav .left-sidenav-menu li.mm-active .mm-active > a {
  background-color: transparent;
  color: white;
}

/* =============
   General
============= */
html {
  position: relative;
  min-height: 100%;
}

body {
  margin: 0;
  overflow-x: hidden !important;
  font-size: 0.8125rem;
  background-color: #25293a;
  color: #edf0f5;
  min-height: 100vh;
  letter-spacing: 0.1px;
  line-height: 1.5;
  display: flex;
  position: relative;
}

.h1, .h2, .h3, .h4, .h5, .h6,
h1, h2, h3, h4, h5, h6 {
  color: #d1d1e2;
  margin: 10px 0;
}

h1 {
  line-height: 43px;
}

h2 {
  line-height: 35px;
}

h3 {
  line-height: 30px;
}

h4 {
  line-height: 22px;
}

a {
  font-family: "Roboto", sans-serif;
  color: #d1d1e2;
  text-decoration: none;
}

a:hover, a:active, a:focus {
  outline: 0;
  text-decoration: none;
}

p {
  line-height: 1.6;
  font-size: 0.8125rem;
  font-weight: 400;
}

* {
  outline: none !important;
}

.w-30 {
  width: 30% !important;
}

.fw-semibold {
  font-weight: 500 !important;
}

.list-group.custom-list-group .list-group-item {
  border-right-width: 0;
  border-left-width: 0;
  border-radius: 0;
}

.list-group.custom-list-group .list-group-item:first-child {
  border-top-width: 0;
}

.list-group.custom-list-group > .list-group-item {
  padding-left: 0;
  padding-right: 0;
}

.list-group.custom-list-group:last-child .list-group-item:last-child {
  border-bottom-width: 0;
}

hr {
  opacity: 1;
}

hr.hr-dashed {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px dashed #33394e;
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  background-color: transparent;
}

.media {
  display: flex;
  align-items: flex-start;
}

.media-body {
  flex: 1;
}

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  transition: all 0.5s ease-out;
  transition-property: transform, opacity;
  transform: scale(0) translate(0, 0);
  pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2);
}

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
}

.waves-notransition {
  transition: none !important;
}

.waves-button,
.waves-circle {
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
}

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1;
}

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em;
}

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em;
}

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}

.waves-input-wrapper.waves-button {
  padding: 0;
}

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
}

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
}

.waves-float {
  -webkit-mask-image: none;
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  transition: all 300ms;
}

.waves-float:active {
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
}

.waves-block {
  display: block;
}

.waves-effect.waves-light .waves-ripple {
  background-color: rgba(255, 255, 255, 0.4);
}

.waves-effect.waves-primary .waves-ripple {
  background-color: rgba(23, 97, 253, 0.4);
}

.waves-effect.waves-success .waves-ripple {
  background-color: rgba(3, 216, 127, 0.4);
}

.waves-effect.waves-info .waves-ripple {
  background-color: rgba(18, 164, 237, 0.4);
}

.waves-effect.waves-warning .waves-ripple {
  background-color: rgba(255, 184, 34, 0.4);
}

.waves-effect.waves-danger .waves-ripple {
  background-color: rgba(245, 50, 92, 0.4);
}

.card {
  margin-bottom: 16px;
  background-color: #2c3144;
  border: 1px solid #2d3552;
}

.card .card-header {
  background-color: #2c3144;
  border-bottom: 1px solid #33394e;
}

.card .card-header:first-child {
  border-radius: calc(.5rem - 5px) calc(.5rem - 5px) 0 0;
}

.card .card-footer {
  background-color: #2c3144;
  border-top: 1px dashed #2d3552;
  border-radius: 0 0 calc(.5rem - 5px) calc(.5rem - 5px);
}

.nav.nav-tabs .nav-item.show:focus, .nav.nav-tabs .nav-item.show.active,
.nav.nav-tabs .nav-link:focus,
.nav.nav-tabs .nav-link.active {
  color: #1761fd;
  background-color: #2c3144;
  border-color: transparent transparent #1761fd;
}

.nav.nav-tabs .nav-item.show.active:hover,
.nav.nav-tabs .nav-link.active:hover {
  border-color: transparent transparent #1761fd;
}

.nav.nav-tabs .nav-item.show:hover,
.nav.nav-tabs .nav-link:hover {
  border-color: transparent transparent #2d3552;
}

.nav.nav-tabs .nav-item.show .nav-link {
  color: #1761fd;
  background-color: #2c3144;
}

.nav.nav-pills {
  background-color: #333950;
}

.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
  color: #333950;
}

.nav-tabs-custom .nav.nav-tabs {
  border-bottom: none;
  display: inline-flex;
}

.nav-tabs-custom .nav.nav-tabs .nav-item {
  border: 2px solid #333950;
  border-radius: 4px;
  margin-right: 5px;
}

.nav-tabs-custom .nav.nav-tabs .nav-link {
  border-radius: 3px;
  padding: 10px 14px;
}

.nav-tabs-custom .nav.nav-tabs .nav-link.active {
  background-color: #333950;
  border-color: transparent;
  color: #1761fd;
}

.nav-tabs-custom .nav.nav-tabs .nav-link.active i {
  color: #1761fd;
}

.nav-tabs-custom .nav.nav-tabs .nav-link i {
  font-size: 24px;
  vertical-align: middle;
  color: #9ba7ca;
}

.nav.nav-pills-custom.nav-pills .nav-item .nav-link {
  padding: 4px 14px;
  font-size: 0.71rem;
  line-height: 2;
  border: 1px solid #303e67;
  color: #8c97b7;
}

.nav.nav-pills-custom.nav-pills .nav-item .nav-link.active {
  color: #ffffff;
}

.nav-pills .nav-item.show .nav-link,
.nav-pills .nav-link.active {
  background: #1761fd;
  color: #ffffff;
}

.eco-revene-history .nav .nav-item .nav-link.active {
  color: #eaf0f9;
  background-color: #333950;
}

.nav-border.nav.nav-pills {
  background-color: transparent;
  border-bottom: 1px dashed #2d3552;
  line-height: 36px;
}

.nav-border .nav-item {
  margin-bottom: -2px;
}

.nav-border .nav-link {
  color: #edf0f5;
}

.nav-border .nav-item.show .nav-link,
.nav-border .nav-link.active {
  background: transparent;
  color: #1761fd;
  box-shadow: none;
  border-radius: 0;
  border-bottom: 2px solid #1761fd;
}

.badge-success,
.badge-orange {
  color: #ffffff;
}

.badge.badge-outline-primary {
  border: 1px solid rgba(23, 97, 253, 0.5);
  color: #1761fd;
}

.badge.badge-outline-secondary {
  border: 1px solid rgba(155, 167, 202, 0.5);
  color: #9ba7ca;
}

.badge.badge-outline-success {
  border: 1px solid rgba(3, 216, 127, 0.5);
  color: #03d87f;
}

.badge.badge-outline-warning {
  border: 1px solid rgba(255, 184, 34, 0.5);
  color: #ffb822;
}

.badge.badge-outline-danger {
  border: 1px solid rgba(245, 50, 92, 0.5);
  color: #f5325c;
}

.badge.badge-outline-info {
  border: 1px solid rgba(18, 164, 237, 0.5);
  color: #12a4ed;
}

.badge.badge-outline-light {
  border: 1px solid #333950;
  color: #8c97b7;
}

.badge.badge-outline-dark {
  border: 1px solid rgba(234, 240, 249, 0.5);
  color: #eaf0f9;
}

.badge.badge-outline-pink {
  border: 1px solid rgba(253, 60, 151, 0.5);
  color: #fd3c97;
}

.badge.badge-outline-purple {
  border: 1px solid rgba(109, 129, 245, 0.5);
  color: #6d81f5;
}

.badge.badge-dot {
  display: inline-flex;
  align-items: center;
}

.badge.badge-dot:after {
  position: absolute;
  content: "";
  right: 3px;
  top: -13px;
  transform: translateY(-50%);
  display: inline-block;
  height: 10px;
  width: 10px;
  border: 2px solid #2c3144;
  border-radius: 50%;
}

.badge.badge-dot.online:after {
  background-color: #03d87f;
}

.badge.badge-dot.offline:after {
  background-color: #9ba7ca;
}

.btn-round {
  border-radius: 30px;
}

.btn-icon-circle {
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.btn-icon-circle-sm {
  width: 30px;
  height: 30px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  line-height: 1;
}

.btn-icon-square {
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.btn-icon-square-sm {
  width: 30px;
  height: 30px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.btn-square {
  border-radius: 0;
}

.btn-skew {
  transform: skew(-15deg);
}

.btn-skew span {
  display: inline-block;
  transform: skew(15deg);
}

.accordion .btn:focus {
  box-shadow: none;
}

.btn.btn-outline-dashed {
  outline: 1px dashed #ffffff !important;
  outline-offset: -5px;
  -moz-outline-radius: 25px;
}

.btn-link:active, .btn-link.active, .btn-link:focus, .btn-link:hover {
  text-decoration: none;
}

.button-list,
.button-items {
  margin-bottom: -8px;
}

.button-list .btn,
.button-items .btn {
  margin: 0 5px 8px 0;
}

.btn {
  box-shadow: none;
}

.btn.focus, .btn:focus {
  box-shadow: none;
}

.btn-xs {
  padding: 2px 10px;
  font-size: 11px;
}

.btn-xl {
  padding: 8px 20px;
  font-size: 15px;
}

.btn-outline-light {
  color: #8c97b7;
  background-image: none;
  background-color: transparent;
  border-color: #33394e;
}

.btn-outline-dark {
  color: #eaf0f9;
}

.btn-outline-dark:hover {
  box-shadow: none;
  color: #2a2e40;
}

.btn-outline-info.focus, .btn-outline-info:focus {
  box-shadow: none;
}

/*soft buttons*/
.btn-soft-primary {
  background-color: rgba(23, 97, 253, 0.1);
  color: #1761fd;
}

.btn-soft-primary:hover {
  background-color: #1761fd;
  color: #ffffff;
}

.btn-soft-primary:focus {
  box-shadow: 0 0 0 0.1rem rgba(23, 97, 253, 0.2);
  background-color: rgba(23, 97, 253, 0.8);
  color: #ffffff;
}

.btn-soft-secondary {
  background-color: rgba(155, 167, 202, 0.15);
  color: #9ba7ca;
}

.btn-soft-secondary:hover {
  background-color: #9ba7ca;
  color: #ffffff;
}

.btn-soft-secondary:focus {
  box-shadow: 0 0 0 0.1rem rgba(155, 167, 202, 0.2);
  background-color: rgba(155, 167, 202, 0.8);
  color: #ffffff;
}

.btn-soft-success {
  background-color: rgba(3, 216, 127, 0.1);
  color: #03d87f;
}

.btn-soft-success:hover {
  background-color: #03d87f;
  color: #ffffff;
}

.btn-soft-success:focus {
  box-shadow: 0 0 0 0.1rem rgba(3, 216, 127, 0.2);
  background-color: rgba(3, 216, 127, 0.8);
  color: #ffffff;
}

.btn-soft-warning {
  background-color: rgba(255, 184, 34, 0.18);
  color: #ffb822;
}

.btn-soft-warning:hover {
  background-color: #ffb822;
  color: #ffffff;
}

.btn-soft-warning:focus {
  box-shadow: 0 0 0 0.1rem rgba(255, 184, 34, 0.2);
  background-color: rgba(255, 184, 34, 0.8);
  color: #ffffff;
}

.btn-soft-danger {
  background-color: rgba(245, 50, 92, 0.1);
  color: #f5325c;
}

.btn-soft-danger:hover {
  background-color: #f5325c;
  color: #ffffff;
}

.btn-soft-danger:focus {
  box-shadow: 0 0 0 0.1rem rgba(245, 50, 92, 0.2);
  background-color: rgba(245, 50, 92, 0.8);
  color: #ffffff;
}

.btn-soft-info {
  background-color: rgba(18, 164, 237, 0.1);
  color: #12a4ed;
}

.btn-soft-info:hover {
  background-color: #12a4ed;
  color: #ffffff;
}

.btn-soft-info:focus {
  box-shadow: 0 0 0 0.1rem rgba(18, 164, 237, 0.2);
  background-color: rgba(18, 164, 237, 0.8);
  color: #ffffff;
}

.btn-soft-dark {
  background-color: rgba(234, 240, 249, 0.1);
  color: #eaf0f9;
}

.btn-soft-dark:hover {
  background-color: #eaf0f9;
  color: #2a2e40;
}

.btn-soft-dark:focus {
  box-shadow: 0 0 0 0.1rem rgba(234, 240, 249, 0.2);
  background-color: rgba(234, 240, 249, 0.8);
  color: #2a2e40;
}

.btn-soft-pink {
  background-color: rgba(253, 60, 151, 0.1);
  color: #fd3c97;
}

.btn-soft-pink:hover {
  background-color: #fd3c97;
  color: #ffffff;
}

.btn-soft-pink:focus {
  box-shadow: 0 0 0 0.1rem rgba(253, 60, 151, 0.2);
  background-color: rgba(253, 60, 151, 0.8);
  color: #ffffff;
}

.btn-soft-purple {
  background-color: rgba(109, 129, 245, 0.1);
  color: #6d81f5;
}

.btn-soft-purple:hover {
  background-color: #6d81f5;
  color: #ffffff;
}

.btn-soft-purple:focus {
  box-shadow: 0 0 0 0.1rem rgba(109, 129, 245, 0.2);
  background-color: rgba(109, 129, 245, 0.8);
  color: #ffffff;
}

.btn-soft-blue {
  background-color: rgba(23, 97, 253, 0.1);
  color: #1761fd;
}

.btn-soft-blue:hover {
  background-color: #1761fd;
  color: #ffffff;
}

.btn-soft-blue:focus {
  box-shadow: 0 0 0 0.1rem rgba(23, 97, 253, 0.2);
  background-color: rgba(23, 97, 253, 0.8);
  color: #ffffff;
}

/*btn-gradient-primary*/
.btn-gradient-primary {
  background: linear-gradient(14deg, #1761fd 0%, rgba(23, 97, 253, 0.6));
  color: #ffffff;
  border: none;
}

.btn-gradient-primary:hover, .btn-gradient-primary:focus, .btn-gradient-primary:active, .btn-gradient-primary.active,
.btn-gradient-primary.focus, .btn-gradient-primary:active, .btn-gradient-primary:focus, .btn-gradient-primary:hover,
.open > .dropdown-toggle.btn-gradient-primary, .btn-gradient-primary.active,
.btn-gradient-primary:active, .show > .btn-gradient-primary.dropdown-toggle {
  background: linear-gradient(14deg, #1761fd 0%, rgba(23, 97, 253, 0.6));
  color: #ffffff;
}

/*btn-gradient-success*/
.btn-gradient-success {
  background: linear-gradient(14deg, #03d87f 0%, rgba(3, 216, 127, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-success:hover, .btn-gradient-success:focus, .btn-gradient-success:active, .btn-gradient-success.active,
.btn-gradient-success.focus, .btn-gradient-success:active, .btn-gradient-success:focus, .btn-gradient-success:hover,
.open > .dropdown-toggle.btn-gradient-success, .btn-gradient-success.active,
.btn-gradient-success:active, .show > .btn-gradient-success.dropdown-toggle {
  background: linear-gradient(14deg, #03d87f 0%, rgba(3, 216, 127, 0.7));
  color: #ffffff;
}

/*btn-gradient-secondary*/
.btn-gradient-secondary {
  background: linear-gradient(14deg, #9ba7ca 0%, rgba(155, 167, 202, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-secondary:hover, .btn-gradient-secondary:focus, .btn-gradient-secondary:active, .btn-gradient-secondary.active,
.btn-gradient-secondary.focus, .btn-gradient-secondary:active, .btn-gradient-secondary:focus, .btn-gradient-secondary:hover,
.open > .dropdown-toggle.btn-gradient-secondary, .btn-gradient-secondary.active,
.btn-gradient-secondary:active, .show > .btn-gradient-secondary.dropdown-toggle {
  background: linear-gradient(14deg, #9ba7ca 0%, rgba(155, 167, 202, 0.7));
  color: #ffffff;
}

/*btn-gradient-danger*/
.btn-gradient-danger {
  background: linear-gradient(14deg, #f5325c 0%, rgba(245, 50, 92, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-danger:hover, .btn-gradient-danger:focus, .btn-gradient-danger:active, .btn-gradient-danger.active,
.btn-gradient-danger.focus, .btn-gradient-danger:active, .btn-gradient-danger:focus, .btn-gradient-danger:hover,
.open > .dropdown-toggle.btn-gradient-danger, .btn-gradient-danger.active,
.btn-gradient-danger:active, .show > .btn-gradient-danger.dropdown-toggle {
  background: linear-gradient(14deg, #f5325c 0%, rgba(245, 50, 92, 0.7));
  color: #ffffff;
}

/*btn-gradient-warning*/
.btn-gradient-warning {
  background: linear-gradient(14deg, #ffb822 0%, rgba(255, 184, 34, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-warning:hover, .btn-gradient-warning:focus, .btn-gradient-warning:active, .btn-gradient-warning.active,
.btn-gradient-warning.focus, .btn-gradient-warning:active, .btn-gradient-warning:focus, .btn-gradient-warning:hover,
.open > .dropdown-toggle.btn-gradient-warning, .btn-gradient-warning.active,
.btn-gradient-warning:active, .show > .btn-gradient-warning.dropdown-toggle {
  background: linear-gradient(14deg, #ffb822 0%, rgba(255, 184, 34, 0.7));
  color: #ffffff;
}

/*btn-gradient-info*/
.btn-gradient-info {
  background: linear-gradient(14deg, #12a4ed 0%, rgba(18, 164, 237, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-info:hover, .btn-gradient-info:focus, .btn-gradient-info:active, .btn-gradient-info.active,
.btn-gradient-info.focus, .btn-gradient-info:active, .btn-gradient-info:focus, .btn-gradient-info:hover,
.open > .dropdown-toggle.btn-gradient-info, .btn-gradient-info.active,
.btn-gradient-info:active, .show > .btn-gradient-info.dropdown-toggle {
  background: linear-gradient(14deg, #12a4ed 0%, rgba(18, 164, 237, 0.7));
  color: #ffffff;
}

/*btn-gradient-dark*/
.btn-gradient-dark {
  background: linear-gradient(14deg, #eaf0f9 0%, rgba(234, 240, 249, 0.7));
  color: #2a2e40;
  border: none;
}

.btn-gradient-dark:hover, .btn-gradient-dark:focus, .btn-gradient-dark:active, .btn-gradient-dark.active,
.btn-gradient-dark.focus, .btn-gradient-dark:active, .btn-gradient-dark:focus, .btn-gradient-dark:hover,
.open > .dropdown-toggle.btn-gradient-dark, .btn-gradient-dark.active,
.btn-gradient-dark:active, .show > .btn-gradient-dark.dropdown-toggle {
  background: linear-gradient(14deg, #eaf0f9 0%, rgba(234, 240, 249, 0.7));
  color: #2a2e40;
}

/*btn-gradient-pink*/
.btn-gradient-pink {
  background: linear-gradient(14deg, #fd3c97 0%, rgba(253, 60, 151, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-pink:hover, .btn-gradient-pink:focus, .btn-gradient-pink:active, .btn-gradient-pink.active,
.btn-gradient-pink.focus, .btn-gradient-pink:active, .btn-gradient-pink:focus, .btn-gradient-pink:hover,
.open > .dropdown-toggle.btn-gradient-pink, .btn-gradient-pink.active,
.btn-gradient-pink:active, .show > .btn-gradient-pink.dropdown-toggle {
  background: linear-gradient(14deg, #fd3c97 0%, rgba(253, 60, 151, 0.7));
  color: #ffffff;
}

/*btn-gradient-purple*/
.btn-gradient-purple {
  background: linear-gradient(14deg, #6d81f5 0%, rgba(109, 129, 245, 0.7));
  color: #ffffff;
  border: none;
}

.btn-gradient-purple:hover, .btn-gradient-purple:focus, .btn-gradient-purple:active, .btn-gradient-purple.active,
.btn-gradient-purple.focus, .btn-gradient-purple:active, .btn-gradient-purple:focus, .btn-gradient-purple:hover,
.open > .dropdown-toggle.btn-gradient-purple, .btn-gradient-purple.active,
.btn-gradient-purple:active, .show > .btn-gradient-purple.dropdown-toggle {
  background: linear-gradient(14deg, #6d81f5 0%, rgba(109, 129, 245, 0.7));
  color: #ffffff;
}

.dropdown-menu {
  box-shadow: 0 3px 12px #25293a;
  margin: 0;
}

.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item:focus {
  background-color: rgba(42, 46, 64, 0.5);
  color: #eaf0f9;
}

.dropdown-toggle::after {
  display: none;
}

.dropdown-lg {
  width: 320px;
}

.ui-widget-content {
  padding: 4px 0;
  font-size: 0.8125rem;
  box-shadow: 0 3px 12px rgba(48, 62, 103, 0.4);
  border-color: rgba(36, 40, 56, 0.05);
  background-color: #2f3447;
  margin: 0;
}

.ui-widget-content .ui-menu-item {
  padding: 6px 24px;
  color: #d1d1e2;
}

.ui-widget-content .ui-menu-item:hover, .ui-widget-content .ui-menu-item:focus, .ui-widget-content .ui-menu-item.active, .ui-widget-content .ui-menu-item:active {
  color: #dfe4ea;
  text-decoration: none;
  background-color: #2a2e40;
}

.ui-widget.ui-widget-content {
  border: 1px solid rgba(51, 57, 80, 0.4);
  z-index: 1111;
  position: fixed;
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border: none;
  color: #dfe4ea;
  text-decoration: none;
  background-color: transparent;
}

.ui-menu .ui-state-focus,
.ui-menu .ui-state-active {
  margin: 0;
}

.dropdown-toggle::after,
.dropstart .dropdown-toggle::before,
.dropend .dropdown-toggle::after,
.dropup .dropdown-toggle::after,
.dropdown-toggle::after {
  display: none;
}

.table th {
  color: #d1d1e2;
  font-weight: 500;
  vertical-align: middle;
  border-color: #33394e;
}

.table td {
  font-weight: 400;
  vertical-align: middle;
  border-color: #33394e;
}

.table.border-dashed th {
  font-weight: 500;
  border-top: 1px dashed #33394e;
}

.table.border-dashed td {
  border-top: 1px dashed #33394e;
}

.table.border-dashed thead th {
  border-bottom: 1px dashed #33394e;
}

.table .thead-light th {
  color: #d1d1e2;
  background-color: #333950;
  border-color: #33394e;
}

.table.table-dark {
  color: #2a2e40;
}

.table.table-dark th {
  color: #2a2e40;
}

.table tr:last-child td {
  border-bottom: none;
}

.table.table-bordered thead {
  background-color: transparent;
}

.table.table-bordered tr:last-child td {
  border-bottom: 1px solid #33394e;
}

.table tfoot tr th {
  border-bottom: none;
  border-top: 1px solid #33394e;
}

/*===Datatable===*/
div.dataTables_wrapper div.dataTables_info {
  padding-top: 8px;
}

table.dataTable {
  margin: 16px auto !important;
}

.table td {
  vertical-align: middle;
}

.table td.highlight {
  font-weight: bold;
  color: #12a4ed;
}

.table td.details-control {
  background: url("../images/open.png") no-repeat center center;
  cursor: pointer;
}

.table tr.shown td.details-control {
  background: url("../images/close.png") no-repeat center center;
}

.dataTables_paginate ul.pagination .page-link {
  padding: .25rem .5rem;
  font-size: .71rem;
  line-height: 1.8;
}

/*== Table Responsive ==*/
.table-rep-plugin .btn-toolbar {
  display: block;
}

.table-rep-plugin .btn-toolbar .float-right {
  float: right;
}

.table-rep-plugin .btn-default {
  background: transparent;
  color: #1761fd;
  margin-right: 0;
  transition: .3s ease-out;
  padding: .25rem .5rem;
  font-size: .71rem;
  line-height: 1.8;
  border-radius: .2rem;
  border: 1px solid #33394e;
}

.table-rep-plugin .btn-default:hover, .table-rep-plugin .btn-default:focus {
  color: #1761fd;
  background: transparent;
}

.table-rep-plugin .btn-default.btn-primary:active,
.table-rep-plugin .btn-default.btn-primary:hover,
.table-rep-plugin .btn-default.btn-primary:focus {
  background: transparent;
  color: #1761fd;
  border: 1px solid #33394e;
}

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal;
}

.table-rep-plugin table.focus-on tbody tr.focused th,
.table-rep-plugin table.focus-on tbody tr.focused td {
  background-color: #1761fd;
  color: #ffffff;
}

/* Sticky Header */
.fixed-solution .sticky-table-header {
  top: 0 !important;
  background-color: #2a2e40;
  border-top: 1px solid #33394e;
  border-bottom: 1px solid #33394e;
}

.table-responsive[data-pattern="priority-columns"] {
  border: 1px solid #33394e;
}

.progress-bar-striped {
  background-size: 6px 6px;
}

/* Progressbar Vertical */
.progress-vertical {
  min-height: 250px;
  height: 250px;
  width: 10px;
  position: relative;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}

.progress-vertical .progress-bar {
  width: 100%;
}

.progress-vertical-bottom {
  min-height: 250px;
  height: 250px;
  position: relative;
  width: 10px;
  display: inline-block;
  margin-bottom: 0;
  margin-right: 20px;
}

.progress-vertical-bottom .progress-bar {
  width: 100%;
  position: absolute;
  bottom: 0;
}

.progress-vertical.progress-sm,
.progress-vertical-bottom.progress-sm {
  width: 5px !important;
}

.progress-vertical.progress-sm .progress-bar,
.progress-vertical-bottom.progress-sm .progress-bar {
  font-size: 8px;
  line-height: 5px;
}

.progress-vertical.progress-md,
.progress-vertical-bottom.progress-md {
  width: 15px !important;
}

.progress-vertical.progress-md .progress-bar,
.progress-vertical-bottom.progress-md .progress-bar {
  font-size: 10.8px;
  line-height: 14.4px;
}

.progress-vertical.progress-lg,
.progress-vertical-bottom.progress-lg {
  width: 20px !important;
}

.progress-vertical.progress-lg .progress-bar,
.progress-vertical-bottom.progress-lg .progress-bar {
  font-size: 12px;
  line-height: 20px;
}

.progress-icon-spin {
  font-size: 36px;
}

.b-round {
  border-radius: 40px;
}

.alert {
  position: relative;
  border: 0;
}

.alert.custom-alert {
  overflow: hidden;
}

.alert.custom-alert:after {
  content: '';
  position: absolute;
  width: 4px;
  height: 100%;
  top: 0;
  left: 0;
}

.alert.custom-alert.custom-alert-primary:after {
  background-color: #1761fd;
}

.alert.custom-alert.custom-alert-success:after {
  background-color: #03d87f;
}

.alert.custom-alert.custom-alert-danger:after {
  background-color: #f5325c;
}

.alert.custom-alert.custom-alert-info:after {
  background-color: #12a4ed;
}

.alert.custom-alert.custom-alert-warning:after {
  background-color: #ffb822;
}

.alert.icon-custom-alert {
  display: flex;
  padding: 5px 18px;
}

.alert .alert-icon {
  font-size: 21px;
  margin-right: 8px;
}

.alert .alert-text {
  flex-grow: 1;
  align-self: center;
}

.alert .alert-close {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.alert .alert-link {
  font-weight: 600;
}

.alert.alert-light {
  color: #eaf0f9;
  background-color: #333950;
}

.alert.alert-outline-success {
  border: 1px solid #03d87f;
  background-color: transparent;
  color: #03d87f;
}

.alert.alert-outline-danger {
  border: 1px solid #f5325c;
  background-color: transparent;
  color: #f5325c;
}

.alert.alert-outline-primary {
  border: 1px solid #1761fd;
  background-color: transparent;
  color: #1761fd;
}

.alert.alert-outline-warning {
  border: 1px solid #ffb822;
  background-color: transparent;
  color: #ffb822;
}

.alert.alert-outline-info {
  border: 1px solid #12a4ed;
  background-color: transparent;
  color: #12a4ed;
}

.alert.alert-outline-pink {
  border: 1px solid #fd3c97;
  background-color: transparent;
  color: #fd3c97;
}

.alert.alert-outline-purple {
  border: 1px solid #6d81f5;
  background-color: transparent;
  color: #6d81f5;
}

.alert.alert-outline-blue {
  border: 1px solid #1761fd;
  background-color: transparent;
  color: #1761fd;
}

.alert.alert-outline-secondary {
  border: 1px solid #9ba7ca;
  background-color: transparent;
  color: #9ba7ca;
}

.alert.alert-outline-dark {
  border: 1px solid #eaf0f9;
  background-color: transparent;
  color: #eaf0f9;
}

.alert.alert-primary-shadow {
  box-shadow: 0 0 24px 0 rgba(23, 97, 253, 0.06), 0 1px 0 0 rgba(23, 97, 253, 0.02);
}

.alert.alert-secondary-shadow {
  box-shadow: 0 0 24px 0 rgba(155, 167, 202, 0.18), 0 1px 0 0 rgba(155, 167, 202, 0.02);
}

.alert.alert-success-shadow {
  box-shadow: 0 0 24px 0 rgba(3, 216, 127, 0.06), 0 1px 0 0 rgba(3, 216, 127, 0.02);
}

.alert.alert-info-shadow {
  box-shadow: 0 0 24px 0 rgba(18, 164, 237, 0.06), 0 1px 0 0 rgba(18, 164, 237, 0.02);
}

.alert.alert-warning-shadow {
  box-shadow: 0 0 24px 0 rgba(255, 184, 34, 0.06), 0 1px 0 0 rgba(255, 184, 34, 0.02);
}

.alert.alert-danger-shadow {
  box-shadow: 0 0 24px 0 rgba(245, 50, 92, 0.06), 0 1px 0 0 rgba(245, 50, 92, 0.02);
}

.alert.alert-dark-shadow {
  box-shadow: 0 0 24px 0 rgba(234, 240, 249, 0.06), 0 1px 0 0 rgba(234, 240, 249, 0.02);
}

.alert.alert-pink-shadow {
  box-shadow: 0 0 24px 0 rgba(253, 60, 151, 0.06), 0 1px 0 0 rgba(253, 60, 151, 0.02);
}

.alert.alert-purple-shadow {
  box-shadow: 0 0 24px 0 rgba(109, 129, 245, 0.06), 0 1px 0 0 rgba(109, 129, 245, 0.02);
}

.alert.alert-blue-shadow {
  box-shadow: 0 0 24px 0 rgba(23, 97, 253, 0.06), 0 1px 0 0 rgba(23, 97, 253, 0.02);
}

.form-control.alert-soft-success {
  border: none;
  background-color: #d1feec;
  color: #03d87f;
}

.form-control.alert-soft-secondary {
  border: none;
  background-color: #e1ebff;
  color: #1761fd;
}

label {
  font-weight: 400;
  font-size: 13px;
}

.custom-control-label {
  padding-top: 2px;
}

.form-control:focus {
  box-shadow: none;
}

.input-group-text {
  font-size: 0.8125rem;
  background-color: #333950;
  border: 1px solid #33394e;
}

.custom-file-input,
.custom-file-label {
  height: calc(2.3rem + 1px);
  line-height: 26px;
}

.custom-file-label::after {
  height: calc(1.5em + 15px);
  line-height: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.custom-select:focus {
  border-color: rgba(23, 97, 253, 0.5);
  box-shadow: none;
}

.custom-control-input:checked ~ .custom-control-indicator {
  background-color: #1761fd;
}

.custom-control-input:focus ~ .custom-control-indicator {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 3px #1761fd;
}

.has-success .form-control {
  border-color: #03d87f;
  box-shadow: none;
}

.has-warning .form-control {
  border-color: #ffb822;
  box-shadow: none;
}

.has-error .form-control {
  border-color: #f5325c;
  box-shadow: none;
}

.input-group-addon {
  border-radius: 2px;
  border: 1px solid #333950;
}

.col-form-label {
  text-align: right;
}

.form-control.is-valid:focus,
.custom-select.is-valid:focus,
.was-validated .custom-select:valid:focus {
  border-color: #03d87f;
  box-shadow: none;
}

.custom-select.is-invalid:focus,
.was-validated .custom-select:invalid:focus {
  border-color: #f5325c;
  box-shadow: none;
}

.custom-file-input.is-invalid:focus ~ .custom-file-label,
.was-validated .custom-file-input:invalid:focus ~ .custom-file-label,
.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
  border-color: #f5325c;
  box-shadow: none;
}

.custom-file-input:focus ~ .custom-file-label {
  border-color: rgba(23, 97, 253, 0.5);
  box-shadow: none;
}

dt {
  font-weight: 600;
}

.demo-container {
  box-sizing: border-box;
  width: 850px;
  height: 450px;
  padding: 20px 15px 15px 15px;
  margin: 15px auto 30px auto;
  border: 1px solid #ddd;
  background: #fff;
  background: linear-gradient(#f6f6f6 0, #fff 50px);
  background: -webkit-linear-gradient(#f6f6f6 0, #fff 50px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  -o-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  user-select: none;
}

.demo-placeholder {
  width: 100%;
  height: 100%;
  font-size: 14px;
}

.dropify-wrapper {
  background-color: #2c3144;
  border: 2px dashed #33394e;
}

.dropify-wrapper .dropify-message p {
  color: #8997bd;
}

.dropify-wrapper .dropify-message span.file-icon {
  color: #1761fd;
}

.dropify-wrapper:hover {
  background-size: 30px 30px;
  background-image: linear-gradient(-45deg, #333950 25%, transparent 25%, transparent 50%, #333950 50%, #333950 75%, transparent 75%, transparent);
}

.dropify-wrapper .dropify-preview {
  background-color: #2c3144;
}

.modal-demo {
  background-color: #2c3144;
  width: 600px;
  border-radius: 4px;
  display: none;
  position: relative;
}

.modal-demo .close {
  position: absolute;
  top: 15px;
  right: 25px;
  color: #333950;
}

.modal-content {
  border: none;
}

.modal-content .modal-header {
  background-color: #1c2d41;
}

.modal-content .modal-header .modal-title {
  color: #ffffff;
  align-self: center;
  font-weight: 500;
  margin-top: 0;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
}

.modal-content .modal-footer {
  border-top: 1px solid #303e67;
}

.modal-content .modal-footer > * {
  margin: 0 0 0 0.25rem;
}

.modal-content .modal-body p, .modal-content h4 {
  color: #8c97b7;
}

.modal-content .modal-title.custom-title {
  color: white;
}

.offcanvas-header {
  background-color: #1c2d41;
}

.offcanvas-header h5 {
  color: #ffffff;
}

.modal-backdrop {
  background-color: rgba(36, 40, 56, 0.1);
  backdrop-filter: blur(2px);
}

.modal-backdrop.show {
  opacity: inherit;
}

.form-switch.form-switch-primary .form-check-input:checked {
  background-color: #1761fd;
  border-color: #1761fd;
}

.form-switch.form-switch-secondary .form-check-input:checked {
  background-color: #9ba7ca;
  border-color: #9ba7ca;
}

.form-switch.form-switch-success .form-check-input:checked {
  background-color: #03d87f;
  border-color: #03d87f;
}

.form-switch.form-switch-info .form-check-input:checked {
  background-color: #12a4ed;
  border-color: #12a4ed;
}

.form-switch.form-switch-warning .form-check-input:checked {
  background-color: #ffb822;
  border-color: #ffb822;
}

.form-switch.form-switch-danger .form-check-input:checked {
  background-color: #f5325c;
  border-color: #f5325c;
}

.form-switch.form-switch-light .form-check-input:checked {
  background-color: #333950;
  border-color: #333950;
}

.form-switch.form-switch-dark .form-check-input:checked {
  background-color: #eaf0f9;
  border-color: #eaf0f9;
}

.form-switch.form-switch-pink .form-check-input:checked {
  background-color: #fd3c97;
  border-color: #fd3c97;
}

.form-switch.form-switch-purple .form-check-input:checked {
  background-color: #6d81f5;
  border-color: #6d81f5;
}

.form-switch.form-switch-beanred .form-check-input:checked {
  background-color: #FE6B8B;
  border-color: #FE6B8B;
}

.form-switch.form-switch-orange .form-check-input:checked {
  background-color: #ff8500;
  border-color: #ff8500;
}

.form-switch.form-switch-blue .form-check-input:checked {
  background-color: #1761fd;
  border-color: #1761fd;
}

/* ==============
  Form-Advanced
===================*/
/* Datepicker */
.datepicker {
  border: 1px solid #333950;
  padding: 8px;
}

.datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover, .datepicker table tr td.today:hover, .datepicker table tr td.selected,
.datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected:hover {
  background-color: #1761fd !important;
  background-image: none;
  box-shadow: none;
  color: #ffffff;
}

.daterangepicker .drp-buttons .btn {
  font-weight: normal;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: rgba(23, 97, 253, 0.1);
  border-color: transparent;
  color: #1761fd;
}

.daterangepicker td.active, .daterangepicker
.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {
  padding: 5px;
}

/* Bootstrap-touchSpin */
.bootstrap-touchspin .input-group-btn-vertical .btn {
  padding: 9px 12px;
}

.bootstrap-touchspin .input-group-btn-vertical i {
  top: 4px;
  left: 8px;
}

/* Prism */
:not(pre) > code[class*="language-"], pre[class*="language-"] {
  background: #333950;
}

/* Rating */
.badge:empty {
  padding: 0;
}

.select2-container--default .select2-selection--single {
  border: 1px solid #33394e;
  height: 38px;
  background-color: #2c3144;
}

.select2-container--default .select2-selection--single:focus {
  outline: none;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #edf0f5;
  line-height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 38px;
  right: 8px;
}

.select2-container--default .select2-selection--multiple {
  background-color: #2c3144;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  margin-top: 7px;
  background-color: #12a4ed;
  border: 1px solid #33394e;
  color: #ffffff;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #ffffff;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  background-color: #2c3144;
  border: 1px solid #33394e;
  color: #d1d1e2;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border: 1px solid #33394e;
  outline: 0;
}

.select2-container .select2-selection--multiple {
  min-height: 38px;
  border: 1px solid #33394e;
}

.select2-container .select2-search--inline .select2-search__field {
  margin-top: 8px;
  color: #d1d1e2;
}

.select2-dropdown {
  background-color: #2c3144;
  border: 1px solid #2d3552;
}

/*---datetimepicker---*/
.dtp-btn-cancel {
  margin-right: 5px;
}

.addon-color-picker .btn {
  padding: 8px;
  line-height: 0;
  border-color: #bfbfbf;
}

/*--colorpicker--*/
.asColorPicker-clear {
  display: none;
  position: absolute;
  top: 8px;
  right: 45px;
  text-decoration: none;
}

.asColorPicker-trigger {
  position: absolute;
  top: 0;
  right: 0;
  height: 38px;
  width: 38px;
  border: 0;
}

.asColorPicker-dropdown {
  max-width: 260px;
}

.asColorPicker-wrap {
  position: relative;
  display: inline-block;
  width: 100%;
  padding-right: 35px;
}

.input-title {
  font-weight: 400;
  font-size: 15px;
  color: #eaf0f9;
}

.dtp > .dtp-content > .dtp-date-view > header.dtp-header {
  background: #1c2d41;
}

.dtp table.dtp-picker-days tr > td > a.selected {
  background: rgba(23, 97, 253, 0.1);
  color: #1761fd;
}

.dtp div.dtp-date, .dtp div.dtp-time {
  background: #21344c;
  color: #1761fd;
}

.dtp div.dtp-actual-year,
.dtp div.dtp-actual-month {
  color: #2a2e40;
  font-size: 14px;
  padding: 8px 0;
}

.dtp > .dtp-content {
  max-height: 555px;
}

.dtp table.dtp-picker-days tr > th {
  color: #335276;
}

.dtp table.dtp-picker-days tr > td > a,
.dtp .dtp-picker-time > a {
  color: #7c9ec7;
  font-size: 12px;
}

.dtp div.dtp-actual-num {
  font-size: 64px;
}

.dtp .p10 > a {
  color: #1c2d41;
  text-decoration: none;
}

.daterangepicker .calendar-table {
  background-color: #32384d;
  border: 1px solid #2d3552;
}

.daterangepicker td.off,
.daterangepicker td.off.in-range,
.daterangepicker td.off.start-date,
.daterangepicker td.off.end-date {
  background-color: #2e3347;
}

.daterangepicker select.hourselect,
.daterangepicker select.minuteselect,
.daterangepicker select.secondselect,
.daterangepicker select.ampmselect {
  background: #262a3b;
  border: 1px solid #2d3552;
  color: #8c97b7;
}

.daterangepicker .drp-buttons {
  border-top: 1px solid #33394e;
}

.error {
  color: #f5325c;
}

.parsley-error {
  border-color: #f5325c;
}

.parsley-errors-list {
  display: none;
  margin: 0;
  padding: 0;
}

.parsley-errors-list.filled {
  display: block;
}

.parsley-errors-list > li {
  font-size: 12px;
  list-style: none;
  color: #f5325c;
  margin-top: 5px;
}

.form-wizard-wrapper label {
  font-size: 13px;
  text-align: right;
}

.wizard > .content > .body input {
  border: 1px solid #33394e;
}

.wizard > .content > .body input:focus {
  border-color: rgba(23, 97, 253, 0.5);
}

.wizard.vertical > .steps {
  width: auto;
}

.wizard > .steps > ul > li {
  width: auto;
  margin-bottom: 10px;
}

.wizard > .steps a,
.wizard > .steps a:active,
.wizard > .steps a:hover {
  margin: 3px;
  padding: 0;
  border-radius: 50px;
}

.wizard > .steps .current a,
.wizard > .steps .current a:active,
.wizard > .steps .current a:hover {
  background: #2c3144;
  color: #1761fd;
  padding: 0 20px 0 0;
}

.wizard > .steps .disabled a,
.wizard > .steps .disabled a:active,
.wizard > .steps .disabled a:hover,
.wizard > .steps .done a,
.wizard > .steps .done a:active,
.wizard > .steps .done a:hover {
  background-color: #2c3144;
  color: #8c97b7;
  padding: 0 20px 0 0;
}

.wizard > .steps .current a .number,
.wizard > .steps .current a:active .number,
.wizard > .steps .current a:hover .number {
  color: #ffffff;
  background-color: #1761fd;
}

.wizard > .steps .disabled a .number,
.wizard > .steps .disabled a:active .number,
.wizard > .steps .disabled a:hover .number,
.wizard > .steps .done a .number,
.wizard > .steps .done a:active .number,
.wizard > .steps .done a:hover .number {
  border-color: #1761fd;
}

.wizard > .content {
  background-color: transparent;
  margin: 0 5px;
  border-radius: 0;
  min-height: 150px;
}

#form-horizontal fieldset {
  margin-top: 20px;
}

.wizard > .content > .body {
  width: 100%;
  height: 100%;
  padding: 15px 0 0;
  position: static;
}

.wizard > .steps .number {
  font-size: 13px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  font-weight: 600;
  text-align: center;
  margin-right: 10px;
  background-color: rgba(23, 97, 253, 0.08);
}

.wizard > .actions,
.wizard.vertical > .actions {
  margin-top: 20px;
}

.wizard > .actions .disabled a,
.wizard > .actions .disabled a:active,
.wizard > .actions .disabled a:hover {
  opacity: .65;
  background: #1761fd;
  color: #ffffff;
  cursor: not-allowed;
}

.wizard > .actions a,
.wizard > .actions a:active,
.wizard > .actions a:hover {
  background: #1761fd;
  border-radius: 4px;
  padding: 8px 15px;
}

@media (max-width: 768px) {
  .wizard > .steps > ul > li {
    width: 50%;
  }
  .form-wizard-wrapper label {
    text-align: left;
  }
}

@media (max-width: 520px) {
  .wizard > .steps > ul > li {
    width: 100%;
  }
}

.mce-panel {
  border: 1px solid #333950 !important;
  background-color: #25293a !important;
  color: #d1d1e2 !important;
}

.mce-label {
  text-shadow: none !important;
  color: #8997bd !important;
}

.mce-tinymce {
  box-shadow: none !important;
}

.mce-btn {
  background: transparent !important;
}

.mce-flow-layout,
.mce-top-part::before {
  box-shadow: none !important;
  background-color: #333950 !important;
}

.mce-caret {
  border-top: 4px solid #d1d1e2 !important;
  opacity: .7 !important;
}

.mce-menubar .mce-caret {
  border-top-color: #d1d1e2 !important;
  opacity: .7 !important;
}

.mce-menubar .mce-menubtn button span,
.mce-btn .mce-txt,
.mce-ico {
  color: #d1d1e2 !important;
}

.mce-btn.mce-disabled button,
.mce-btn.mce-disabled:hover button {
  opacity: .7 !important;
}

.mce-menu-item.mce-disabled,
.mce-menu-item.mce-disabled:hover,
.mce-menu-item:hover,
.mce-menu-item:focus {
  background: #2a2e40 !important;
}

.spinner-border.spinner-border-custom-1 {
  border: 5px double currentColor;
  border-right-color: transparent;
}

.spinner-border.spinner-border-custom-2 {
  border: 5px dotted currentColor;
  border-right-color: transparent;
}

.spinner-border.spinner-border-custom-3 {
  border: 5px groove currentColor;
  border-right-color: transparent;
}

.spinner-border.spinner-border-custom-4 {
  border: 5px dashed currentColor;
  border-right-color: transparent;
}

.spinner-border.spinner-border-custom-5 {
  border: 5px outset currentColor;
  border-right-color: transparent;
}

[data-simplebar] {
  position: relative;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
}

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

.simplebar-offset {
  direction: inherit !important;
  box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

.simplebar-content-wrapper {
  direction: inherit;
  box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%;
  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  visibility: visible;
  max-width: 100%;
  /* Not required for horizontal scroll to trigger */
  max-height: 100%;
  /* Needed for vertical scroll to trigger */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.simplebar-content:before,
.simplebar-content:after {
  content: ' ';
  display: table;
}

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

.simplebar-height-auto-observer-wrapper {
  box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  flex-grow: inherit;
  flex-shrink: 0;
  flex-basis: 0;
}

.simplebar-height-auto-observer {
  box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  user-select: none;
  -webkit-user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all;
}

.simplebar-scrollbar {
  position: absolute;
  right: 0;
  width: 6px;
  min-height: 10px;
}

.simplebar-scrollbar:before {
  position: absolute;
  content: '';
  background: #7081b9;
  border-radius: 8px;
  left: 0;
  right: 0;
  opacity: 0;
  transition: opacity 0.2s linear;
}

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  transition: opacity 0s linear;
}

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px;
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px;
}

.simplebar-track.simplebar-horizontal {
  left: 0;
  height: 11px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: 100%;
  left: 2px;
  right: 2px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  right: auto;
  left: 0;
  top: 2px;
  height: 7px;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

/* Rtl support */
[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0;
}

.hs-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
}

.simplebar-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.gmaps, .gmaps-panaroma {
  height: 300px;
  background: #333950;
  border-radius: 3px;
}

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #ffffff;
  font-size: 16px;
  line-height: 40px;
  background: #1761fd;
  border-radius: 4px;
  padding: 10px 20px;
}

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute;
}

.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid #1761fd;
}

.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid #1761fd;
}

.jvectormap-zoomin,
.jvectormap-zoomout,
.jvectormap-goback {
  position: absolute;
  left: auto;
  right: 0;
  display: inline-block;
  border-radius: 50%;
  background: #2a2e40;
  padding: 5px;
  color: #dfe4ea;
  cursor: pointer;
  line-height: 20px;
  text-align: center;
  box-sizing: content-box;
}

.drop-shadow-map {
  filter: drop-shadow(0px 8px 4px rgba(206, 212, 218, 0.7));
}

.chart-demo {
  height: 370px;
  margin: 20px auto;
}

/* Sparkline chart */
.jqstooltip {
  width: auto !important;
  height: auto !important;
  box-sizing: content-box;
  background-color: #ffffff !important;
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #ffffff !important;
  display: inline-block !important;
  box-shadow: 0 -3px 31px 0 rgba(0, 0, 0, 0.05), 0 6px 20px 0 rgba(0, 0, 0, 0.02);
}

.jqsfield {
  font-size: 12px !important;
  line-height: 18px !important;
  color: #eaf0f9 !important;
}

.chart {
  position: relative;
  display: inline-block;
  width: 110px;
  height: 110px;
  margin-top: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.chart canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.chart.chart-widget-pie {
  margin-top: 5px;
  margin-bottom: 5px;
}

.percent {
  display: inline-block;
  line-height: 110px;
  z-index: 2;
}

.percent:after {
  content: '%';
  margin-left: 0.1em;
  font-size: .8em;
}

/* Morris chart */
.morris-chart text {
  font-family: "Poppins", sans-serif !important;
  color: #8997bd;
}

.morris-hover.morris-default-style {
  border-radius: 5px;
  padding: 10px 12px;
  background: #333950;
  border: none;
  font-family: "Poppins", sans-serif;
  box-shadow: 0 5px 25px 5px rgba(234, 240, 249, 0.14);
}

.morris-hover.morris-default-style .morris-hover-point {
  line-height: 28px;
}

.morris-hover.morris-default-style .morris-hover-row-label {
  background-color: #1761fd;
  color: #ffffff;
  padding: 4px 20px;
  border-radius: 5px 5px 0 0;
  margin: -10px -12px 10px;
}

/* Flot chart */
#flotTip {
  padding: 8px 12px;
  background-color: #2c3144;
  z-index: 100;
  color: #eaf0f9;
  box-shadow: 0 5px 25px 5px rgba(234, 240, 249, 0.14);
  border-radius: 1px;
}

.flot-chart .flot-y-axis .flot-tick-label,
.flot-chart .flot-x-axis .flot-tick-label {
  transform: translateY(2px);
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 400;
  font-family: "Poppins", sans-serif;
  letter-spacing: .5px;
  fill: #8997bd;
}

/* Chartist chart */
.ct-golden-section:before {
  float: none;
}

.ct-chart {
  height: 300px;
}

.ct-grid {
  stroke: rgba(234, 240, 249, 0.09);
  stroke-width: 2px;
  stroke-dasharray: 3px;
}

.ct-chart .ct-label {
  fill: #a4abc5;
  color: #a4abc5;
  font-size: 14px;
  line-height: 1;
}

.ct-chart.simple-pie-chart-chartist .ct-label {
  color: #ffffff;
  fill: #ffffff;
  font-size: 16px;
}

.ct-chart .ct-series.ct-series-a .ct-slice-donut,
.ct-chart .ct-series.ct-series-b .ct-bar {
  stroke: #303e67;
}

.ct-chart .ct-series.ct-series-b .ct-line,
.ct-chart .ct-series.ct-series-b .ct-point {
  stroke: #7081b9;
}

.ct-chart .ct-series.ct-series-c .ct-slice-donut {
  stroke: #9ba7ca;
}

.ct-chart .ct-series.ct-series-a .ct-line,
.ct-chart .ct-series.ct-series-a .ct-point,
.ct-chart .ct-series.ct-series-b .ct-slice-donut,
.ct-chart .ct-series.ct-series-a .ct-bar {
  stroke: #1761fd;
}

.ct-chart .ct-series.ct-series-c .ct-point,
.ct-chart .ct-series.ct-series-c .ct-line {
  stroke: #ffb822;
}

.ct-chart .ct-series.ct-series-c .ct-bar {
  stroke: #2a2e40;
}

.ct-chart .ct-series.ct-series-e .ct-slice-donut,
.ct-chart .ct-series.ct-series-d .ct-bar,
.ct-chart .ct-series.ct-series-d .ct-line,
.ct-chart .ct-series.ct-series-d .ct-point {
  stroke: #03d87f;
}

.ct-chart .ct-series.ct-series-d .ct-slice-donut {
  stroke: #ffb822;
}

.ct-chart .ct-series.ct-series-e .ct-bar,
.ct-chart .ct-series.ct-series-e .ct-line,
.ct-chart .ct-series.ct-series-e .ct-point {
  stroke: #fd3c97;
}

.ct-chart .ct-series.ct-series-f .ct-bar,
.ct-chart .ct-series.ct-series-f .ct-line,
.ct-chart .ct-series.ct-series-f .ct-point {
  stroke: #6d81f5;
}

.ct-chart .ct-series.ct-series-g .ct-bar,
.ct-chart .ct-series.ct-series-g .ct-line,
.ct-chart .ct-series.ct-series-g .ct-point {
  stroke: #e90b3b;
}

.ct-series-a .ct-area,
.ct-series-a .ct-slice-pie {
  fill: #1761fd;
}

.ct-series-b .ct-area,
.ct-series-b .ct-slice-pie {
  fill: #9ba7ca;
}

.ct-series-c .ct-area,
.ct-series-c .ct-slice-pie {
  fill: #303e67;
}

.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  background-clip: padding-box;
  background: #eaf0f9;
  color: #ffffff;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  transition: opacity .2s linear;
}

.chartist-tooltip.tooltip-show {
  opacity: 1;
}

#donut-chart #donut-chart-container .legend {
  position: relative;
  margin-right: -50px;
  top: -50px;
}

.apexcharts-gridlines-horizontal,
.apexcharts-gridlines-vertical,
.apexcharts-grid,
.apexcharts-gridline,
.apexcharts-xaxis-tick,
.apexcharts-xaxis line,
.apexcharts-gridline line {
  pointer-events: none;
  stroke: #32374a;
}

.apexcharts-radar-series line,
.apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-radar-series path, .apexcharts-radar-series polygon {
  pointer-events: none;
  stroke: #3b415a;
}

.apexcharts-legend-text {
  color: #8997bd !important;
  font-family: "Poppins", sans-serif !important;
}

.apexcharts-pie-label {
  fill: #ffffff !important;
}

.apexcharts-datalabels text,
.apexcharts-data-labels,
.apexcharts-xaxis text,
.apexcharts-yaxis text {
  font-family: "Roboto", sans-serif !important;
  fill: #8997bd;
}

.apexcharts-yaxis-annotation-label {
  color: #8997bd !important;
}

.apexcharts-bar-series.apexcharts-plot-series .apexcharts-datalabels .apexcharts-data-labels text,
.apexcharts-series .apexcharts-datalabels .apexcharts-data-labels text {
  fill: #a4abc5 !important;
  font-size: 11px !important;
  font-weight: 500 !important;
}

.apexcharts-point-annotations text,
.apexcharts-xaxis-annotations text,
.apexcharts-yaxis-annotations text {
  fill: #ffffff;
}

.britechart,
.tick text {
  font-family: "Poppins", sans-serif;
  font-size: .75rem;
}

.extended-x-line,
.extended-y-line,
.horizontal-grid-line,
.vertical-grid-line {
  stroke: #303e67;
}

.bar-chart .percentage-label,
.donut-text,
.legend-entry-name,
.legend-entry-value,
.tick text {
  fill: #8997bd;
}

.apex-charts {
  min-height: 10px !important;
}

.apexcharts-tooltip-title {
  background: #2c3144 !important;
  color: #eaf0f9;
  border-color: #2a2e40 !important;
}

.dash-apex-chart .apexcharts-toolbar {
  display: none !important;
}

.apexcharts-tooltip {
  background: #2c3144 !important;
  border-color: #2d3552 !important;
  white-space: normal !important;
  box-shadow: none !important;
}

.apexcharts-xaxistooltip {
  background-color: #25293a !important;
  border-color: #25293a !important;
  color: #d1d1e2 !important;
}

.apexcharts-xaxistooltip-top:before,
.apexcharts-xaxistooltip-top:after {
  border-top-color: #25293a !important;
}

.apexcharts-xaxistooltip-bottom:before,
.apexcharts-xaxistooltip-bottom:after {
  border-bottom-color: #25293a !important;
}

.apexcharts-xaxistooltip:before,
.apexcharts-xaxistooltip:after {
  border-color: #25293a;
}

.apexcharts-title-text,
.apexcharts-datalabel-label {
  color: #d1d1e2;
}

.apexcharts-datalabel-value {
  color: #2a2e40;
}

.apexcharts-legend-marker {
  margin-right: 6px;
  height: 5px !important;
  vertical-align: middle;
}

.apexcharts-yaxis > line,
.apexcharts-xaxis > line {
  stroke-width: 0;
}

.apexcharts-yaxis .apexcharts-yaxis-texts-g .apexcharts-text.apexcharts-yaxis-label,
.apexcharts-yaxis .apexcharts-yaxis-texts-g .apexcharts-text.apexcharts-xaxis-label,
.apexcharts-yaxis .apexcharts-xaxis-texts-g .apexcharts-text.apexcharts-yaxis-label,
.apexcharts-yaxis .apexcharts-xaxis-texts-g .apexcharts-text.apexcharts-xaxis-label,
.apexcharts-xaxis .apexcharts-yaxis-texts-g .apexcharts-text.apexcharts-yaxis-label,
.apexcharts-xaxis .apexcharts-yaxis-texts-g .apexcharts-text.apexcharts-xaxis-label,
.apexcharts-xaxis .apexcharts-xaxis-texts-g .apexcharts-text.apexcharts-yaxis-label,
.apexcharts-xaxis .apexcharts-xaxis-texts-g .apexcharts-text.apexcharts-xaxis-label {
  font-weight: 500;
}

.apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value, .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
  fill: #8c97b7;
  color: #8c97b7;
  font-family: "Roboto", sans-serif;
  font-weight: 600;
}

/*clock*/
.superpose {
  color: #333950;
  height: 350px;
  width: 100%;
}

.superpose .superclock {
  position: relative;
  width: 300px;
  margin: auto;
}

.superpose .superclock .superclock1 {
  position: absolute;
  left: 10px;
  top: 10px;
}

.superpose .superclock .superclock2 {
  position: absolute;
  left: 60px;
  top: 60px;
}

.superpose .superclock .superclock3 {
  position: absolute;
  left: 110px;
  top: 110px;
}

.calendar-cta {
  background-image: url("../images/pattern-sm.png");
  background-repeat: repeat;
}

.calendar {
  float: left;
  margin-bottom: 0;
}

.none-border .modal-footer {
  border-top: none;
}

.fc-toolbar h2 {
  font-size: 16px;
  line-height: 40px;
  text-transform: uppercase;
}

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar button:focus,
.fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0;
}

.fc-day {
  background: #2c3144;
}

.fc-widget-header {
  background-color: #333950;
  color: #d1d1e2;
}

.fc-widget-content {
  border: 1px solid #7081b9;
}

.fc th.fc-widget-header {
  font-size: 13px;
  line-height: 10px;
  padding: 15px 0;
  text-transform: uppercase;
  font-weight: 600;
}

.fc button {
  height: auto;
  padding: 6px 16px;
}

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
  border-color: #333950;
}

.fc-button {
  background: transparent;
  border: 1px solid #333950;
  color: #8c97b7;
  font-family: inherit;
  text-transform: capitalize;
}

.fc-state-active,
.fc-state-down {
  box-shadow: none;
  background: #1761fd;
  border-color: none;
  color: #ffffff;
  text-shadow: none;
}

.fc-text-arrow {
  font-family: arial;
  font-size: 16px;
}

.fc-state-hover {
  background: #8997bd;
}

.fc-state-highlight {
  background: #303e67;
}

.fc-cell-overlay {
  background: #303e67;
}

.fc-unthemed td.fc-today {
  background: rgba(23, 97, 253, 0.08) !important;
}

.fc .fc-row .fc-content-skeleton td {
  background: transparent !important;
}

.fc-day-top .fc-mon .fc-today {
  background: rgba(23, 97, 253, 0.1) !important;
}

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 13px;
  margin: 5px;
  padding: 7px 5px;
  text-align: center;
  background-color: rgba(23, 97, 253, 0.18);
  color: #1761fd;
}

.external-event {
  color: #ffffff;
  cursor: move;
  margin: 10px 0;
  padding: 6px 10px;
}

.fc-basic-view td.fc-week-number span {
  padding-right: 5px;
}

.fc-basic-view td.fc-day-number {
  padding-right: 5px;
}

tr:first-child > td > .fc-day-grid-event {
  border-left: 5px solid;
}

.fc-button {
  text-shadow: none;
  box-shadow: none;
}

.fc-button:hover,
.fc-state-default:hover {
  background: #1761fd;
  color: #ffffff;
  border-color: #2c3144;
}

.fc-day.fc-widget-content.fc-other-month.fc-past,
.fc-day.fc-widget-content.fc-other-month.fc-future {
  background-image: linear-gradient(-45deg, #333950 25%, transparent 25%, transparent 50%, #2c3144 50%, #303e67 75%, transparent 75%, transparent);
  background-size: 10px 10px;
}

.fc-button-primary:disabled,
.fc-button-primary:not(:disabled):active, .fc-button-primary:not(:disabled).fc-button-active {
  background: rgba(23, 97, 253, 0.1);
  color: #1761fd;
  border-color: #2c3144;
  opacity: 1;
}

.fc-button-primary:not(:disabled):active:focus,
.fc-button-primary:not(:disabled).fc-button-active:focus,
.fc-button-primary:focus {
  box-shadow: none;
}

.fc-unthemed .fc-divider,
.fc-unthemed .fc-popover .fc-header,
.fc-unthemed .fc-list-heading td {
  background: #333950;
}

@media (max-width: 767.98px) {
  .fc-toolbar {
    display: block;
  }
}

/* ==============
  Range slider
===================*/
.irs-from, .irs-to, .irs-single {
  background: #1761fd;
}

.irs-from:after, .irs-to:after, .irs-single:after {
  border-top-color: #1761fd;
}

.irs--round .irs-bar,
.irs--flat .irs-handle.state_hover > i:first-child,
.irs--flat .irs-handle:hover > i:first-child,
.irs--flat .irs-handle > i:first-child,
.irs--flat .irs-from,
.irs--flat .irs-to,
.irs--flat .irs-single,
.irs--flat .irs-bar {
  background-color: #1761fd;
}

.irs--flat .irs-from:before,
.irs--flat .irs-to:before,
.irs--flat .irs-single:before {
  border-top-color: #1761fd;
}

.irs--sharp .irs-bar,
.irs--sharp .irs-handle {
  background-color: #03d87f;
}

.irs--sharp .irs-handle > i:first-child {
  border-top-color: #03d87f;
}

.irs--sharp .irs-line {
  background-color: #303e67;
}

.irs--square .irs-handle {
  border-color: #1761fd;
}

.irs--square .irs-from,
.irs--square .irs-to,
.irs--square .irs-single,
.irs--square .irs-bar {
  background-color: #1761fd;
}

.irs--flat .irs-min, .irs--flat .irs-max,
.irs--round .irs-min, .irs--round .irs-max,
.irs--square .irs-min, .irs--square .irs-max,
.irs--modern .irs-min, .irs--modern .irs-max {
  top: 0;
  font-size: 12px;
  line-height: 1.333;
  text-shadow: none;
  padding: 1px 5px;
  color: #d1d1e2;
  background-color: #333950;
  border-radius: 5px;
}

.irs--modern .irs-from,
.irs--modern .irs-to,
.irs--modern .irs-single,
.irs--round .irs-from,
.irs--round .irs-to,
.irs--round .irs-single {
  font-size: 11px;
  line-height: 1.333;
  text-shadow: none;
  padding: 2px 5px;
  background-color: #1761fd;
  color: #ffffff;
  border-radius: 5px;
}

.irs--modern .irs-from:before,
.irs--modern
.irs-to:before,
.irs--modern .irs-single:before,
.irs--round .irs-from:before,
.irs--round .irs-to:before,
.irs--round .irs-single:before {
  content: "";
  border: 3px solid transparent;
  border-top-color: #1761fd;
}

.irs--round .irs-line,
.irs--square .irs-line,
.irs--flat .irs-line,
.irs--modern .irs-line {
  background: linear-gradient(to bottom, #303e67 0%, #333950 100%);
  border: 1px solid #2d3552;
}

.irs--sharp .irs-grid-pol,
.irs--flat .irs-grid-pol,
.irs--square .irs-grid-pol,
.irs--modern .irs-grid-pol {
  background-color: #7081b9;
}

.irs--square .irs-handle,
.irs--round .irs-handle.state_hover,
.irs--round .irs-handle:hover {
  background-color: #2c3144;
}

.irs--modern .irs-bar {
  background: #1761fd;
  background: linear-gradient(135deg, rgba(23, 97, 253, 0.5) 0%, #1761fd 36%, #0251f8 61%, rgba(23, 97, 253, 0.5) 100%);
}

.irs--round .irs-handle {
  border: 4px solid #1761fd;
  background-color: transparent;
  box-shadow: 0 1px 3px rgba(23, 97, 253, 0.3);
}

.showcase__mark {
  display: block;
  position: absolute;
  top: 35px;
  background: #f5325c;
  transform: rotate(-45deg);
  padding: 1px 3px;
  border-radius: 3px;
  color: #ffffff;
  margin-left: -18px;
}

.irs--modern .irs-handle > i:nth-child(1) {
  width: 8px;
  height: 8px;
}

.irs--modern .irs-handle > i:nth-child(1),
.irs--modern .irs-handle > i:nth-child(2) {
  background: #2c3144;
}

.swal2-popup {
  background: #2c3144;
}

.swal2-popup .swal2-title {
  font-size: 24px;
  font-weight: 500;
  color: #d1d1e2;
}

.swal2-popup .swal2-content {
  color: #8c97b7;
}

.swal2-popup .swal2-styled.swal2-confirm {
  border: 0;
  border-radius: .25em;
  background: initial;
  background-color: #1761fd;
  color: #ffffff;
  font-size: 1.0625em;
}

.swal2-popup .swal2-styled:focus {
  outline: 0;
  box-shadow: 0 0 0 2px #2c3144, 0 0 0 4px rgba(23, 97, 253, 0.4);
}

.swal2-popup .swal2-styled.swal2-cancel {
  border: 0;
  border-radius: .25em;
  background: initial;
  background-color: #f5325c;
  color: #ffffff;
  font-size: 1.0625em;
}

.swal2-popup .swal2-styled.swal2-cancel:focus {
  outline: 0;
  box-shadow: 0 0 0 2px #2c3144, 0 0 0 4px rgba(245, 50, 92, 0.5);
}

.swal2-popup .swal2-styled.swal2-cancel {
  border: 1px solid transparent;
}

.swal2-actions .btn-success:not(:disabled):not(.disabled):active,
.swal2-actions .btn-success,
.swal2-actions .btn-success:active {
  background-color: #1761fd;
  border: 1px solid #1761fd;
}

.swal2-actions .btn-success:focus {
  box-shadow: 0 0 0 2px #2c3144, 0 0 0 4px rgba(23, 97, 253, 0.4);
}

.swal2-actions .btn-danger:not(:disabled):not(.disabled):active {
  color: #ffffff;
  background-color: #f5325c;
  border-color: #f5325c;
}

.swal2-actions .btn-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow: 0 0 0 2px #2c3144, 0 0 0 4px rgba(245, 50, 92, 0.5);
}

.swal2-actions .btn-danger {
  background-color: #f5325c;
  border: 1px solid #f5325c;
}

.swal2-actions .btn-danger:active {
  background-color: #f5325c;
  border: 1px solid #f5325c;
}

.swal2-actions .btn-danger:hover {
  background-color: #f5325c;
  border: 1px solid #f5325c;
}

.swal2-actions .btn-danger:focus {
  background-color: #f5325c;
  border: 1px solid #f5325c;
  box-shadow: 0 0 0 2px #2c3144, 0 0 0 4px rgba(245, 50, 92, 0.5);
}

.swal2-icon.swal2-question {
  border-color: #fd3c97;
  color: #fd3c97;
}

.swal2-footer {
  border-top: 1px solid #33394e;
}

.swal2-popup.swal2-toast {
  background-color: #2c3144;
  box-shadow: 0 0 0.625em #2a2e40;
}

.custom-dd {
  max-width: 100%;
}

.dd3-content,
.dd-handle {
  height: 40px;
  padding: 8px 10px;
  border: 1px solid #333950;
  background-color: #31374d;
  color: #d1d1e2;
  border-radius: 3px;
  font-weight: normal;
}

.dd3-content:hover,
.dd-handle:hover {
  background-color: #373d56;
  color: #1761fd;
}

.dd-item > button {
  height: 25px;
  margin: 8px 0 0;
  font-size: 18px;
}

.dd-item > button:before {
  color: #d1d1e2;
}

.dd3-item {
  margin: 5px 0;
}

.dd3-item .dd3-handle {
  position: absolute;
  margin: 0;
  left: 0;
  top: 0;
  cursor: pointer;
  width: 30px;
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  border: 1px solid #1761fd;
  background: #1761fd;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.dd3-item .dd3-handle:hover {
  background: #3072fd;
}

.dd3-item .dd3-handle:before {
  content: "\f0c9";
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 600;
  display: block;
  position: absolute;
  left: 0;
  top: 9px;
  width: 100%;
  text-align: center;
  text-indent: 0;
  color: #ffffff;
  font-size: 14px;
}

.dd3-content-p {
  padding: 8px 10px 8px 40px;
}

.dd-empty,
.dd-placeholder {
  background: #25293a;
  border: 1px dashed #33394e;
}

/*! Color themes for Google Code Prettify | MIT License | github.com/jmblog/color-themes-for-google-code-prettify */
.prettyprint {
  background: #2c3144;
  font-family: Menlo, "Bitstream Vera Sans Mono", "DejaVu Sans Mono", Monaco, Consolas, monospace;
  border: 0 !important;
  line-height: 20px !important;
}

.pln {
  color: #1d2c48 !important;
}

/* Specify class=linenums on a pre to get line numbering */
ol.linenums {
  margin-top: 0;
  margin-bottom: 0;
  color: #cccccc;
}

li.L0,
li.L1,
li.L2,
li.L3,
li.L4,
li.L5,
li.L6,
li.L7,
li.L8,
li.L9 {
  padding-left: 1em;
  background-color: #2c3144;
  list-style-type: decimal !important;
}

@media screen {
  /* string content */
  .str {
    color: #d14;
  }
  /* keyword */
  .kwd {
    color: #333;
  }
  /* comment */
  .com {
    color: #307df3 !important;
  }
  /* type name */
  .typ {
    color: #ff0000 !important;
  }
  /* literal value */
  .lit {
    color: #458;
  }
  /* punctuation */
  .pun {
    color: #333;
  }
  /* lisp open bracket */
  .opn {
    color: #333;
  }
  /* lisp close bracket */
  .clo {
    color: #333;
  }
  /* markup tag name */
  .tag {
    color: #a10000 !important;
  }
  /* markup attribute name */
  .atn {
    color: #ff0000 !important;
  }
  /* markup attribute value */
  .atv {
    color: #0000ff !important;
  }
  /* declaration */
  .dec {
    color: #333;
  }
  /* variable name */
  .var {
    color: #a10000 !important;
  }
  /* function name */
  .fun {
    color: #a10000 !important;
  }
}

.tippy-tooltip .light-theme[data-animatefill] {
  background-color: transparent;
}

.light-theme {
  color: #d1d1e2;
  box-shadow: 0 0 20px 4px #303e67, 0 4px 80px -8px #7081b9, 0 4px 4px -2px #303e67;
  background-color: #ffffff;
}

.light-theme .tippy-backdrop {
  background-color: #ffffff;
}

.light-theme .tippy-roundarrow {
  fill: #ffffff;
}

.gradient-theme {
  font-weight: 400;
}

.gradient-theme .tippy-backdrop {
  color: #ffffff;
  background: #6d81f5;
  background: linear-gradient(to right, #f5325c, #6d81f5);
}

.tippy-popper[x-placement^=top] .tippy-tooltip.light-theme .tippy-arrow {
  border-top: 7px solid #ffffff;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
}

.tippy-popper[x-placement^=bottom] .tippy-tooltip.light-theme .tippy-arrow {
  border-bottom: 7px solid #ffffff;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
}

.tippy-popper[x-placement^=left] .tippy-tooltip.light-theme .tippy-arrow {
  border-left: 7px solid #ffffff;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

.tippy-popper[x-placement^=right] .tippy-tooltip.light-theme .tippy-arrow {
  border-right: 7px solid #ffffff;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../fonts/inter-light.woff2?v=3.13") format("woff2"), url("../fonts/inter-light.woff?v=3.13") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../fonts/inter-regular.woff2?v=3.13") format("woff2"), url("../fonts/inter-regular.woff?v=3.13") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../fonts/inter-medium.woff2?v=3.13") format("woff2"), url("../fonts/inter-medium.woff?v=3.13") format("woff");
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../fonts/inter-bold.woff2?v=3.13") format("woff2"), url("../fonts/iter-bold.woff?v=3.13") format("woff");
}

.tox-tinymce {
  border: 1px solid #2d3552 !important;
}

.tox {
  color: #edf0f5 !important;
}

.tox .tox-menubar {
  background-color: #25293a !important;
}

.tox .tox-mbtn {
  color: #edf0f5 !important;
}

.tox .tox-mbtn:hover {
  background-color: #1d202e !important;
}

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
  background-color: #25293a !important;
  background: url("data:image/svg+xml;charset=utf8,%3Csvg height='39px' viewBox='0 0 40 39px' width='40' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='0' y='38px' width='100' height='1' fill='#000'/%3E%3C/svg%3E") left 0 top 0 #25293a !important;
}

.tox .tox-tbtn svg,
.tox .tox-tbtn--disabled svg,
.tox .tox-tbtn--disabled:hover svg,
.tox .tox-tbtn:disabled svg,
.tox .tox-tbtn:disabled:hover svg {
  fill: #d1d1e2 !important;
}

.tox .tox-tbtn svg:hover,
.tox .tox-tbtn--disabled svg:hover,
.tox .tox-tbtn--disabled:hover svg:hover,
.tox .tox-tbtn:disabled svg:hover,
.tox .tox-tbtn:disabled:hover svg:hover {
  background-color: #1d202e !important;
}

.tox .tox-tbtn--enabled,
.tox .tox-tbtn {
  color: #d1d1e2 !important;
}

.tox .tox-tbtn--enabled:hover,
.tox .tox-tbtn:hover {
  color: #d1d1e2 !important;
  background: #1d202e !important;
}

.tox .tox-split-button:hover {
  box-shadow: none !important;
}

.tox .tox-tbtn--enabled,
.tox .tox-split-button:focus {
  color: #d1d1e2 !important;
  background: #1d202e !important;
}

.tox .tox-edit-area__iframe {
  background: #25293a !important;
  border-top: 1px solid #2d3552 !important;
}

.tox .tox-statusbar {
  color: #8c97b7 !important;
  background: #1d202e !important;
  border-top: 1px solid #2d3552 !important;
}

.tox .tox-menubar + .tox-toolbar,
.tox .tox-menubar + .tox-toolbar-overlord .tox-toolbar__primary {
  border-top: 1px solid #2d3552 !important;
}

.tox .tox-statusbar a,
.tox .tox-statusbar__path-item,
.tox .tox-statusbar__wordcount {
  color: #8997bd !important;
}

.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
  border-right: 1px solid #2d3552 !important;
}

#basic-conf_ifr body {
  color: #edf0f5 !important;
}

.avatar-md {
  height: 36px;
  width: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 50%;
  font-size: 18px;
  display: inline-block;
}

.img-group {
  display: inline-block;
}

.img-group .avatar-badge {
  position: absolute;
  right: 1px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  box-shadow: 0 0 0 2px #2c3144;
  z-index: 2;
}

.img-group .user-avatar + .user-avatar-group {
  margin-left: -18px;
}

.img-group .user-avatar {
  position: relative;
  display: inline-block;
}

.img-group .user-avatar:focus, .img-group .user-avatar:hover {
  z-index: 2;
}

.img-group .user-avatar .online {
  background: #03d87f;
}

.img-group .user-avatar .offline {
  background: #9ba7ca;
}

.img-group .user-avatar img {
  box-shadow: 0 0 0 2px #2a2e40;
}

.avatar-box {
  position: relative;
  display: inline-block;
}

.avatar-box .avatar-title {
  display: flex;
  width: 100%;
  height: 100%;
  color: #2a2e40;
  align-items: center;
  justify-content: center;
}

.slimScrollDiv {
  height: auto !important;
}

.icon-dual {
  color: #a1acb8;
  fill: rgba(161, 172, 184, 0.12);
}

.border-bottom-dashed {
  border-bottom: 1px dashed #2d3552 !important;
}

.w-90 {
  width: 90%;
}

.dual-btn-icon i {
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 20px;
  background-color: #1761fd;
  color: #ffffff;
  display: inline-block;
  margin: 0 -4px 0 14px;
}

.code-padding {
  padding: 1rem;
}

code[class*="lang-html"], pre[class*="lang-html"] {
  color: #393A34;
  font-family: "Consolas", "Bitstream Vera Sans Mono", "Courier New", Courier, monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  font-size: 0.95em;
  line-height: 1.2em;
  tab-size: 4;
  hyphens: none;
}

.thumb-xxs {
  height: 24px !important;
  width: 24px !important;
  font-size: 10px;
  font-weight: 700;
}

.thumb-xs {
  height: 32px !important;
  width: 32px !important;
  font-size: 10px;
  font-weight: 700;
}

.thumb-sm {
  height: 36px !important;
  width: 36px !important;
  font-size: 12px;
  font-weight: 700;
}

.thumb-md {
  height: 42px;
  width: 42px;
  font-size: 14px;
  font-weight: 700;
}

.thumb-lg {
  height: 48px;
  width: 48px;
  font-size: 16px;
  font-weight: 700;
}

.thumb-xl {
  height: 60px;
  width: 60px;
  font-size: 22px;
  font-weight: 700;
}

.thumb-xxl {
  height: 72px;
  width: 72px;
  font-size: 28px;
  font-weight: 700;
}

.badge {
  line-height: 10px;
}

/* Page titles */
.title-text,
.card-title {
  text-transform: capitalize;
  letter-spacing: 0.02em;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  color: #b9c1d4;
  text-shadow: 0 0 1px rgba(51, 57, 80, 0.1);
  font-family: "Poppins", sans-serif;
}

.page-title-box {
  padding: 16px 0;
}

.page-title-box .page-title {
  font-size: 17px;
  margin: 0;
  color: #dfe4ea;
  font-weight: 500;
  line-height: 18px;
}

.page-title-box .breadcrumb {
  font-size: 12px;
  margin-bottom: 0;
  font-weight: 500;
  font-family: "Poppins", sans-serif;
  padding: 2px 0;
  background-color: transparent;
}

.title-border:after {
  content: '';
  height: 3px;
  width: 80px;
  position: absolute;
  top: 30px;
  left: 12px;
  border: 1.5px solid rgba(23, 97, 253, 0.15);
  border-radius: 63px;
}

.dash-info-carousel .carousel-item h2 {
  font-weight: 500;
  color: #d1d1e2;
  font-size: 16px;
}

.dash-info-carousel .carousel-control-prev,
.dash-info-carousel .carousel-control-next {
  width: 30px;
  height: 30px;
  background-color: #2a2e40;
  border-radius: 50%;
  opacity: 1;
}

.dash-info-carousel .carousel-control-prev:hover,
.dash-info-carousel .carousel-control-next:hover {
  opacity: .9;
}

.dash-info-carousel .carousel-control-prev {
  left: auto;
  right: 38px;
  top: 0;
}

.dash-info-carousel .carousel-control-prev .carousel-control-prev-icon {
  background-image: none;
}

.dash-info-carousel .carousel-control-prev:after {
  content: '\f054';
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900;
  font-size: .875rem;
  color: #d1d1e2;
  line-height: 1.875rem;
  margin-right: 0.48rem;
}

.dash-info-carousel .carousel-control-next {
  left: auto;
  right: 0;
  top: 0;
}

.dash-info-carousel .carousel-control-next .carousel-control-next-icon {
  background-image: none;
}

.dash-info-carousel .carousel-control-next:after {
  content: '\f053';
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900;
  font-size: .875rem;
  color: #d1d1e2;
  line-height: 1.875rem;
  margin-right: 0.48rem;
}

:not(pre) > code[class*="language-"], pre[class*="language-"] {
  background: transparent;
}

.popover {
  border-color: #303e67;
}

.popover .popover-header {
  margin-top: 0;
  background-color: #333950;
}

.accordion > .card > .card-header {
  background-color: #34394f;
}

.custom-accordion {
  background-color: #1761fd !important;
}

.custom-accordion a:first-child {
  color: #ffffff !important;
}

.icon-demo-content {
  text-align: center;
}

.icon-demo-content svg,
.icon-demo-content i {
  display: block;
  font-size: 24px;
  margin: 0 auto 5px;
  color: #8c97b7;
}

.icon-demo-content .col-sm-6 {
  margin-bottom: 40px;
}

.icon-demo-content .col-sm-6:hover i {
  color: #1761fd;
}

.icon-demo-content .col-sm-6:hover path:nth-of-type(1) {
  fill: #1761fd;
}

.icon-demo-content .col-sm-6:hover path:nth-of-type(2) {
  fill: #a4abc5;
}

.br-theme-bars-square .br-widget a {
  background-color: #2c3144;
}

.br-theme-fontawesome-stars-o .br-widget a:after,
.br-theme-css-stars .br-widget a:after,
.br-theme-fontawesome-stars .br-widget a:after {
  color: #303e67;
}

code[class*="language-"], pre[class*="language-"] {
  text-shadow: none;
  color: #d1d1e2;
}

.token.operator,
.token.entity,
.token.url,
.language-css
.token.string,
.style .token.string {
  color: #03d87f;
  background: rgba(137, 151, 189, 0.5);
}

.icon-xs {
  width: 15px;
  height: 15px;
}

.icon-sm {
  width: 22px;
  height: 22px;
}

.icon-md {
  width: 30px;
  height: 30px;
}

.icon-lg {
  width: 36px;
  height: 36px;
}

.icon-xl {
  width: 42px;
  height: 42px;
}

.daterangepicker {
  background-color: #2c3144;
  border: 1px solid #2d3552;
  box-shadow: 0 3px 12px #25293a;
}

.daterangepicker .ranges li.active {
  background-color: rgba(23, 97, 253, 0.08);
  color: #1761fd;
}

.daterangepicker .ranges li:hover {
  background-color: rgba(23, 97, 253, 0.08);
  color: #1761fd;
}

.contact-icon i {
  border: 1px solid #2d3552;
  border-radius: 50%;
  padding: 6px;
  font-size: 10px;
  color: #9ba7ca;
}

.form-wizard-wrapper label {
  text-align: left;
}

.bg-black {
  background-color: #242838 !important;
  color: #ffffff;
}

.bg-card {
  background-color: #2c3144 !important;
}

.bg-light-alt {
  background-color: #31364a !important;
}

.bg-soft-primary {
  background-color: rgba(23, 97, 253, 0.08) !important;
  color: #1761fd !important;
}

.bg-soft-secondary {
  background-color: rgba(155, 167, 202, 0.08) !important;
  color: #9ba7ca !important;
}

.bg-soft-success {
  background-color: rgba(3, 216, 127, 0.08) !important;
  color: #03d87f !important;
}

.bg-soft-warning {
  background-color: rgba(255, 184, 34, 0.08) !important;
  color: #ffb822 !important;
}

.bg-soft-info {
  background-color: rgba(18, 164, 237, 0.08) !important;
  color: #12a4ed !important;
}

.bg-soft-danger {
  background-color: rgba(245, 50, 92, 0.08) !important;
  color: #f5325c !important;
}

.bg-soft-pink {
  background-color: rgba(253, 60, 151, 0.08) !important;
  color: #fd3c97 !important;
}

.bg-soft-purple {
  background-color: rgba(109, 129, 245, 0.08) !important;
  color: #6d81f5 !important;
}

.bg-soft-blue {
  background-color: rgba(23, 97, 253, 0.08) !important;
  color: #1761fd !important;
}

.bg-soft-dark {
  background-color: rgba(234, 240, 249, 0.08) !important;
  color: #eaf0f9 !important;
}

.bg-primary-50 {
  background-color: rgba(23, 97, 253, 0.5) !important;
  color: #1761fd !important;
}

.bg-secondary-50 {
  background-color: rgba(155, 167, 202, 0.5) !important;
  color: #9ba7ca !important;
}

.bg-success-50 {
  background-color: rgba(3, 216, 127, 0.5) !important;
  color: #03d87f !important;
}

.bg-warning-50 {
  background-color: rgba(255, 184, 34, 0.5) !important;
  color: #ffb822 !important;
}

.bg-info-50 {
  background-color: rgba(18, 164, 237, 0.5) !important;
  color: #12a4ed !important;
}

.bg-danger-50 {
  background-color: rgba(245, 50, 92, 0.5) !important;
  color: #f5325c !important;
}

.bg-pink-50 {
  background-color: rgba(253, 60, 151, 0.5) !important;
  color: #fd3c97 !important;
}

.bg-purple-50 {
  background-color: rgba(109, 129, 245, 0.5) !important;
  color: #6d81f5 !important;
}

.bg-blue-50 {
  background-color: rgba(23, 97, 253, 0.5) !important;
  color: #1761fd !important;
}

.bg-dark-50 {
  background-color: rgba(234, 240, 249, 0.5) !important;
  color: #eaf0f9 !important;
}

.bg-primary-gradient {
  background: linear-gradient(14deg, #4d79f6 36%, #39add0 100%);
}

.bg-secondary-gradient {
  background: linear-gradient(14deg, #42cef1 36%, #63ddfb 100%);
}

.bg-success-gradient {
  background: linear-gradient(14deg, #22d4b3 36%, #71e6d1 100%);
}

.bg-info-gradient {
  background: linear-gradient(14deg, #4b545d 36%, #67737b 100%);
}

.bg-warning-gradient {
  background: linear-gradient(14deg, #efc039 36%, #f1cb60 100%);
}

.bg-danger-gradient {
  background: linear-gradient(14deg, #fb7589 36%, #ff9595 100%);
}

.bg-pink-gradient {
  background: linear-gradient(14deg, #FE5D9E 36%, #8E73FE 100%);
}

.bg-purple-gradient {
  background: linear-gradient(14deg, #6d81f5 36%, #8498f3 100%);
}

.bg-blue-gradient {
  background: linear-gradient(14deg, #11368a 36%, #326eca 100%);
}

.bg-dark-gradient {
  background: linear-gradient(14deg, #12100e 36%, #2b4162 100%);
}

.bg-light-gradient {
  background: linear-gradient(14deg, #c3cbdc 36%, #edf1f4 100%);
}

.bg-custom-gradient {
  background: linear-gradient(14deg, #521c85 36%, #4400ff 100%);
}

.badge {
  box-shadow: none;
  font-size: 11px !important;
}

.badge.badge-soft-primary {
  background-color: rgba(23, 97, 253, 0.15) !important;
  color: #1761fd !important;
  box-shadow: 0px 0px 13px 0px rgba(23, 97, 253, 0.05);
}

.badge.badge-soft-secondary {
  background-color: rgba(155, 167, 202, 0.15) !important;
  color: #9ba7ca !important;
  box-shadow: 0px 0px 13px 0px rgba(155, 167, 202, 0.05);
}

.badge.badge-soft-success {
  background-color: rgba(3, 216, 127, 0.15) !important;
  color: #03d87f !important;
  box-shadow: 0px 0px 13px 0px rgba(3, 216, 127, 0.05);
}

.badge.badge-soft-warning {
  background-color: rgba(255, 184, 34, 0.15) !important;
  color: #ffb822 !important;
  box-shadow: 0px 0px 13px 0px rgba(255, 184, 34, 0.05);
}

.badge.badge-soft-info {
  background-color: rgba(18, 164, 237, 0.15) !important;
  color: #12a4ed !important;
  box-shadow: 0px 0px 13px 0px rgba(18, 164, 237, 0.05);
}

.badge.badge-soft-danger {
  background-color: rgba(245, 50, 92, 0.15) !important;
  color: #f5325c !important;
  box-shadow: 0px 0px 13px 0px rgba(245, 50, 92, 0.05);
}

.badge.badge-soft-pink {
  background-color: rgba(253, 60, 151, 0.15) !important;
  color: #fd3c97 !important;
  box-shadow: 0px 0px 13px 0px rgba(253, 60, 151, 0.05);
}

.badge.badge-soft-purple {
  background-color: rgba(109, 129, 245, 0.15) !important;
  color: #6d81f5 !important;
  box-shadow: 0px 0px 13px 0px rgba(109, 129, 245, 0.05);
}

.badge.badge-soft-dark {
  background-color: rgba(234, 240, 249, 0.15) !important;
  color: #eaf0f9 !important;
  box-shadow: 0px 0px 13px 0px rgba(234, 240, 249, 0.05);
}

.shadow-primary {
  box-shadow: 0 6px 20px 0 rgba(23, 97, 253, 0.5) !important;
}

.shadow-secondary {
  box-shadow: 0 6px 20px 0 rgba(155, 167, 202, 0.5) !important;
}

.shadow-success {
  box-shadow: 0 6px 20px 0 rgba(3, 216, 127, 0.5) !important;
}

.shadow-warning {
  box-shadow: 0 6px 20px 0 rgba(255, 184, 34, 0.5) !important;
}

.shadow-info {
  box-shadow: 0 6px 20px 0 rgba(18, 164, 237, 0.5) !important;
}

.shadow-danger {
  box-shadow: 0 6px 20px 0 rgba(245, 50, 92, 0.5) !important;
}

.shadow-pink {
  box-shadow: 0 6px 20px 0 rgba(253, 60, 151, 0.5) !important;
}

.shadow-purple {
  box-shadow: 0 6px 20px 0 rgba(109, 129, 245, 0.5) !important;
}

.shadow-orange {
  box-shadow: 0 6px 20px 0 rgba(255, 133, 0, 0.5) !important;
}

.shadow-dark {
  box-shadow: 0 6px 20px 0 rgba(234, 240, 249, 0.5) !important;
}

.icon-dual-2 {
  color: #8c97b7;
  fill: rgba(140, 151, 183, 0.25);
}

.icon-dual-primary {
  color: #1761fd;
  fill: rgba(23, 97, 253, 0.25);
}

.icon-dual-secondary {
  color: #9ba7ca;
  fill: rgba(155, 167, 202, 0.25);
}

.icon-dual-success {
  color: #03d87f;
  fill: rgba(3, 216, 127, 0.25);
}

.icon-dual-warning {
  color: #ffb822;
  fill: rgba(255, 184, 34, 0.25);
}

.icon-dual-info {
  color: #12a4ed;
  fill: rgba(18, 164, 237, 0.25);
}

.icon-dual-danger {
  color: #f5325c;
  fill: rgba(245, 50, 92, 0.25);
}

.icon-dual-pink {
  color: #fd3c97;
  fill: rgba(253, 60, 151, 0.25);
}

.icon-dual-purple {
  color: #6d81f5;
  fill: rgba(109, 129, 245, 0.25);
}

.icon-dual-dark {
  color: #eaf0f9;
  fill: rgba(234, 240, 249, 0.25);
}

.analytic-dash-activity {
  height: 345px;
}

.dash-chart {
  position: relative;
  height: 350px;
}

.browser_users table td i {
  vertical-align: middle;
}

.impressions-data h2 {
  font-size: 28px;
}

.rep-map {
  height: 280px;
}

.chart-report-card {
  background-color: #364675;
}

.chart-report-card .report-main-icon,
.report-card .report-main-icon {
  font-size: 28px;
  width: 52px;
  height: 52px;
  align-items: center;
  justify-content: center;
  display: flex;
  border-radius: 50%;
}

.chart-report-card h3,
.report-card h3 {
  color: #eaf0f9;
  font-weight: 600;
  font-size: 20px;
}

.url-list li i {
  font-size: 24px;
  vertical-align: middle;
}

.traffic-card {
  margin: 20px 0;
}

.traffic-card h3 {
  font-size: 28px;
  color: #d1d1e2;
  margin: 20px 0;
}

@media print {
  .logo, .page-title, .breadcrumb, .footer {
    display: none;
    margin: 0;
    padding: 0;
  }
  .page-title-box,
  .left {
    display: none;
  }
  .content, .page-content-wrapper, .page-wrapper {
    margin-top: 0;
    padding-top: 0;
  }
  .content-page {
    margin-left: 0;
    margin-top: 0;
  }
  .topbar, .footer, .left-sidenav, .report-btn, .leftbar-tab-menu {
    display: none;
    margin: 0;
    padding: 0;
  }
  .content-page > .content {
    margin: 0;
  }
  .page-wrapper .page-content {
    width: 100%;
    margin-top: 0;
  }
}

.earning-data .money-bag {
  opacity: 0.45;
}

.earning-data .earn-money {
  font-size: 32px;
  font-weight: 600;
}

.dashboard-map {
  height: 270px;
}

.renenue-chart {
  position: relative;
  height: 350px;
}

.card-eco .card-eco-title {
  border: 2px solid #ffffff;
  border-radius: 8px;
  padding: 2px 8px;
  position: relative;
  top: -30px;
}

.card-eco .card-eco-title.eco-title-success {
  background-color: #03d87f;
  color: #ffffff;
  box-shadow: 0px 0px 0px 1px #03d87f;
}

.card-eco .card-eco-title.eco-title-danger {
  background-color: #f5325c;
  color: #ffffff;
  box-shadow: 0px 0px 0px 1px #f5325c;
}

.card-eco .card-eco-title.eco-title-secondary {
  background-color: #9ba7ca;
  color: #ffffff;
  box-shadow: 0px 0px 0px 1px #9ba7ca;
}

.card-eco .card-eco-title.eco-title-purple {
  background-color: #6d81f5;
  color: #ffffff;
  box-shadow: 0px 0px 0px 1px #6d81f5;
}

.card-eco .card-eco-title.eco-title-warning {
  background-color: #ffb822;
  color: #ffffff;
  box-shadow: 0px 0px 0px 1px #ffb822;
}

.card-eco .bg-pattern {
  position: absolute;
  background-image: url("../images/1.png");
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  opacity: 0.3;
  z-index: 0;
  width: 140px;
  height: 100%;
  top: 0;
  right: 0;
}

.card-eco .card-eco-icon {
  font-size: 40px;
  color: #8997bd;
}

.card-eco .card-eco-icon.bg-icon-purple {
  background-color: #6d81f5;
  box-shadow: 0px 5px 5px 0.25px rgba(109, 129, 245, 0.5);
}

.card-eco .card-eco-icon.bg-icon-pink {
  background-color: #fd3c97;
  box-shadow: 0px 5px 5px 0.25px rgba(253, 60, 151, 0.5);
}

.card-eco .card-eco-icon.bg-icon-secondary {
  background-color: #9ba7ca;
  box-shadow: 0px 5px 5px 0.25px rgba(155, 167, 202, 0.5);
}

.card-eco .card-eco-icon.bg-icon-warning {
  background-color: #ffb822;
  box-shadow: 0px 5px 5px 0.25px rgba(255, 184, 34, 0.5);
}

.card-eco h3 {
  font-size: 28px;
}

.re-customers-detail h3 {
  color: #d1d1e2;
  font-weight: 600;
  font-size: 22px;
}

.order-list table td img {
  width: 40px;
}

.e-co-product {
  background-color: #364675;
}

.e-co-product a {
  text-align: center !important;
  padding-top: 30px;
}

.e-co-product a img {
  height: 230px;
}

.e-co-product .ribbon-box {
  position: relative;
  margin-bottom: 0;
  padding-top: 30px;
}

.e-co-product .ribbon-box .ribbon {
  position: absolute;
  color: #ffffff;
}

.e-co-product .ribbon-box .ribbon-label {
  clear: none;
  padding: 0 5px;
  height: 66px;
  width: 66px;
  line-height: 66px;
  text-align: center;
  left: 0;
  top: 0;
  border-radius: 52% 48% 23% 77% / 44% 68% 32% 56%;
  font-weight: 500;
}

.e-co-product .product-info {
  background-color: #ffffff;
}

.e-co-product .product-info .product-title {
  font-size: 15px;
  color: #8c97b7;
  font-weight: 500;
}

.e-co-product .product-info .product-price {
  color: #d1d1e2;
  font-weight: 600;
  padding: 5px 0;
  margin-bottom: 0;
  font-size: 24px;
}

.e-co-product .product-info .product-price span {
  color: #8997bd;
  font-weight: 400;
  font-size: 13px;
}

.e-co-product .product-info .product-review li {
  margin: 0;
}

.e-co-product .product-info .product-review li i {
  font-size: 20px;
}

.e-co-product .product-info .wishlist,
.e-co-product .product-info .quickview {
  border-radius: 50px;
  padding: 4px 10px;
  opacity: 0;
  transition: all .3s ease-out;
}

.e-co-product:hover {
  box-shadow: 0px 0px 13px 0px rgba(51, 57, 80, 0.1);
}

.e-co-product:hover .wishlist,
.e-co-product:hover .quickview {
  opacity: 1;
}

.offer-box {
  background-image: url("../images/products/offer.jpg");
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}

.offer-box .offer-content {
  padding: 30px;
}

.offer-box .offer-content h3 {
  font-weight: 600;
  font-size: 30px;
  color: #d1d1e2;
}

.ribbon {
  position: absolute;
  top: 0;
  padding: 6px;
  left: -5px;
  color: #ffffff;
}

.ribbon:before {
  position: absolute;
  top: 31px;
  left: 0;
  content: "";
  border: 3px solid transparent;
}

.ribbon.ribbon-pink {
  background-color: #fd3c97;
}

.ribbon.ribbon-pink:before {
  border-top-color: #fd3c97;
  border-right-color: #fd3c97;
}

.ribbon.ribbon-secondary {
  background-color: #9ba7ca;
}

.ribbon.ribbon-secondary:before {
  border-top-color: #9ba7ca;
  border-right-color: #9ba7ca;
}

hr.thick {
  border: 0;
  height: 3px;
  max-width: 150px;
  background-image: linear-gradient(to right, #1761fd, #6d81f5);
  border-radius: 30px;
}

.single-pro-detail .custom-border {
  width: 60px;
  height: 2px;
  background-color: #eaf0f9;
}

.single-pro-detail .pro-title {
  color: #d1d1e2;
  font-size: 24px;
}

.single-pro-detail .product-review li {
  margin: 0;
}

.single-pro-detail .product-review li i {
  font-size: 16px;
}

.single-pro-detail .pro-price {
  color: #d1d1e2;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 0;
}

.single-pro-detail .pro-price span {
  font-size: 14px;
  color: #8997bd;
}

.single-pro-detail .quantity input {
  width: 14%;
  display: inline-flex;
}

.single-pro-detail .pro-features li {
  line-height: 26px;
  color: #a4abc5;
}

.single-pro-detail .pro-features li::before {
  content: "\f00c" !important;
  font-family: 'Font Awesome 5 Free' !important;
  font-weight: 600;
  font-size: 12px;
  color: #03d87f !important;
  display: inline-block;
  margin-right: 8px;
}

.single-pro-detail .form-check-inline {
  margin-right: 5px;
}

.pro-order-box {
  padding: 20px;
  border: 1px solid #333950;
  border-radius: 3px;
  background-color: #34394f;
  box-shadow: 0px 0px 13px 0px rgba(51, 57, 80, 0.05);
}

.pro-order-box i {
  font-size: 42px;
}

.review-box {
  background-color: #2a2e40;
  padding: 20px;
  justify-content: center;
  align-items: center;
}

.review-box h1 {
  font-size: 48px;
  color: #f5325c;
}

.review-box .product-review li {
  margin-right: 0;
}

.review-box .product-review li i {
  font-size: 22px;
}

.reviewer {
  padding-bottom: 12px;
  margin-top: 8px;
  border-bottom: 2px dotted #333950;
}

.reviewer .product-review li {
  margin-right: 0;
}

.total-payment .table tbody td,
.total-payment table tbody td,
.shopping-cart .table tbody td,
.shopping-cart table tbody td {
  padding: 10px;
  border-top: 0;
  border-bottom: 1px solid #333950;
}

.total-payment .table tbody td .product-name,
.total-payment table tbody td .product-name,
.shopping-cart .table tbody td .product-name,
.shopping-cart table tbody td .product-name {
  font-size: 13px;
  color: #d1d1e2;
  font-weight: 500;
}

.total-payment .table tbody tr:last-child td,
.total-payment table tbody tr:last-child td,
.shopping-cart .table tbody tr:last-child td,
.shopping-cart table tbody tr:last-child td {
  border-bottom: 0;
}

.total-payment {
  border: 1px solid #333950;
  background-color: #303549;
  border-radius: 3px;
}

.total-payment .payment-title {
  color: #d1d1e2;
  font-weight: 500;
}

.cart-promo {
  padding: 90px;
  background-image: url(../images/products/bg-cart.png);
  background-position: center center;
  background-size: cover;
}

.jp-card .jp-card-front,
.jp-card .jp-card-back {
  background: #eaf0f9 !important;
}

.balence-nav .nav {
  background-color: #2c3144;
}

.balence-nav .nav .nav-link {
  padding: 8px;
  color: #d1d1e2;
  margin-bottom: 10px;
  font-weight: 500;
  margin-right: 5px;
  width: 70px;
  height: 50px;
}

.balence-nav .nav .nav-link.active {
  color: #1761fd;
  background: #34394f;
  box-shadow: 0px 0px 13px 0px rgba(137, 151, 189, 0.25);
}

.billing-nav .nav.nav-pills {
  background-color: #2c3144;
}

.billing-nav .nav .nav-link {
  padding: 8px;
  color: #d1d1e2;
  margin-bottom: 10px;
  font-weight: 500;
  margin-right: 5px;
  width: 105px;
  height: 60px;
}

.billing-nav .nav .nav-link.active {
  color: #1761fd;
  background: #34394f;
  box-shadow: 0px 0px 13px 0px rgba(137, 151, 189, 0.25);
}

.lightpick {
  box-shadow: none;
  font-family: "Poppins", sans-serif;
  background-color: #2c3144;
  border: 1px solid #2d3552;
  z-index: 99;
}

.lightpick.lightpick--inlined {
  display: block;
}

.lightpick .lightpick__month {
  width: 100%;
  background-color: #2c3144;
}

.lightpick .lightpick__day {
  color: #8997bd;
}

.lightpick .lightpick__previous-action,
.lightpick .lightpick__next-action,
.lightpick .lightpick__close-action {
  background-color: #333950;
  padding-bottom: 4px;
}

.lightpick .lightpick__month-title-bar {
  margin-bottom: 22px;
}

.lightpick .lightpick__month-title-bar .lightpick__month-title > .lightpick__select-months {
  color: #d1d1e2;
  font-weight: 500;
}

.lightpick .lightpick__day-of-the-week {
  color: #d1d1e2;
  font-weight: 500;
  font-size: 13px;
}

.lightpick .lightpick__day.is-start-date,
.lightpick .lightpick__day.is-end-date,
.lightpick .lightpick__day.is-start-date:hover,
.lightpick .lightpick__day.is-end-date:hover,
.lightpick .lightpick__day:not(.is-disabled):hover,
.lightpick .lightpick__day.is-today {
  color: #1761fd;
  background-color: #333950;
  background-image: none;
  font-weight: 500;
  display: flex;
  justify-content: center;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50%;
  margin: 0 auto;
}

.lightpick .lightpick__day.is-today {
  border: 3px solid #2e3246;
  box-shadow: 0px 0px 0px 1.25px #333950;
  font-weight: 600;
}

.lightpick .lightpick__month-title > .lightpick__select {
  color: #d1d1e2;
}

.lightpick .lightpick__toolbar .lightpick__previous-action,
.lightpick .lightpick__toolbar .lightpick__next-action {
  color: #d1d1e2;
}

.transaction-history li {
  border-bottom: 1px solid #333950;
  padding: 12px 0;
}

.transaction-history li:last-of-type {
  border: none;
  padding-bottom: 0;
}

.transaction-history .transaction-icon {
  align-self: center;
  margin-right: 12px;
}

.transaction-history .transaction-icon i {
  font-size: 20px;
  width: 36px;
  height: 36px;
  display: inline-block;
  line-height: 36px;
  text-align: center;
  background-color: rgba(23, 97, 253, 0.15);
  color: #1761fd;
  border-radius: 50%;
}

.transaction-history .transaction-data h3 {
  font-size: 14px;
  color: #d1d1e2;
}

.activity-scroll {
  min-height: 620px;
}

.activity-height {
  height: 400px !important;
}

.activity .activity-info {
  display: flex;
  margin: 5px 0;
  position: relative;
  min-height: 60px;
}

.activity .activity-info::before {
  content: '';
  position: absolute;
  bottom: 0;
  top: 42px;
  left: 18px;
  border-left: 2px dotted #33394e;
}

.activity .activity-info .icon-info-activity i {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #a4abc5;
  box-shadow: 0px 0px 1px 0.25px;
  font-size: 18px;
  border: 2px solid #3e445f;
  margin-left: 2px;
}

.activity .activity-info .activity-info-text {
  margin-left: 16px;
  width: 100%;
}

.activity .activity-info .activity-info-text a,
.activity .activity-info .activity-info-text span {
  color: #d1d1e2;
}

/* ==============
  Timeline
===================*/
.main-timeline {
  overflow: hidden;
  position: relative;
}

.main-timeline:before {
  content: "";
  width: 3px;
  height: 100%;
  background: #333950;
  position: absolute;
  top: 0;
  left: 50%;
}

.main-timeline .timeline {
  padding-right: 30px;
  position: relative;
}

.main-timeline .timeline:before,
.main-timeline .timeline:after {
  content: "";
  display: block;
  width: 100%;
  clear: both;
}

.main-timeline .timeline:first-child:before,
.main-timeline .timeline:last-child:before {
  content: "";
  width: 13px;
  height: 13px;
  border-radius: 50%;
  border: 2px solid #333950;
  background: #2c3144;
  margin: 0 auto;
  position: absolute;
  top: 0;
  left: 0;
  right: -3px;
}

.main-timeline .timeline:last-child:before {
  top: auto;
  bottom: 0;
}

.main-timeline .timeline-icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #2c3144;
  border: 2px solid #333950;
  box-sizing: content-box;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: -4px;
}

.main-timeline .timeline-icon:before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #12a4ed;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.main-timeline .year {
  display: inline-block;
  padding: 8px 20px;
  margin: 0;
  font-size: 14px;
  color: #ffffff;
  background: #12a4ed;
  text-align: center;
  position: absolute;
  top: 50%;
  right: 35%;
  transform: translateY(-50%);
}

.main-timeline .year:before {
  content: "";
  border-right: 18px solid #12a4ed;
  border-top: 18px solid transparent;
  border-bottom: 18px solid transparent;
  position: absolute;
  top: 0;
  left: -18px;
}

.main-timeline .timeline-content {
  width: 46.5%;
  padding: 20px;
  margin: 0 20px 0 0;
  background: #333950;
  position: relative;
}

.main-timeline .timeline-content:after {
  content: "";
  border-left: 20px solid #333950;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  position: absolute;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
}

.main-timeline .title {
  float: left;
  font-size: 14px;
  font-weight: 500;
  color: #dfe4ea;
  margin: 0 20px 20px 0;
}

.main-timeline .post {
  display: inline-block;
  font-size: 13px;
  color: #d1d1e2;
}

.main-timeline .description {
  font-size: 14px;
  color: #d1d1e2;
  line-height: 24px;
  margin: 0;
  clear: both;
}

.main-timeline .timeline:nth-child(2n) {
  padding: 0 0 0 30px;
}

.main-timeline .timeline:nth-child(2n) .year {
  right: auto;
  left: 35%;
}

.main-timeline .timeline:nth-child(2n) .year:before {
  border: 18px solid transparent;
  border-right: none;
  border-left: 18px solid #12a4ed;
  left: auto;
  right: -18px;
}

.main-timeline .timeline:nth-child(2n) .timeline-content {
  float: right;
  margin: 0 0 0 20px;
}

.main-timeline .timeline:nth-child(2n) .timeline-content:after {
  border-left: none;
  border-right: 20px solid #333950;
  right: auto;
  left: -20px;
}

@media only screen and (max-width: 1200px) {
  .main-timeline .year {
    right: 30%;
  }
  .main-timeline .timeline:nth-child(2n) .year {
    left: 30%;
  }
}

@media only screen and (max-width: 990px) {
  .main-timeline .year {
    right: 25%;
  }
  .main-timeline .timeline:nth-child(2n) .year {
    left: 25%;
  }
}

@media only screen and (max-width: 767px) {
  .main-timeline:before {
    left: 10px;
  }
  .main-timeline .timeline {
    padding: 0 0 0 30px;
    margin-bottom: 20px;
  }
  .main-timeline .timeline:last-child {
    margin-bottom: 0;
  }
  .main-timeline .timeline:first-child:before,
  .main-timeline .timeline:last-child:before {
    display: none;
  }
  .main-timeline .timeline-icon {
    margin: 0;
    position: absolute;
    top: 7px;
    left: 0;
  }
  .main-timeline .year,
  .main-timeline .timeline:nth-child(2n) .year {
    display: block;
    font-weight: bold;
    margin: 0 0 32px 30px;
    z-index: 1;
    position: relative;
    top: auto;
    left: auto;
    right: auto;
    transform: none;
  }
  .main-timeline .timeline:nth-child(2n) .year:before {
    border-left: none;
    border-right: 18px solid #12a4ed;
    right: auto;
    left: -18px;
  }
  .main-timeline .timeline-content {
    padding: 20px;
  }
  .main-timeline .timeline-content,
  .main-timeline .timeline:nth-child(2n) .timeline-content {
    width: auto;
    float: none;
    margin: 0 0 0 30px;
  }
  .main-timeline .timeline-content:after,
  .main-timeline .timeline:nth-child(2n) .timeline-content:after {
    border: none;
    border-bottom: 20px solid #333950;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    top: -20px;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
  }
}

@media only screen and (max-width: 480px) {
  .main-timeline .title {
    float: none;
    margin: 0;
  }
  .main-timeline .year,
  .main-timeline .timeline:nth-child(2n) .year {
    margin-left: 20px;
  }
  .main-timeline .timeline-content,
  .main-timeline .timeline:nth-child(2n) .timeline-content {
    margin-left: 10px;
  }
  .main-timeline .post {
    margin: 5px 0;
  }
}

.email-leftbar {
  width: 220px;
  float: left;
}

.email-leftbar .mail-list a {
  display: block;
  color: #d1d1e2;
  font-size: 13px;
  line-height: 24px;
  padding: 5px;
}

.email-leftbar .mail-list a i {
  color: #a4abc5;
}

.email-leftbar .mail-list a:hover,
.email-leftbar .mail-list a.active {
  color: #1761fd;
}

.email-leftbar .mail-list a:hover i,
.email-leftbar .mail-list a.active i {
  color: #1761fd;
}

.email-leftbar .chat-user-box p.user-title {
  font-size: 13px;
  color: #eaf0f9;
  font-weight: 500;
}

.email-leftbar .chat-user-box p {
  font-size: 12px;
}

.email-rightbar {
  margin-left: 240px;
}

.message-list {
  display: block;
  padding-left: 0;
}

.message-list li {
  position: relative;
  display: block;
  height: 50px;
  line-height: 50px;
  transition-duration: .3s;
}

.message-list li a {
  color: #8c97b7;
}

.message-list li:hover {
  background: #333950;
  transition-duration: .05s;
}

.message-list li .col-mail {
  float: left;
  position: relative;
}

.message-list li .col-mail-1 {
  width: 320px;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 12px;
}

.message-list li .col-mail-1 .star-toggle,
.message-list li .col-mail-1 .checkbox-wrapper-mail,
.message-list li .col-mail-1 .dot {
  display: block;
  float: left;
}

.message-list li .col-mail-1 .dot {
  border: 4px solid transparent;
  border-radius: 100px;
  margin: 22px 26px 0;
  height: 0;
  width: 0;
  line-height: 0;
  font-size: 0;
}

.message-list li .col-mail-1 .star-toggle {
  font-size: 15px;
  margin-left: 12px;
}

.message-list li .col-mail-1 .title {
  position: absolute;
  top: 0;
  left: 110px;
  right: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

.message-list li .col-mail-2 {
  position: absolute;
  top: 0;
  left: 320px;
  right: 0;
  bottom: 0;
}

.message-list li .col-mail-2 .subject,
.message-list li .col-mail-2 .date {
  position: absolute;
  top: 0;
}

.message-list li .col-mail-2 .subject {
  left: 0;
  right: 200px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.message-list li .col-mail-2 .date {
  right: 0;
  width: 170px;
  padding-left: 80px;
}

.message-list li.active,
.message-list li.active:hover {
  box-shadow: inset 3px 0 0 #1761fd;
}

.message-list li.unread {
  background-color: #333950;
}

.message-list li.unread a {
  color: #d6e2f3;
}

.message-list .checkbox-wrapper-mail {
  cursor: pointer;
  height: 16px;
  width: 16px;
  position: relative;
  display: inline-block;
  box-shadow: inset 0 0 0 1px #7081b9;
  border-radius: 1px;
}

.message-list .checkbox-wrapper-mail input {
  opacity: 0;
  cursor: pointer;
}

.message-list .checkbox-wrapper-mail input:checked ~ label {
  opacity: 1;
}

.message-list .checkbox-wrapper-mail label {
  position: absolute;
  height: 16px;
  width: 16px;
  left: 0;
  cursor: pointer;
  opacity: 0;
  margin-bottom: 0;
  transition-duration: .05s;
  top: 0;
}

.message-list .checkbox-wrapper-mail label:before {
  content: "\F12C";
  font-family: "Material Design Icons";
  top: 0;
  height: 16px;
  color: #d6e2f3;
  width: 16px;
  position: absolute;
  margin-top: -17px;
  left: 1px;
  font-size: 13px;
}

@media (max-width: 767px) {
  .email-leftbar {
    float: none;
    width: 100%;
  }
  .email-rightbar {
    margin: 0;
  }
}

.note-editor.note-frame {
  border: 1px solid #33394e;
}

.note-editor.note-frame .note-editing-area .note-editable {
  padding: 10px;
  overflow: auto;
  color: #d1d1e2;
  background-color: #2c3144;
}

.note-editor.note-frame .note-statusbar .note-resizebar {
  background-color: #25293a;
}

.chat-box-left {
  float: left;
  width: 340px;
  height: 740px;
  background-color: #2c3144;
  border-radius: 3px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid #2d3552;
}

.chat-box-left .nav-pills {
  background-color: #2a2e40;
}

.chat-box-left .nav-pills .nav-link {
  color: #d1d1e2;
}

.chat-box-left .nav-pills .nav-link.active {
  color: #ffffff;
  background: #1761fd;
}

.chat-box-left .chat-search {
  margin-top: 20px;
}

.chat-box-left .chat-list {
  height: 600px !important;
}

.chat-box-left .chat-list .media + .media {
  border: 1px solid #333950;
  margin-bottom: 5px;
  border-radius: 5px;
}

.chat-box-left .chat-list .media {
  padding: 15px;
  position: relative;
}

.chat-box-left .chat-list .media:hover, .chat-box-left .chat-list .media:focus {
  background-color: rgba(51, 57, 80, 0.5);
}

.chat-box-left .chat-list .media.new-message {
  border: 1px solid #303e67;
  background-color: #333950;
  margin-bottom: 5px;
  border-radius: 5px;
}

.chat-box-left .chat-list .media .media-left {
  position: relative;
  align-self: center;
}

.chat-box-left .chat-list .media .media-left .round-10 {
  border: 2px solid #2a2e40;
  border-radius: 50%;
  position: absolute;
  bottom: 0;
  right: -1px;
  display: inline-block;
  height: 12px;
  width: 12px;
}

.chat-box-left .chat-list .media .media-body {
  margin-left: 15px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.chat-box-left .chat-list .media .media-body h6 {
  font-size: 14px;
  color: #d1d1e2;
  margin-bottom: 0px;
}

.chat-box-left .chat-list .media .media-body p {
  margin-bottom: 0;
  color: #d1d1e2;
  font-size: 12px;
}

.chat-box-left .chat-list .media .media-body > div:last-child {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  min-width: 50px;
  text-align: right;
}

.chat-box-left .chat-list .media .media-body > div:last-child span:nth-child(2) {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #03d87f;
  color: #ffffff;
  border-radius: 50%;
  font-size: 10px;
  width: 18px;
  height: 18px;
  margin-top: 8px;
}

.chat-box-left .chat-list .media .media-body span {
  font-size: 12px;
  color: #d1d1e2;
  display: block;
}

.chat-box-right {
  width: auto;
  background-color: #2c3144;
  display: block;
  border-radius: 3px;
  position: relative;
  height: 740px;
  margin-left: 361px;
  margin-bottom: 20px;
  border: 1px solid #2d3552;
}

.chat-box-right .chat-header {
  border-bottom: 1px solid #333950;
  padding: 16px;
  background-color: #2c3144;
}

.chat-box-right .chat-header .media .media-body {
  margin-left: 15px;
  align-self: center;
}

.chat-box-right .chat-header .media .media-body h6 {
  font-size: 14px;
  color: #d1d1e2;
  margin-bottom: 5px;
}

.chat-box-right .chat-header .media .media-body p {
  margin-bottom: 0;
  color: #8997bd;
  font-size: 12px;
}

.chat-box-right .chat-header .chat-features {
  position: relative;
  top: -34px;
  float: right;
}

.chat-box-right .chat-header .chat-features a {
  color: #7081b9;
  font-size: 16px;
  margin-left: 12px;
}

.chat-box-right .chat-header .chat-features a:hover {
  color: #1761fd;
}

.chat-box-right .chat-body {
  padding: 16px;
  height: 580px;
}

.chat-box-right .chat-body .chat-detail {
  max-height: 610px;
}

.chat-box-right .chat-body .chat-detail .reverse {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.chat-box-right .chat-body .chat-detail .media .media-img {
  z-index: 5;
}

.chat-box-right .chat-body .chat-detail .media .media-body {
  margin-left: 14px;
}

.chat-box-right .chat-body .chat-detail .media .media-body .chat-msg {
  max-width: 80%;
  margin-bottom: 5px;
  margin-left: -56px;
}

.chat-box-right .chat-body .chat-detail .media .media-body .chat-msg:first-child p {
  padding-left: 54px;
}

.chat-box-right .chat-body .chat-detail .media .media-body .chat-msg p {
  padding: 10px 16px;
  background-color: rgba(23, 97, 253, 0.01);
  border: 1px solid #33394e;
  color: #d1d1e2;
  display: inline-block;
  margin-bottom: 0;
  border-radius: 50px;
}

.chat-box-right .chat-body .chat-detail .media .media-body.reverse {
  margin-right: 0;
  margin-left: 14px;
}

.chat-box-right .chat-body .chat-detail .media .media-body.reverse .chat-msg {
  max-width: 80%;
  margin-bottom: 5px;
  margin-right: -42px;
}

.chat-box-right .chat-body .chat-detail .media .media-body.reverse .chat-msg:first-child p {
  padding-right: 54px;
}

.chat-box-right .chat-body .chat-detail .media .media-body.reverse .chat-msg p {
  padding: 10px 16px;
  background-color: rgba(23, 97, 253, 0.01);
  border: 1px solid #33394e;
  color: #d1d1e2;
  display: inline-block;
  margin-bottom: 0;
  border-radius: 50px;
}

.chat-box-right .chat-footer {
  border-top: 1px solid #2d3552;
  background-color: #2c3144;
  padding: 16px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
}

.chat-box-right .chat-footer .media .media-body {
  margin-left: 10px;
  align-self: center;
}

.chat-box-right .chat-footer .media .media-body h6 {
  font-size: 14px;
  color: #d1d1e2;
  margin-bottom: 5px;
}

.chat-box-right .chat-footer .media .media-body p {
  margin-bottom: 0;
  color: #d1d1e2;
  font-size: 12px;
}

.chat-box-right .chat-footer .chat-features {
  position: relative;
  top: 8px;
  float: right;
}

.chat-box-right .chat-footer .chat-features a {
  color: #7081b9;
  font-size: 16px;
  margin-left: 12px;
}

.chat-box-right .chat-footer .chat-features a:hover {
  color: #1761fd;
}

.chat-box-right .chat-footer input.form-control {
  border: none;
}

.chat-box-right .chat-footer .chat-admin {
  position: absolute;
  top: -22px;
  border: 2px solid #2d3552;
  border-radius: 50%;
}

@media (min-width: 1024px) and (max-width: 1680px) {
  .chat-box-left {
    width: 310px;
    float: left;
  }
  .chat-box-right {
    width: auto;
    margin-left: 330px;
  }
}

@media (min-width: 767px) and (max-width: 1023.98px) {
  .chat-box-left {
    width: 100%;
    float: none;
  }
  .chat-box-right {
    width: 100%;
    margin-left: 0;
  }
}

@media (max-width: 767px) {
  .chat-box-left {
    float: none;
    width: 100%;
  }
  .chat-box-right {
    margin: 0;
    width: 100%;
  }
}

@media (max-width: 660px) {
  .chat-box-left {
    float: none;
    width: 100%;
  }
  .chat-box-right {
    margin: 0;
    width: 100%;
  }
}

@media (max-width: 568px) {
  .chat-box-left {
    float: none;
    width: 100%;
  }
  .chat-box-right {
    margin: 0;
    width: 100%;
  }
}

.pro-map {
  clip-path: ellipse(100% 77% at 50% 23%);
}

.shape {
  position: absolute;
  pointer-events: none;
  right: 0;
  bottom: 0;
  left: 0;
}

.dastone-profile .dastone-profile-main {
  display: flex;
  flex-direction: row;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
}

.dastone-profile .dastone-profile-main .dastone-profile-main-pic {
  position: relative;
  max-width: 128px;
  max-height: 128px;
  margin-right: 18px;
}

.dastone-profile .dastone-profile-main .dastone-profile-main-pic .dastone-profile_main-pic-change {
  cursor: pointer;
  background-color: #1761fd;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  position: absolute;
  bottom: 4px;
  right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  border: 2px solid #2c3144;
  box-shadow: 0px 0px 20px 0px rgba(42, 46, 64, 0.05);
}

.dastone-profile .dastone-profile-main .dastone-profile-main-pic .dastone-profile_main-pic-change i {
  transition: all 0.3s;
  color: #ffffff;
  font-size: 12px;
}

.dastone-profile .dastone-profile-main .dastone-profile_user-detail .dastone-user-name {
  font-size: 22px;
  font-weight: 700;
  color: #d1d1e2;
  margin-bottom: 4px;
}

.dastone-profile .dastone-profile-main .dastone-profile_user-detail .dastone-user-name-post {
  color: #a4abc5;
  font-size: 12px;
}

.dastone-profile .personal-detail li {
  color: #d1d1e2;
}

.education-activity {
  min-height: 400px;
}

.dastone-basic-detail h3 {
  font-size: 34px;
  font-weight: 600;
  margin-bottom: 18px;
}

.dastone-basic-detail p {
  line-height: 22px;
}

.own-detail {
  padding: 20px;
  width: 145px;
  height: 145px;
  text-align: center;
  border-radius: 52% 48% 23% 77% / 44% 68% 32% 56%;
  box-shadow: 0px 0px 3px 1.25px #303e67;
}

.own-detail h1 {
  font-weight: 600;
  color: #ffffff;
  margin-top: 0;
}

.own-detail h5 {
  color: #f2f2f2;
}

.own-detail.own-detail-project {
  position: absolute;
  top: 110px;
  left: -60px;
}

.own-detail.own-detail-happy {
  position: absolute;
  top: 110px;
  left: 60px;
}

@media (max-width: 767px) {
  .own-detail {
    display: none;
  }
  #settings_detail .dropify-wrapper {
    width: 100% !important;
  }
}

#settings_detail .dropify-wrapper {
  width: 16%;
  height: 164px;
  margin-bottom: 30px;
}

.profile-nav .nav .nav-link {
  padding: 10px;
  color: #d1d1e2;
  margin-bottom: 10px;
  font-weight: 500;
}

.profile-nav .nav .nav-link.active {
  background-color: rgba(23, 97, 253, 0.05);
  color: #1761fd;
}

.profile-card .profile-socials a i {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
}

.profile-card .pro-title {
  font-size: 18px;
  font-weight: 600;
  color: #d1d1e2;
  margin-bottom: 5px;
}

.profile-card .socials-data h3 {
  color: #d1d1e2;
  font-family: "Roboto", sans-serif;
  font-size: 18px;
}

.profile-card .action-btn {
  position: absolute;
  top: 18px;
  right: 18px;
}

/* section skills */
.skill-detail {
  color: #a4abc5;
  font-family: "Poppins", sans-serif;
  font-size: 14px;
}

.skills .skill-box {
  margin-bottom: 30px;
}

.skills .skill-box:last-of-type {
  margin-bottom: 0;
}

.skills .skill-box .skill-title {
  margin-bottom: 10px;
  color: #d1d1e2;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 600;
}

.skills .skill-box .progress-line {
  background: #2a2e40;
  height: 7px;
  margin: 0;
  position: relative;
  border: 2px solid #2c3144;
  width: 100%;
  border-radius: 10px;
  box-shadow: 0px 0px 1px 0.25px #1761fd;
}

.skills .skill-box .progress-line > span {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: #1761fd;
}

.skills .skill-box .progress-line > span > .percent-tooltip {
  position: absolute;
  right: -15px;
  top: -30px;
  margin-bottom: 10px;
  color: #d1d1e2;
  text-transform: uppercase;
  font-size: 13px;
}

.profile-activity-height {
  height: 440px !important;
}

.lightpick__days-of-the-week {
  margin-bottom: 16px;
}

.invoice-head {
  border-bottom: 4px double #7081b9;
}

.invoice-head .logo-lg.logo-light {
  display: none;
}

.invoice-head .contact-detail li {
  border-left: 2px solid #7081b9;
}

.invoice-head .contact-detail li i {
  font-size: 24px;
  color: #12a4ed;
  margin-bottom: 5px;
}

.project-overview-activity {
  height: 335px;
}

.project-budget-chart {
  height: 273px;
}

.weather-btn-icon i {
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 50%;
  background-color: #1761fd;
  color: #ffffff;
  display: inline-block;
  margin: 0 -4px 0 14px;
}

.icon-info i {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 26px;
  z-index: 5;
}

.dash-info-carousel .carousel-item h2 {
  font-weight: 500;
  color: #d1d1e2;
  font-size: 16px;
}

.dash-info-carousel .carousel-control-prev,
.dash-info-carousel .carousel-control-next {
  width: 30px;
  height: 30px;
  background-color: #2a2e40;
  border-radius: 50%;
  opacity: 1;
}

.dash-info-carousel .carousel-control-prev:hover,
.dash-info-carousel .carousel-control-next:hover {
  opacity: .9;
}

.dash-info-carousel .carousel-control-prev {
  left: auto;
  right: 38px;
  top: 0;
}

.dash-info-carousel .carousel-control-prev .carousel-control-prev-icon {
  background-image: none;
}

.dash-info-carousel .carousel-control-prev:after {
  content: '\55';
  font-family: "dripicons-v2" !important;
  font-size: .875rem;
  color: #d1d1e2;
  line-height: 1.875rem;
  margin-right: 0.48rem;
}

.dash-info-carousel .carousel-control-next {
  left: auto;
  right: 0;
  top: 0;
}

.dash-info-carousel .carousel-control-next .carousel-control-next-icon {
  background-image: none;
}

.dash-info-carousel .carousel-control-next:after {
  content: '\56';
  font-family: "dripicons-v2" !important;
  font-size: .875rem;
  color: #d1d1e2;
  line-height: 1.875rem;
  margin-right: 0.48rem;
}

.c-progress-steps {
  margin: 0;
  list-style-type: none;
  font-family: "Roboto", sans-serif;
}

.c-progress-steps li {
  position: relative;
  font-size: 13px;
  color: #7081b9;
  padding: 16px 0 0 36px;
}

.c-progress-steps li a {
  color: inherit;
}

.c-progress-steps li.done {
  color: #12a4ed;
}

.c-progress-steps li.done span {
  background-color: rgba(18, 164, 237, 0.09);
}

.c-progress-steps li.done:before {
  color: #12a4ed;
  content: "\f058";
  width: 30px;
  height: 30px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  border: 2px solid #12a4ed;
}

.c-progress-steps li.current {
  color: #1761fd;
  font-weight: 500;
}

.c-progress-steps li.current:before {
  color: #1761fd;
  content: "\f192";
  width: 30px;
  height: 30px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  border: 2px solid #1761fd;
}

.c-progress-steps li:before {
  position: absolute;
  left: 0;
  font-family: 'Font Awesome 5 Free' !important;
  font-weight: 600;
  font-size: 18px;
  background-color: #ffffff;
  content: "\f111";
  width: 30px;
  height: 30px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  border: 2px solid #303e67;
}

@media all and (max-width: 600px) {
  .c-progress-steps li:before {
    top: calc(50% - 8px);
    font-size: 16px;
  }
}

@media all and (min-width: 600px) {
  .c-progress-steps {
    display: table;
    list-style-type: none;
    margin: 20px auto;
    padding: 0;
    table-layout: fixed;
    width: 100%;
  }
  .c-progress-steps li {
    display: table-cell;
    text-align: center;
    padding: 0;
    padding-bottom: 34px;
    white-space: nowrap;
    position: relative;
    border-left-width: 0;
    border-bottom-width: 4px;
    border-bottom-style: solid;
    border-bottom-color: #303e67;
  }
  .c-progress-steps li.done {
    border-bottom-color: #12a4ed;
  }
  .c-progress-steps li.current {
    color: #ffffff;
    border-bottom-color: #1761fd;
  }
  .c-progress-steps li.current span {
    background: linear-gradient(14deg, #1761fd 0%, rgba(23, 97, 253, 0.6));
    box-shadow: 0 7px 14px 0 rgba(23, 97, 253, 0.5);
  }
  .c-progress-steps li.current:before {
    color: #1761fd;
    content: "\f192";
  }
  .c-progress-steps li:before {
    bottom: -15px;
    left: 50%;
    margin-left: -16px;
  }
  .c-progress-steps li span {
    background-color: rgba(18, 164, 237, 0.1);
    border-radius: 3px;
    padding: 6px;
    box-shadow: 0px 0px 5px 0.25px #3a4b7c;
  }
}

.workload-chart {
  height: 210px;
}

/*== todo list ==*/
.todo-list {
  height: 200px !important;
}

.todo-list .todo-box i {
  position: absolute;
  right: 20px;
  z-index: 10;
  font-size: 16px;
  margin-top: 6px;
  color: #7081b9;
}

.todo-list .todo-box .todo-task {
  padding: 4px 0;
}

.todo-list .todo-box .todo-task label {
  display: inline-block;
  margin-bottom: .5rem;
}

.todo-list .todo-box .todo-task .ckbox {
  font-weight: normal;
  position: relative;
  display: block;
  line-height: 25px;
}

.todo-list .todo-box .todo-task .ckbox span {
  padding-left: 16px;
  color: #9fa8bf;
}

.todo-list .todo-box .todo-task .ckbox span:before {
  content: '';
  width: 20px;
  height: 20px;
  background-color: transparent;
  border: 2px solid #303e67;
  top: 2px;
  left: 0;
  border-radius: 5px;
  position: absolute;
}

.todo-list .todo-box .todo-task .ckbox span:after {
  top: 1px;
  left: 0;
  width: 20px;
  height: 20px;
  content: "\F12C";
  font: normal normal normal 24px/1 "Material Design Icons";
  font-size: 14px;
  text-align: center;
  color: #8c97b7;
  background-color: transparent;
  line-height: 20px;
  display: none;
  border-radius: 5px;
  position: absolute;
}

.todo-list .todo-box .todo-task .ckbox input[type='checkbox'] {
  opacity: 0;
  margin: 0 5px 0 0;
}

.todo-list .todo-box .todo-task .ckbox input[type='checkbox']:checked + span:after {
  display: block;
}

.todo-list .todo-box .todo-task input:checked + span {
  text-decoration: line-through;
}

.todo-list .form-control {
  color: #eaf0f9;
  background: #303e67;
  border: 1px solid transparent;
}

.todo-list .form-control:focus {
  border-color: transparent;
  background: #303e67;
  box-shadow: none;
}

.client-card .client-name {
  color: #d1d1e2;
}

.calendar-event .event-name h3 {
  font-size: 14px;
  color: #d1d1e2;
}

.project-invoice .table tr td h5 {
  color: #d1d1e2;
}

.team-card .user-img {
  height: 40px;
  width: 40px;
}

.team-card .online-circle {
  position: absolute;
  right: 0;
  top: 40px;
  left: 48px;
}

.team-card .online-circle i {
  border: 3px solid #2c3144;
  border-radius: 50%;
  font-size: 8px;
}

.team-card .team-leader {
  font-size: 15px;
  color: #d1d1e2;
}

.project-card {
  font-family: "Poppins", sans-serif;
}

.project-card .project-title {
  font-size: 20px;
  color: #d1d1e2;
  font-weight: 600;
}

.task-box .task-priority-icon i {
  border: 5px solid #25293a;
  border-radius: 50%;
  position: absolute;
  top: -5px;
  left: -5px;
}

.drop-shadow-bar {
  filter: drop-shadow(6px 6px 8px rgba(112, 129, 185, 0.65));
}

ul.steppedprogress {
  counter-reset: step;
  display: flex;
  margin: auto;
  overflow: hidden;
  padding: 0;
  position: relative;
  text-align: center;
  z-index: 1;
}

ul.steppedprogress li {
  list-style-type: none;
  color: #d1d1e2;
  line-height: normal;
  text-transform: uppercase;
  flex: 1;
  font-size: 12px;
  position: relative;
  padding: 0 2%;
  font-weight: 500;
  font-family: "Poppins", sans-serif;
}

ul.steppedprogress li:before {
  content: counter(step);
  counter-increment: step;
  width: 24px;
  height: 24px;
  line-height: 20px;
  display: block;
  font-size: 12px;
  color: #d1d1e2;
  background: #333950;
  border-radius: 50%;
  margin: 0 auto 10px;
  position: relative;
  z-index: 1;
  border: 2px solid #2c3144;
  box-shadow: 0px 0px 0px 1.25px #333950;
}

ul.steppedprogress li:after {
  content: '';
  width: 100%;
  border: 0.5px dashed #2d3552;
  position: absolute;
  left: -50%;
  top: 12px;
  z-index: -1;
}

ul.steppedprogress li.complete:before {
  background: #9ba7ca;
  color: #ffffff;
  border: 2px solid #2c3144;
  box-shadow: 0px 0px 0px 1.25px #9ba7ca;
}

ul.steppedprogress li.complete.continuous:before {
  background: #1761fd;
  color: #ffffff;
  border: 2px solid #2c3144;
  box-shadow: 0px 0px 0px 1.25px #1761fd;
}

ul.steppedprogress li.complete.continuous span {
  color: #1761fd;
}

ul.steppedprogress li.complete.finish:before {
  background: #f5325c;
  color: #ffffff;
  border: 2px solid #2c3144;
  box-shadow: 0px 0px 0px 1.25px #f5325c;
  content: "\F12C";
  font: normal normal normal 24px/1 "Material Design Icons";
  font-size: 16px;
  line-height: 20px;
}

ul.steppedprogress li.complete.finish span {
  color: #f5325c;
}

ul.steppedprogress li.complete:after {
  border-color: #262c45;
}

ul.steppedprogress li:first-child:after {
  content: none;
}

@media (max-width: 480px) {
  ul.steppedprogress {
    display: block;
  }
  ul.steppedprogress li {
    flex: none;
    clear: both;
    text-align: left;
    padding: 0;
    margin-left: 2px;
    min-height: 2.2em;
  }
  ul.steppedprogress li span {
    white-space: nowrap;
  }
  ul.steppedprogress li:before {
    float: none;
    display: inline-block;
    margin-right: 10px;
    text-align: center;
    margin-left: 0;
  }
  ul.steppedprogress li:after {
    content: '';
    width: 6px;
    height: 100%;
    position: absolute;
    left: 12px;
    top: -50%;
    z-index: -1;
  }
}

.kanban-board {
  flex: 1;
  white-space: nowrap;
  display: flex;
  overflow-x: auto;
}

.kanban-board .kanban-col {
  user-select: none;
  flex: 1 0 auto;
  width: 18rem;
  max-height: 100%;
  padding-right: 12px;
  padding-bottom: 12px;
  outline: none !important;
  position: relative;
}

.kanban-board .kanban-col .kanban-main-card {
  max-height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-bottom: 12px;
  border-radius: 4px;
  padding: 16px;
  background: #333950;
}

.kanban-board .kanban-col .kanban-main-card .kanban-box-title {
  position: relative;
  display: block;
}

.kanban-board .kanban-col .kanban-main-card .kanban-box-title .kanban-main-dropdown {
  position: absolute;
  right: 0;
  top: 0;
}

@media (min-width: 1200px) {
  .kanban-board {
    overflow-x: scroll;
  }
}

.files-nav .nav.nav-pills {
  background-color: #2c3144;
}

.files-nav .nav .nav-link {
  padding: 10px 0;
  color: #d1d1e2;
  font-weight: 500;
  display: flex;
}

.files-nav .nav .nav-link .icon-dual-file {
  color: #eaf0f9;
  fill: rgba(234, 240, 249, 0.12);
  stroke-width: 1px;
}

.files-nav .nav .nav-link h5 {
  color: #d1d1e2;
  font-size: 13px;
  font-weight: 500;
}

.files-nav .nav .nav-link small {
  color: #8997bd;
  font-size: 10px;
}

.files-nav .nav .nav-link.active {
  background-color: transparent;
}

.files-nav .nav .nav-link.active h5 {
  color: #1761fd;
}

.files-nav .nav .nav-link.active .icon-dual-file {
  color: #1761fd;
  fill: rgba(23, 97, 253, 0.12);
}

.file-box-content {
  margin-bottom: -16px;
}

.file-box-content .file-box {
  border: 1px solid #2d3552;
  border-radius: 5px;
  padding: 20px;
  width: 160px;
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 16px;
  background-color: #2c3144;
}

.file-box-content .file-box .download-icon-link {
  color: #a4abc5;
}

.file-box-content .file-box .download-icon-link .file-download-icon {
  font-size: 13px;
  position: relative;
  top: -10px;
  left: 110px;
}

.file-box-content .file-box .download-icon-link:hover {
  color: #1761fd;
}

.file-box-content .file-box i {
  font-size: 36px;
}

.file-category {
  width: 100%;
}

.file-category .file-category-block {
  text-align: center;
  margin-right: 20px;
}

.file-category .file-category-block .file-category-folder {
  border-bottom: 2px solid transparent;
  padding: 10px 20px !important;
  border-radius: 18px;
}

.file-category .file-category-block .file-category-folder.active {
  border-bottom: 2px solid;
}

.file-category .file-category-block .file-category-folder i {
  display: block;
  font-size: 38px;
}

.category-icon i {
  font-size: 200px;
  color: #2a2e40;
}

input.add-file-input {
  position: absolute;
  opacity: 0;
  right: 0;
  top: 0;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
  border-color: #ffffff;
}

.radio .custom-control-label::after {
  position: absolute;
  top: 7px;
}

.checkbox label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal;
  margin-bottom: 8px;
}

.checkbox label::before {
  -o-transition: 0.3s ease-in-out;
  -webkit-transition: 0.3s ease-in-out;
  background-color: #2c3144;
  border-radius: 3px;
  border: 1px solid #a4abc5;
  content: "";
  display: inline-block;
  height: 18px;
  left: 0;
  top: 0px;
  margin-left: -18px;
  position: absolute;
  transition: 0.3s ease-in-out;
  width: 18px;
  outline: none !important;
}

.checkbox label::after {
  color: #333950;
  display: inline-block;
  font-size: 11px;
  height: 18px;
  left: 0;
  margin-left: -18px;
  padding-left: 3px;
  padding-top: 2px;
  position: absolute;
  width: 18px;
}

.checkbox input[type="checkbox"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important;
}

.checkbox input[type="checkbox"]:disabled + label {
  opacity: 0.65;
}

.checkbox input[type="checkbox"]:focus + label::before {
  outline-offset: -2px;
  outline: none;
}

.checkbox input[type="checkbox"]:checked + label::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 7px;
  display: table;
  width: 5px;
  height: 10px;
  border: 2px solid #eaf0f9;
  border-top-width: 0;
  border-left-width: 0;
  transform: rotate(45deg);
}

.checkbox input[type="checkbox"]:disabled + label::before {
  background-color: #333950;
  cursor: not-allowed;
}

.checkbox.checkbox-circle label::before {
  border-radius: 50%;
}

.checkbox.checkbox-inline {
  margin-top: 0;
}

.checkbox.checkbox-single {
  height: 18px;
  width: 18px;
}

.checkbox.checkbox-single input {
  height: 18px;
  width: 18px;
  position: absolute;
}

.checkbox.checkbox-single label {
  height: 18px;
  width: 18px;
}

.checkbox.checkbox-single label:before {
  margin-left: 0;
}

.checkbox.checkbox-single label:after {
  margin-left: 0;
}

.checkbox-primary input[type="checkbox"]:checked + label::before {
  background-color: #1761fd;
  border-color: #1761fd;
}

.checkbox-primary input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-danger input[type="checkbox"]:checked + label::before {
  background-color: #f5325c;
  border-color: #f5325c;
}

.checkbox-danger input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-info input[type="checkbox"]:checked + label::before {
  background-color: #12a4ed;
  border-color: #12a4ed;
}

.checkbox-info input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-warning input[type="checkbox"]:checked + label::before {
  background-color: #ffb822;
  border-color: #ffb822;
}

.checkbox-warning input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-success input[type="checkbox"]:checked + label::before {
  background-color: #03d87f;
  border-color: #03d87f;
}

.checkbox-success input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-purple input[type="checkbox"]:checked + label::before {
  background-color: #6d81f5;
  border-color: #6d81f5;
}

.checkbox-purple input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-pink input[type="checkbox"]:checked + label::before {
  background-color: #fd3c97;
  border-color: #fd3c97;
}

.checkbox-pink input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

.checkbox-dark input[type="checkbox"]:checked + label::before {
  background-color: #eaf0f9;
  border-color: #eaf0f9;
}

.checkbox-dark input[type="checkbox"]:checked + label::after {
  border-color: #ffffff;
}

/* Radios */
.radio label {
  display: inline-block;
  padding-left: 2px;
  position: relative;
  font-weight: normal;
  margin-bottom: 8px;
}

.radio label::before {
  -o-transition: border 0.5s ease-in-out;
  -webkit-transition: border 0.5s ease-in-out;
  background-color: #2c3144;
  border-radius: 50%;
  border: 1px solid #a4abc5;
  content: "";
  display: inline-block;
  height: 14px;
  left: 0;
  top: 2px;
  margin-left: -18px;
  position: absolute;
  transition: border 0.5s ease-in-out;
  width: 14px;
  outline: none !important;
}

.radio label::after {
  -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  -ms-transform: scale(0, 0);
  -o-transform: scale(0, 0);
  -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  -webkit-transform: scale(0, 0);
  -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
  background-color: #eaf0f9;
  border-radius: 50%;
  content: " ";
  display: inline-block;
  height: 6px;
  width: 6px;
  left: 6px;
  margin-left: -20px;
  position: absolute;
  top: 6px;
  transform: scale(0, 0);
  transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
}

.radio input[type="radio"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important;
}

.radio input[type="radio"]:disabled + label {
  opacity: 0.65;
}

.radio input[type="radio"]:focus + label::before {
  outline-offset: -2px;
  outline: 5px auto -webkit-focus-ring-color;
  outline: thin dotted;
}

.radio input[type="radio"]:checked + label::after {
  transform: scale(1, 1);
}

.radio input[type="radio"]:disabled + label::before {
  cursor: not-allowed;
}

.radio.radio-inline {
  margin-top: 0;
}

.radio.radio-single {
  height: 17px;
}

.radio.radio-single input {
  position: relative;
  top: -10px;
}

.radio.radio-single label {
  height: 17px;
  padding: 0;
}

.radio-primary input[type="radio"] + label::after {
  background-color: #1761fd;
}

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #1761fd;
}

.radio-primary input[type="radio"]:checked + label::after {
  background-color: #1761fd;
}

.radio-danger input[type="radio"] + label::after {
  background-color: #f5325c;
}

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #f5325c;
}

.radio-danger input[type="radio"]:checked + label::after {
  background-color: #f5325c;
}

.radio-info input[type="radio"] + label::after {
  background-color: #12a4ed;
}

.radio-info input[type="radio"]:checked + label::before {
  border-color: #12a4ed;
}

.radio-info input[type="radio"]:checked + label::after {
  background-color: #12a4ed;
}

.radio-warning input[type="radio"] + label::after {
  background-color: #ffb822;
}

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #ffb822;
}

.radio-warning input[type="radio"]:checked + label::after {
  background-color: #ffb822;
}

.radio-success input[type="radio"] + label::after {
  background-color: #03d87f;
}

.radio-success input[type="radio"]:checked + label::before {
  border-color: #03d87f;
}

.radio-success input[type="radio"]:checked + label::after {
  background-color: #03d87f;
}

.radio-purple input[type="radio"] + label::after {
  background-color: #6d81f5;
}

.radio-purple input[type="radio"]:checked + label::before {
  border-color: #6d81f5;
}

.radio-purple input[type="radio"]:checked + label::after {
  background-color: #6d81f5;
}

.radio-pink input[type="radio"] + label::after {
  background-color: #fd3c97;
}

.radio-pink input[type="radio"]:checked + label::before {
  border-color: #fd3c97;
}

.radio-pink input[type="radio"]:checked + label::after {
  background-color: #fd3c97;
}

.radio2 label {
  display: inline-block;
  padding-left: 8px;
  position: relative;
  font-weight: normal;
  margin-bottom: 10px;
}

.radio2 label::before {
  background-color: #ffffff;
  border-radius: 50%;
  border: 1px solid transparent;
  content: "";
  display: inline-block;
  height: 16px;
  top: -1px;
  left: -2px;
  margin-left: -18px;
  position: absolute;
  width: 16px;
  outline: none !important;
}

.radio2 label::after {
  border-radius: 50%;
  content: " ";
  display: inline-block;
  height: 8px;
  left: 4px;
  margin-left: -20px;
  position: absolute;
  top: 3px;
  transform: scale(0, 0);
  width: 8px;
}

.radio2 input[type="radio"] {
  cursor: pointer;
  opacity: 0;
  z-index: 1;
  outline: none !important;
}

.radio2 input[type="radio"]:disabled + label {
  opacity: 0.65;
}

.radio2 input[type="radio"]:focus + label::before {
  outline-offset: -2px;
  outline: 5px auto -webkit-focus-ring-color;
  outline: thin dotted;
  border-color: #1761fd;
}

.radio2 input[type="radio"]:checked + label::after {
  transform: scale(1, 1);
}

.radio2 input[type="radio"]:checked + label::before {
  border-color: #1761fd;
}

.radio2 input[type="radio"]:disabled + label::before {
  cursor: not-allowed;
}

.radio2.radio-primary2 input[type="radio"] + label::before {
  background-color: #1761fd;
}

.radio2.radio-primary2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-primary2 input[type="radio"]:checked + label::before {
  border-color: #1761fd;
}

.radio2.radio-primary2 input[type="radio"]:checked + label::after {
  border-color: #1761fd;
}

.radio2.radio-secondary2 input[type="radio"] + label::before {
  background-color: #9ba7ca;
}

.radio2.radio-secondary2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-secondary2 input[type="radio"]:checked + label::before {
  border-color: #9ba7ca;
}

.radio2.radio-secondary2 input[type="radio"]:checked + label::after {
  border-color: #9ba7ca;
}

.radio2.radio-success2 input[type="radio"] + label::before {
  background-color: #03d87f;
}

.radio2.radio-success2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-success2 input[type="radio"]:checked + label::before {
  border-color: #03d87f;
}

.radio2.radio-success2 input[type="radio"]:checked + label::after {
  border-color: #03d87f;
}

.radio2.radio-danger2 input[type="radio"] + label::before {
  background-color: #f5325c;
}

.radio2.radio-danger2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-danger2 input[type="radio"]:checked + label::before {
  border-color: #f5325c;
}

.radio2.radio-danger2 input[type="radio"]:checked + label::after {
  border-color: #f5325c;
}

.radio2.radio-warning2 input[type="radio"] + label::before {
  background-color: #ffb822;
}

.radio2.radio-warning2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-warning2 input[type="radio"]:checked + label::before {
  border-color: #ffb822;
}

.radio2.radio-warning2 input[type="radio"]:checked + label::after {
  border-color: #ffb822;
}

.radio2.radio-info2 input[type="radio"] + label::before {
  background-color: #12a4ed;
}

.radio2.radio-info2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-info2 input[type="radio"]:checked + label::before {
  border-color: #12a4ed;
}

.radio2.radio-info2 input[type="radio"]:checked + label::after {
  border-color: #12a4ed;
}

.radio2.radio-dark2 input[type="radio"] + label::before {
  background-color: #eaf0f9;
}

.radio2.radio-dark2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-dark2 input[type="radio"]:checked + label::before {
  border-color: #eaf0f9;
}

.radio2.radio-dark2 input[type="radio"]:checked + label::after {
  border-color: #eaf0f9;
}

.radio2.radio-purple2 input[type="radio"] + label::before {
  background-color: #6d81f5;
}

.radio2.radio-purple2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-purple2 input[type="radio"]:checked + label::before {
  border-color: #6d81f5;
}

.radio2.radio-purple2 input[type="radio"]:checked + label::after {
  border-color: #6d81f5;
}

.radio2.radio-pink2 input[type="radio"] + label::before {
  background-color: #fd3c97;
}

.radio2.radio-pink2 input[type="radio"] + label::after {
  background-color: #ffffff;
}

.radio2.radio-pink2 input[type="radio"]:checked + label::before {
  border-color: #fd3c97;
}

.radio2.radio-pink2 input[type="radio"]:checked + label::after {
  border-color: #fd3c97;
}

.pricingTable1 .title1 {
  color: #d1d1e2;
  font-size: 18px;
  font-weight: 600;
  text-transform: capitalize;
}

.pricingTable1 .amount {
  display: block;
  position: relative;
  font-size: 30px;
  font-weight: 700;
  font-family: "Poppins", sans-serif;
  color: #d1d1e2;
}

.pricingTable1 .amount.amount-border::after {
  content: '';
  position: absolute;
  height: 6px;
  background-color: rgba(255, 184, 34, 0.6);
  top: 28px;
  width: 100%;
  right: 0;
  border-radius: 30px;
  transform: rotate(-5deg);
}

.pricingTable1 .pricing-content-2 {
  margin: 0 0 0px 0;
}

.pricingTable1 .pricing-content-2 li {
  color: #d1d1e2;
  line-height: 40px;
}

.pricingTable1 .pricing-content-2 li::before {
  content: "\f00c" !important;
  font-family: 'Font Awesome 5 Free' !important;
  font-weight: 600;
  font-size: 9px;
  text-align: center;
  background-color: rgba(109, 129, 245, 0.1);
  color: #6d81f5 !important;
  width: 20px;
  height: 20px;
  line-height: 20px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 5px;
}

.pricingTable1 .a-animate-blink {
  animation: a-animate-blink 1s step-start 0s infinite;
  animation-fill-mode: initial;
}

@keyframes a-animate-blink {
  50% {
    opacity: 0.0;
  }
}

.account-body {
  width: 100%;
  height: 100vh;
}

.account-body.accountbg {
  background-image: url("../images/auth-bg.png");
  background-position: center center;
  background-size: cover;
  background-repeat: repeat;
  background-color: rgba(0, 0, 0, 0.04);
}

.account-body .auth-header-box {
  background-color: #0c213a;
}

.account-body .auth-header-box h4 {
  color: #ffffff;
}

.account-body .account-social {
  text-align: center;
}

.account-body .account-social h6 {
  color: #d1d1e2;
}

.account-body .account-social h6:after {
  display: inline-block;
  margin: 0 6px 4px 6px;
  content: " ";
  text-shadow: none;
  border: 1px dashed #2d3552;
  width: 120px;
}

.account-body .account-social h6:before {
  display: inline-block;
  margin: 0 6px 4px 6px;
  content: " ";
  text-shadow: none;
  border: 1px dashed #2d3552;
  width: 120px;
}

.account-body .account-social a i {
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  display: inline-block;
  border-radius: 50%;
  font-size: 14px;
  z-index: 5;
}

.account-body .account-social a i.facebook {
  background-color: #1761fd;
  color: #ffffff;
}

.account-body .account-social a i.twitter {
  background-color: #9ba7ca;
  color: #ffffff;
}

.account-body .account-social a i.google {
  background-color: #fd3c97;
  color: #ffffff;
}

.account-body .account-social a:hover .facebook {
  color: #ffffff;
  background-color: #3072fd;
}

.account-body .account-social a:hover .twitter {
  color: #ffffff;
  background-color: #acb6d3;
}

.account-body .account-social a:hover .google {
  color: #ffffff;
  background-color: #fd55a4;
}

.auth-bg {
  background-image: url("../images/auth-bg.png");
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: rgba(0, 0, 0, 0.04);
}

.footer-auth {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
}

.auth-page .logo-lg.logo-light {
  display: inline-block;
}

.auth-page .logo-lg.logo-dark {
  display: none;
}

.ex-page-content h1 {
  font-size: 98px;
  font-weight: 700;
  text-shadow: rgba(137, 151, 189, 0.2) 1px 1px, rgba(137, 151, 189, 0.2) 2px 2px, rgba(137, 151, 189, 0.2) 3px 3px;
}

@media (max-width: 768px) {
  .auth-card {
    margin-top: 34px;
  }
  .footer-auth {
    position: relative;
  }
}

.ribbon1 {
  position: absolute;
  top: -6.1px;
  right: 10px;
}

.ribbon1:after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-top: 10px solid;
}

.ribbon1 span {
  display: block;
  padding: 6px 4px 0px;
  border-top-right-radius: 6px;
  width: 60px;
  font-size: 11px;
  font-weight: 500;
}

.ribbon1 span:before, .ribbon1 span:after {
  position: absolute;
  content: "";
}

.ribbon1 span:before {
  height: 6px;
  width: 4px;
  left: -4px;
  top: 0;
}

.ribbon1 span:after {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
  border-radius: 8px 8px 0 0;
}

.ribbon1 span.rib1-primary:before, .ribbon1 span.rib1-primary {
  background: #1761fd;
}

.ribbon1 span.rib1-primary:after {
  background: #0249df;
}

.ribbon1 span.rib1-secondary:before, .ribbon1 span.rib1-secondary {
  background: #9ba7ca;
}

.ribbon1 span.rib1-secondary:after {
  background: #697bb0;
}

.ribbon1 span.rib1-success:before, .ribbon1 span.rib1-success {
  background: #03d87f;
}

.ribbon1 span.rib1-success:after {
  background: #028d53;
}

.ribbon1 span.rib1-pink:before, .ribbon1 span.rib1-pink {
  background: #fd3c97;
}

.ribbon1 span.rib1-pink:after {
  background: #ea0270;
}

.ribbon1 span.rib1-purple:before, .ribbon1 span.rib1-purple {
  background: #6d81f5;
}

.ribbon1 span.rib1-purple:after {
  background: #2543f0;
}

.ribbon1 span.rib1-warning:before, .ribbon1 span.rib1-warning {
  background: #ffb822;
}

.ribbon1 span.rib1-warning:after {
  background: #d59000;
}

.ribbon1 span.rib1-danger:before, .ribbon1 span.rib1-danger {
  background: #f5325c;
}

.ribbon1 span.rib1-danger:after {
  background: #d00a35;
}

.ribbon1 span.rib1-info:before, .ribbon1 span.rib1-info {
  background: #12a4ed;
}

.ribbon1 span.rib1-info:after {
  background: #0d73a6;
}

.ribbon1 span.rib1-light:before, .ribbon1 span.rib1-light {
  background: #333950;
}

.ribbon1 span.rib1-light:after {
  background: #151821;
}

.ribbon1 span.rib1-dark:before, .ribbon1 span.rib1-dark {
  background: #eaf0f9;
}

.ribbon1 span.rib1-danger:after {
  background: #afc6e8;
}

.ribbon1.rib1-primary:after {
  border-top-color: #1761fd;
}

.ribbon1.rib1-secondary:after {
  border-top-color: #9ba7ca;
}

.ribbon1.rib1-success:after {
  border-top-color: #03d87f;
}

.ribbon1.rib1-warning:after {
  border-top-color: #ffb822;
}

.ribbon1.rib1-danger:after {
  border-top-color: #f5325c;
}

.ribbon1.rib1-info:after {
  border-top-color: #12a4ed;
}

.ribbon1.rib1-light:after {
  border-top-color: #333950;
}

.ribbon1.rib1-dark:after {
  border-top-color: #eaf0f9;
}

.ribbon1.rib1-pink:after {
  border-top-color: #fd3c97;
}

.ribbon1.rib1-purple:after {
  border-top-color: #6d81f5;
}

.ribbon2 {
  width: 30px;
  padding: 8px 0;
  position: absolute;
  top: -6px;
  right: 12px;
  text-align: center;
  border-top-left-radius: 3px;
}

.ribbon2:before, .ribbon2:after {
  content: "";
  position: absolute;
}

.ribbon2:before {
  height: 0;
  width: 0;
  right: -5.5px;
  top: 0.1px;
  border-right: 6px solid transparent;
}

.ribbon2:after {
  height: 0;
  width: 0;
  bottom: -18px;
  left: 0;
  border-bottom: 18px solid transparent;
}

.ribbon2.rib2-primary {
  background: #1761fd;
}

.ribbon2.rib2-primary:before {
  border-bottom: 6px solid #0241c6;
}

.ribbon2.rib2-primary:after {
  border-left: 15px solid #1761fd;
  border-right: 15px solid #1761fd;
}

.ribbon2.rib2-secondary {
  background: #9ba7ca;
}

.ribbon2.rib2-secondary:before {
  border-bottom: 6px solid #697bb0;
}

.ribbon2.rib2-secondary:after {
  border-left: 15px solid #9ba7ca;
  border-right: 15px solid #9ba7ca;
}

.ribbon2.rib2-success {
  background: #03d87f;
}

.ribbon2.rib2-success:before {
  border-bottom: 6px solid #028d53;
}

.ribbon2.rib2-success:after {
  border-left: 15px solid #03d87f;
  border-right: 15px solid #03d87f;
}

.ribbon2.rib2-warning {
  background: #ffb822;
}

.ribbon2.rib2-warning:before {
  border-bottom: 6px solid #d59000;
}

.ribbon2.rib2-warning:after {
  border-left: 15px solid #ffb822;
  border-right: 15px solid #ffb822;
}

.ribbon2.rib2-pink {
  background: #fd3c97;
}

.ribbon2.rib2-pink:before {
  border-bottom: 6px solid #ea0270;
}

.ribbon2.rib2-pink:after {
  border-left: 15px solid #fd3c97;
  border-right: 15px solid #fd3c97;
}

.ribbon3 {
  text-align: center;
  width: 70px;
  height: 26px;
  line-height: 26px;
  padding-right: 12px;
  position: absolute;
  right: -8px;
  top: 10px;
  font-size: 11px;
  font-weight: 500;
}

.ribbon3:before, .ribbon3:after {
  content: "";
  position: absolute;
}

.ribbon3:before {
  height: 0;
  width: 0;
  bottom: -8.5px;
  right: 0.1px;
  border-right: 9px solid transparent;
}

.ribbon3:after {
  height: 0;
  width: 0;
  left: -14.5px;
  border-bottom: 13px solid transparent;
  border-top: 13px solid transparent;
}

.ribbon3.rib3-primary {
  background: #1761fd;
}

.ribbon3.rib3-primary:before {
  border-top: 9px solid #0241c6;
}

.ribbon3.rib3-primary:after {
  border-right: 15px solid #1761fd;
}

.ribbon3.rib3-secondary {
  background: #9ba7ca;
}

.ribbon3.rib3-secondary:before {
  border-top: 9px solid #697bb0;
}

.ribbon3.rib3-secondary:after {
  border-right: 15px solid #9ba7ca;
}

.ribbon3.rib3-warning {
  background: #ffb822;
}

.ribbon3.rib3-warning:before {
  border-top: 9px solid #d59000;
}

.ribbon3.rib3-warning:after {
  border-right: 15px solid #ffb822;
}

.ribbon3.rib3-success {
  background: #03d87f;
}

.ribbon3.rib3-success:before {
  border-top: 9px solid #028d53;
}

.ribbon3.rib3-success:after {
  border-right: 15px solid #03d87f;
}

.ribbon4 {
  width: 100%;
  height: 188px;
  position: absolute;
  top: -8px;
  left: 8px;
  overflow: hidden;
}

.ribbon4:before, .ribbon4:after {
  content: "";
  position: absolute;
}

.ribbon4:before {
  width: 40px;
  height: 8px;
  right: 44px;
  border-radius: 8px 8px 0px 0px;
}

.ribbon4:after {
  width: 8px;
  height: 40px;
  right: 0px;
  top: 44px;
  border-radius: 0px 8px 8px 0px;
}

.ribbon4 .ribbon4-band {
  width: 170px;
  height: 20px;
  line-height: 20px;
  position: absolute;
  top: 16px;
  right: -50px;
  z-index: 2;
  overflow: hidden;
  transform: rotate(45deg);
  border: 1px dashed;
  text-align: center;
  font-size: 11px;
  font-weight: 500;
}

.ribbon4 .ribbon4-band.ribbon4-band-primary {
  box-shadow: 0 0 0 3px #1761fd, 0px 21px 5px -18px rgba(209, 209, 226, 0.6);
  background: #1761fd;
}

.ribbon4 .ribbon4-band.ribbon4-band-secondary {
  box-shadow: 0 0 0 3px #9ba7ca, 0px 21px 5px -18px rgba(209, 209, 226, 0.6);
  background: #9ba7ca;
}

.ribbon4 .ribbon4-band.ribbon4-band-success {
  box-shadow: 0 0 0 3px #03d87f, 0px 21px 5px -18px rgba(209, 209, 226, 0.6);
  background: #03d87f;
}

.ribbon4 .ribbon4-band.ribbon4-band-warning {
  box-shadow: 0 0 0 3px #ffb822, 0px 21px 5px -18px rgba(209, 209, 226, 0.6);
  background: #ffb822;
}

.ribbon4.rib4-primary:before, .ribbon4.rib4-primary:after {
  background: #0249df;
}

.ribbon4.rib4-secondary:before, .ribbon4.rib4-secondary:after {
  background: #7a8ab8;
}

.ribbon4.rib4-success:before, .ribbon4.rib4-success:after {
  background: #02a661;
}

.ribbon4.rib4-warning:before, .ribbon4.rib4-warning:after {
  background: #eea200;
}

.blog-card .meta-box li {
  color: #8c97b7;
  font-size: 11px;
  font-weight: 500;
  margin-left: 0;
  margin-right: 0;
  text-transform: uppercase;
}

.blog-card .meta-box li a {
  color: #9ba7ca;
}

.blog-card .meta-box li::after {
  content: "|";
  margin: 0 8px;
}

.blog-card .meta-box li:last-child:after {
  content: "";
}

.blog-card h4 a {
  color: #dfe4ea;
  font-weight: 500;
  font-size: 20px;
  font-family: "Poppins", sans-serif;
}

.blog-card p {
  font-family: "Roboto", sans-serif;
}

.faq-qa li h6 {
  font-size: 14px;
  color: #d1d1e2;
  font-weight: 500;
}

#accordionExample-faq .accordion-header {
  line-height: inherit;
}

#accordionExample-faq .accordion-header button {
  padding-left: 48px;
}

#accordionExample-faq .accordion-header button:before {
  content: "\f062";
  font-family: 'Font Awesome 5 Free' !important;
  font-weight: 600;
  position: absolute;
  top: 10px;
  left: 10px;
  transition: transform .25s;
  transform: rotate(180deg);
  color: #1761fd;
  font-size: 10px;
  background-color: rgba(23, 97, 253, 0.15);
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 50%;
  text-align: center;
}

#accordionExample-faq .accordion-header button.collapsed::before {
  content: "\f128";
  transform: rotate(0deg);
  font-size: 10px;
}
/*# sourceMappingURL=app-dark.css.map */