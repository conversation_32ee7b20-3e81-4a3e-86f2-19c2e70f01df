.apex-charts {
    min-height: 350px;
}
.card {
    height: 100%;
    margin-bottom: 20px;
}
.report-card {
    min-height: 350px;
}
.analytics-container {
    border: 1px solid #e3ebf6;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 30px;
}
.analytics-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3ebf6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.analytics-content {
    display: grid;
    grid-template-columns: 20% 75%;
    gap: 5%;
}

.statistic-container {
    /* Remove flex properties */
    padding: 15px;
    text-align: center;
}

.graph-container {
    /* Remove flex properties */
    padding: 15px;
    position: relative;
}

.download-items {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 200px;
}
.file-box {
    position: relative;
    padding: 10px;
    border: 1px solid #e3ebf6;
    border-radius: 4px;
    transition: all 0.3s ease;
    background: #fff;
    text-align: center;
    margin-bottom: 10px;
}
.file-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
.file-icon {
    font-size: 16px;
    margin-right: 5px;
    vertical-align: middle;
}
.file-download-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    transition: color 0.3s ease;
}
.file-download-icon:hover {
    color: #3b5de7;
}
.filter-wrapper {
    margin-top: 10px;
    margin-bottom: 20px;
}
.file-info {
    text-align: left;
    margin-left: 5px;
}
.file-name {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 0;
}
.file-meta {
    font-size: 12px;
    color: #6c757d;
}

/* Base responsive font sizes using clamp() */
.report-card h1 {
    font-size: clamp(2rem, 5vw, 3.5rem);
}

.report-card h3 {
    font-size: clamp(1rem, 2vw, 1.2rem);
}

/* Responsive grid layout */
.analytics-content {
    display: grid;
    grid-template-columns: 20% 75%;
    gap: 1%;
}

/* Media queries for different screen sizes */
@media screen and (max-width: 1200px) {
    .analytics-content {
        grid-template-columns: 25% 70%;
    }
}

@media screen and (max-width: 768px) {
    .analytics-content {
        grid-template-columns: 100%;
        gap: 20px;
    }
}