/**
 * Theme: Dastone - Responsive Bootstrap 5 Admin Dashboard
 * Author: <PERSON><PERSON>hemes
 * Upload Js
 */


 // dropify js
 
$(function () {
  // Basic
  $('.dropify').dropify();

  // Translated
  $('.dropify-fr').dropify({
      messages: {
          default: 'Glis<PERSON>z-déposez un fichier ici ou cliquez',
          replace: 'Glis<PERSON>z-déposez un fichier ou cliquez pour remplacer',
          remove:  'Supprimer',
          error:   '<PERSON><PERSON><PERSON><PERSON>, le fichier trop volumineux'
      }
  });

  // Used events
  var drEvent = $('#input-file-events').dropify();

  drEvent.on('dropify.beforeClear', function(event, element){
      return confirm("Do you really want to delete \"" + element.file.name + "\" ?");
  });

  drEvent.on('dropify.afterClear', function(event, element){
      alert('File deleted');
  });

  drEvent.on('dropify.errors', function(event, element){
      console.log('Has Errors');
  });

  var drDestroy = $('#input-file-to-destroy').dropify();
  drDestroy = drDestroy.data('dropify')
  $('#toggleDropify').on('click', function(e){
      e.preventDefault();
      if (drDestroy.isDropified()) {
          drDestroy.destroy();
      } else {
          drDestroy.init();
      }
  })
});