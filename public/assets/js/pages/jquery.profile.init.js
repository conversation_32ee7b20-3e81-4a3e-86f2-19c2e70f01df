/**
 * Theme: Dastone - Responsive Bootstrap 5 Admin Dashboard
 * Author: Mannatthemes
 * Profile Js
 */



var cities = L.layerGroup();

	<PERSON><PERSON>marker([21.17, 72.83]).bindPopup('This is Mannat Themes, CO.').addTo(cities);


	var mbAttr = 'Website - <a href="https://mannatthemes.com/" target="_blank">Mannatthemes</a> '
		mbUrl = 'https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

	var grayscale   = L.tileLayer(mbUrl, {id: 'mapbox/light-v9', tileSize: 512, zoomOffset: -1, attribution: mbAttr}),
		streets  = L.tileLayer(mbUrl, {id: 'mapbox/streets-v11', tileSize: 512, zoomOffset: -1, attribution: mbAttr});

	var map = L.map('user_map', {
		center: [21.17, 72.83],
		zoom: 10,
		layers: [grayscale, cities]
	});

	var baseLayers = {
		"Grayscale": grayscale,
		"Streets": streets
	};

	var overlays = {
		"Cities": cities
	};

	L.control.layers(baseLayers, overlays).addTo(map);


	

   // light_datepick
new Lightpick({
	field: document.getElementById('light_datepick'),
	inline: true,                
  });


