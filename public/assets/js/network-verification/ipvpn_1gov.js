$(document).ready(function(){
    // IPME js

    // Get Interface Btn
    $('#get-ce-interface-btn').on('click', function(){
        // verify all required fields
        var invalid_ce = [];
        $(".ce_required").each(function(){
            // check if field == '' or 'not available' store the id in an array
            if($(this).val() === "" || $(this).val() === "not available") invalid_ce.push($(this).attr("id"));
        });

        // check whether the invalid ce array is not empty
        if (invalid_ce.length > 0) {
            // generate error message
            var errorMessage = "";
            $.each(invalid_ce, function(index, id) {
                var transformedId = id.replace('_', ' ');
                errorMessage += "- " + transformedId + " field value is empty/invalid<br>";
            });
            // popup modal error
            $("#modal-error-content").html(errorMessage);
            $("#modal-error").modal('show');

        }
        else {

            // show loading btn
            $("#get-ce-interface-spinner").removeClass('visually-hidden');
            $("#get-ce-interface-icon").addClass('visually-hidden');

            // Define required parameters
            let ce_url = "https://pisa.tm.com.my/cisco";     // set a default url (cisco)
            if ($("#ce_vendor")==='JUNIPER') ce_url = "https://pisa.tm.com.my/juniper";      // if ce vendor is juniper, change url (juniper)
            var params = {
                ce_ip: $("#ce_ip").val().slice(0, -3)
            };

            // Construct the 'GET' query string
            var queryString = Object.keys(params).map(key => key + '=' + encodeURIComponent(params[key])).join('&');

            // Append the query string to the URL
            var url = ce_url + '/ce/noproxy/v1/get_interface/?' + queryString;

            // Invoke API to get CE interface
            fetch(url)
            .then(response => {
                if (!response.ok) {
                    $("#modal-error-content").html('Unexpected Error returned by the API');
                    // display error modal
                    $("#modal-error").modal('show');
                }
                return response.json();
            })
            .then(data => {
                console.log(data, url);
                // Process the response data here
                $("#get-ce-interface-spinner").addClass('visually-hidden');
                $("#get-ce-interface-icon").removeClass('visually-hidden');
                if (data.status === 'success'){
                    // if cisco
                    if ($("#ce_vendor").val().toLowerCase()==='cisco'){
                        // if output return not interface
                        if (!data.output['name'].toLowerCase().includes('ethernet')) {
                            $("#modal-error-content").text(data.output);
                            // display error modal
                            $("#modal-error").modal('show');
                        }
                        else {
                            $("#ce_interface").val(data.output['name']);
                            $("#ce_interface_msg").text('Successfully verified with CE');
                        }
                    }
                    // if juniper
                    else {
                        // if output return not interface
                        if (!data.output.hasOwnProperty('name')) {
                            $("#modal-error-content").text(data.output);
                            // display error modal
                            $("#modal-error").modal('show');
                        }
                        else {
                            $("#ce_interface").val(data.output['name']);
                            $("#ce_interface_msg").text('Successfully verified with CE');
                        }
                    }
                }
                else {
                    $("#modal-error-content").html(data.output);
                    // display error modal
                    $("#modal-error").modal('show');
                }
                
            })
            .catch(error => {
                // console.error('There was a problem with the fetch operation:', error);
                $("#modal-error-content").html('There was a problem with the fetch operation:', error);
                // display error modal
                $("#modal-error").modal('show');
            });

        }

        // const postData = {
        //     vendor: $("#ce_vendor").val().toLowerCase(),
        //     ce_ip: $("#ce_ip").val(),
        //     service_id: $("#svc_id").val(),                        
        // };
        // $.ajax({
        //     url: "{{ route('net-verification.ce-interface') }}",
        //     method: 'POST',
        //     headers: {
        //         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        //     },
        //     contentType: 'application/json',
        //     dataType: 'json',
        //     data: JSON.stringify(postData),
        //     async: true,
        //     success: function(data) {
        //         console.log(data);
        //         $("#get-ce-interface-spinner").addClass('visually-hidden');
        //         $("#get-ce-interface-icon").removeClass('visually-hidden');
        //         if (data.status === 'success'){
        //             // if cisco
        //             if ($("#ce_vendor").val().toLowerCase()==='cisco'){
        //                 // if output return not interface
        //                 if (!data.output['name'].toLowerCase().includes('ethernet')) {
        //                     $("#modal-error-content").text(data.output);
        //                     // display error modal
        //                     $("#modal-error").modal('show');
        //                 }
        //                 else {
        //                     $("#ce_interface").val(data.output['name']);
        //                     $("#ce_interface_msg").text('Successfully verified with CE');
        //                 }
        //             }
        //             // if juniper
        //             else {
        //                 // if output return not interface
        //                 if (!data.output.hasOwnProperty('name')) {
        //                     $("#modal-error-content").text(data.output);
        //                     // display error modal
        //                     $("#modal-error").modal('show');
        //                 }
        //                 else {
        //                     $("#ce_interface").val(data.output['name']);
        //                     $("#ce_interface_msg").text('Successfully verified with CE');
        //                 }
        //             }
        //         }
        //         else {
        //             $("#modal-error-content").html(data.output);
        //             // display error modal
        //             $("#modal-error").modal('show');
        //         }
        //     }
        // });
    });
});
