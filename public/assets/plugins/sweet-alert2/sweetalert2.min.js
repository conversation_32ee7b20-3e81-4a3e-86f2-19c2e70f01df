!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Sweetalert2=e()}(this,function(){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function a(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function c(){return(c=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function l(t,e,n){return(l=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}()?Reflect.construct:function(t,e,n){var o=[null];o.push.apply(o,e);var i=new(Function.bind.apply(t,o));return n&&u(i,n.prototype),i}).apply(null,arguments)}function d(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function p(t,e,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var o=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}(t,e);if(o){var i=Object.getOwnPropertyDescriptor(o,e);return i.get?i.get.call(n):i.value}})(t,e,n||t)}function f(e){return Object.keys(e).map(function(t){return e[t]})}function m(t){return Array.prototype.slice.call(t)}function h(t){console.error("".concat(N," ").concat(t))}function g(t,e){!function(t){-1===U.indexOf(t)&&(U.push(t),D(t))}('"'.concat(t,'" is deprecated and will be removed in the next major release. Please use "').concat(e,'" instead.'))}function v(t){return t&&Promise.resolve(t)===t}function b(t){return t instanceof Element||function(t){return"object"===r(t)&&t.jquery}(t)}function t(t){var e={};for(var n in t)e[t[n]]="swal2-"+t[n];return e}function y(){return document.body.querySelector(".".concat(z.container))}function w(t){var e=y();return e?e.querySelector(t):null}function e(t){return w(".".concat(t))}function C(){return e(z.popup)}function n(){var t=C();return m(t.querySelectorAll(".".concat(z.icon)))}function k(){var t=n().filter(function(t){return dt(t)});return t.length?t[0]:null}function x(){return e(z.title)}function A(){return e(z.content)}function P(){return e(z.image)}function B(){return e(z["progress-steps"])}function E(){return e(z["validation-message"])}function S(){return w(".".concat(z.actions," .").concat(z.confirm))}function T(){return w(".".concat(z.actions," .").concat(z.cancel))}function L(){return e(z.actions)}function O(){return e(z.header)}function M(){return e(z.footer)}function H(){return e(z["timer-progress-bar"])}function j(){return e(z.close)}function V(){var t=m(C().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(function(t,e){return t=parseInt(t.getAttribute("tabindex")),(e=parseInt(e.getAttribute("tabindex")))<t?1:t<e?-1:0}),e=m(C().querySelectorAll('\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex="0"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n')).filter(function(t){return"-1"!==t.getAttribute("tabindex")});return function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(t.concat(e)).filter(function(t){return dt(t)})}function I(){return!K()&&!document.body.classList.contains(z["no-backdrop"])}function q(t,e){if(!e)return!1;for(var n=e.split(/\s+/),o=0;o<n.length;o++)if(!t.classList.contains(n[o]))return!1;return!0}function R(t,e,n){if(!function(e,n){m(e.classList).forEach(function(t){-1===f(z).indexOf(t)&&-1===f(W).indexOf(t)&&-1===f(n.showClass).indexOf(t)&&e.classList.remove(t)})}(t,e),e.customClass&&e.customClass[n]){if("string"!=typeof e.customClass[n]&&!e.customClass[n].forEach)return D("Invalid type of customClass.".concat(n,'! Expected string or iterable object, got "').concat(r(e.customClass[n]),'"'));st(t,e.customClass[n])}}var N="SweetAlert2:",D=function(t){console.warn("".concat(N," ").concat(t))},U=[],F=function(t){return"function"==typeof t?t():t},_=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),z=t(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","toast","toast-shown","toast-column","show","hide","close","title","header","content","html-container","actions","confirm","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error"]),W=t(["success","warning","info","question","error"]),K=function(){return document.body.classList.contains(z["toast-shown"])},Y={previousBodyPadding:null};function Z(t,e){if(!e)return null;switch(e){case"select":case"textarea":case"file":return lt(t,z[e]);case"checkbox":return t.querySelector(".".concat(z.checkbox," input"));case"radio":return t.querySelector(".".concat(z.radio," input:checked"))||t.querySelector(".".concat(z.radio," input:first-child"));case"range":return t.querySelector(".".concat(z.range," input"));default:return lt(t,z.input)}}function Q(t){if(t.focus(),"file"!==t.type){var e=t.value;t.value="",t.value=e}}function $(t,e,n){t&&e&&("string"==typeof e&&(e=e.split(/\s+/).filter(Boolean)),e.forEach(function(e){t.forEach?t.forEach(function(t){n?t.classList.add(e):t.classList.remove(e)}):n?t.classList.add(e):t.classList.remove(e)}))}function J(t,e,n){n||0===parseInt(n)?t.style[e]="number"==typeof n?"".concat(n,"px"):n:t.style.removeProperty(e)}function X(t,e){var n=1<arguments.length&&void 0!==e?e:"flex";t.style.opacity="",t.style.display=n}function G(t){t.style.opacity="",t.style.display="none"}function tt(t,e,n){e?X(t,n):G(t)}function et(t){var e=window.getComputedStyle(t),n=parseFloat(e.getPropertyValue("animation-duration")||"0"),o=parseFloat(e.getPropertyValue("transition-duration")||"0");return 0<n||0<o}function nt(t,e){var n=1<arguments.length&&void 0!==e&&e,o=H();dt(o)&&(n&&(o.style.transition="none",o.style.width="100%"),setTimeout(function(){o.style.transition="width ".concat(t/1e3,"s linear"),o.style.width="0%"},10))}function ot(){return"undefined"==typeof window||"undefined"==typeof document}function it(t){Je.isVisible()&&ct!==t.target.value&&Je.resetValidationMessage(),ct=t.target.value}function rt(t,e){t instanceof HTMLElement?e.appendChild(t):"object"===r(t)?mt(e,t):t&&(e.innerHTML=t)}function at(t,e){var n=L(),o=S(),i=T();e.showConfirmButton||e.showCancelButton||G(n),R(n,e,"actions"),gt(o,"confirm",e),gt(i,"cancel",e),e.buttonsStyling?function(t,e,n){st([t,e],z.styled),n.confirmButtonColor&&(t.style.backgroundColor=n.confirmButtonColor);n.cancelButtonColor&&(e.style.backgroundColor=n.cancelButtonColor);var o=window.getComputedStyle(t).getPropertyValue("background-color");t.style.borderLeftColor=o,t.style.borderRightColor=o}(o,i,e):(ut([o,i],z.styled),o.style.backgroundColor=o.style.borderLeftColor=o.style.borderRightColor="",i.style.backgroundColor=i.style.borderLeftColor=i.style.borderRightColor=""),e.reverseButtons&&o.parentNode.insertBefore(i,o)}var ct,st=function(t,e){$(t,e,!0)},ut=function(t,e){$(t,e,!1)},lt=function(t,e){for(var n=0;n<t.childNodes.length;n++)if(q(t.childNodes[n],e))return t.childNodes[n]},dt=function(t){return!(!t||!(t.offsetWidth||t.offsetHeight||t.getClientRects().length))},pt='\n <div aria-labelledby="'.concat(z.title,'" aria-describedby="').concat(z.content,'" class="').concat(z.popup,'" tabindex="-1">\n   <div class="').concat(z.header,'">\n     <ul class="').concat(z["progress-steps"],'"></ul>\n     <div class="').concat(z.icon," ").concat(W.error,'"></div>\n     <div class="').concat(z.icon," ").concat(W.question,'"></div>\n     <div class="').concat(z.icon," ").concat(W.warning,'"></div>\n     <div class="').concat(z.icon," ").concat(W.info,'"></div>\n     <div class="').concat(z.icon," ").concat(W.success,'"></div>\n     <img class="').concat(z.image,'" />\n     <h2 class="').concat(z.title,'" id="').concat(z.title,'"></h2>\n     <button type="button" class="').concat(z.close,'"></button>\n   </div>\n   <div class="').concat(z.content,'">\n     <div id="').concat(z.content,'" class="').concat(z["html-container"],'"></div>\n     <input class="').concat(z.input,'" />\n     <input type="file" class="').concat(z.file,'" />\n     <div class="').concat(z.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(z.select,'"></select>\n     <div class="').concat(z.radio,'"></div>\n     <label for="').concat(z.checkbox,'" class="').concat(z.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(z.label,'"></span>\n     </label>\n     <textarea class="').concat(z.textarea,'"></textarea>\n     <div class="').concat(z["validation-message"],'" id="').concat(z["validation-message"],'"></div>\n   </div>\n   <div class="').concat(z.actions,'">\n     <button type="button" class="').concat(z.confirm,'">OK</button>\n     <button type="button" class="').concat(z.cancel,'">Cancel</button>\n   </div>\n   <div class="').concat(z.footer,'"></div>\n   <div class="').concat(z["timer-progress-bar"],'"></div>\n </div>\n').replace(/(^|\n)\s*/g,""),ft=function(t){if(!function(){var t=y();t&&(t.parentNode.removeChild(t),ut([document.documentElement,document.body],[z["no-backdrop"],z["toast-shown"],z["has-column"]]))}(),ot())h("SweetAlert2 requires document to initialize");else{var e=document.createElement("div");e.className=z.container,e.innerHTML=pt;var n=function(t){return"string"==typeof t?document.querySelector(t):t}(t.target);n.appendChild(e),function(t){var e=C();e.setAttribute("role",t.toast?"alert":"dialog"),e.setAttribute("aria-live",t.toast?"polite":"assertive"),t.toast||e.setAttribute("aria-modal","true")}(t),function(t){"rtl"===window.getComputedStyle(t).direction&&st(y(),z.rtl)}(n),function(){var t=A(),e=lt(t,z.input),n=lt(t,z.file),o=t.querySelector(".".concat(z.range," input")),i=t.querySelector(".".concat(z.range," output")),r=lt(t,z.select),a=t.querySelector(".".concat(z.checkbox," input")),c=lt(t,z.textarea);e.oninput=it,n.onchange=it,r.onchange=it,a.onchange=it,c.oninput=it,o.oninput=function(t){it(t),i.value=o.value},o.onchange=function(t){it(t),o.nextSibling.value=o.value}}()}},mt=function(t,e){if(t.innerHTML="",0 in e)for(var n=0;n in e;n++)t.appendChild(e[n].cloneNode(!0));else t.appendChild(e.cloneNode(!0))},ht=function(){if(ot())return!1;var t=document.createElement("div"),e={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&void 0!==t.style[n])return e[n];return!1}();function gt(t,e,n){tt(t,n["show".concat(function(t){return t.charAt(0).toUpperCase()+t.slice(1)}(e),"Button")],"inline-block"),t.innerHTML=n["".concat(e,"ButtonText")],t.setAttribute("aria-label",n["".concat(e,"ButtonAriaLabel")]),t.className=z[e],R(t,n,"".concat(e,"Button")),st(t,n["".concat(e,"ButtonClass")])}function vt(t,e){var n=y();if(n){!function(t,e){"string"==typeof e?t.style.background=e:e||st([document.documentElement,document.body],z["no-backdrop"])}(n,e.backdrop),!e.backdrop&&e.allowOutsideClick&&D('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),function(t,e){e in z?st(t,z[e]):(D('The "position" parameter is not valid, defaulting to "center"'),st(t,z.center))}(n,e.position),function(t,e){if(e&&"string"==typeof e){var n="grow-".concat(e);n in z&&st(t,z[n])}}(n,e.grow),R(n,e,"container");var o=document.body.getAttribute("data-swal2-queue-step");o&&(n.setAttribute("data-queue-step",o),document.body.removeAttribute("data-swal2-queue-step"))}}function bt(t,e){t.placeholder&&!e.inputPlaceholder||(t.placeholder=e.inputPlaceholder)}var yt={promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap},wt=["input","file","range","select","radio","checkbox","textarea"],Ct=function(t){if(!Pt[t.input])return h('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(t.input,'"'));var e=At(t.input),n=Pt[t.input](e,t);X(n),setTimeout(function(){Q(n)})},kt=function(t,e){var n=Z(A(),t);if(n)for(var o in!function(t){for(var e=0;e<t.attributes.length;e++){var n=t.attributes[e].name;-1===["type","value","style"].indexOf(n)&&t.removeAttribute(n)}}(n),e)"range"===t&&"placeholder"===o||n.setAttribute(o,e[o])},xt=function(t){var e=At(t.input);t.customClass&&st(e,t.customClass.input)},At=function(t){var e=z[t]?z[t]:z.input;return lt(A(),e)},Pt={};Pt.text=Pt.email=Pt.password=Pt.number=Pt.tel=Pt.url=function(t,e){return"string"==typeof e.inputValue||"number"==typeof e.inputValue?t.value=e.inputValue:v(e.inputValue)||D('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(r(e.inputValue),'"')),bt(t,e),t.type=e.input,t},Pt.file=function(t,e){return bt(t,e),t},Pt.range=function(t,e){var n=t.querySelector("input"),o=t.querySelector("output");return n.value=e.inputValue,n.type=e.input,o.value=e.inputValue,t},Pt.select=function(t,e){if(t.innerHTML="",e.inputPlaceholder){var n=document.createElement("option");n.innerHTML=e.inputPlaceholder,n.value="",n.disabled=!0,n.selected=!0,t.appendChild(n)}return t},Pt.radio=function(t){return t.innerHTML="",t},Pt.checkbox=function(t,e){var n=Z(A(),"checkbox");return n.value=1,n.id=z.checkbox,n.checked=Boolean(e.inputValue),t.querySelector("span").innerHTML=e.inputPlaceholder,t},Pt.textarea=function(e,t){if(e.value=t.inputValue,bt(e,t),"MutationObserver"in window){var n=parseInt(window.getComputedStyle(C()).width),o=parseInt(window.getComputedStyle(C()).paddingLeft)+parseInt(window.getComputedStyle(C()).paddingRight);new MutationObserver(function(){var t=e.offsetWidth+o;C().style.width=n<t?"".concat(t,"px"):null}).observe(e,{attributes:!0,attributeFilter:["style"]})}return e};function Bt(t,e){var n=A().querySelector("#".concat(z.content));e.html?(rt(e.html,n),X(n,"block")):e.text?(n.textContent=e.text,X(n,"block")):G(n),function(t,o){var i=A(),e=yt.innerParams.get(t),r=!e||o.input!==e.input;wt.forEach(function(t){var e=z[t],n=lt(i,e);kt(t,o.inputAttributes),n.className=e,r&&G(n)}),o.input&&(r&&Ct(o),xt(o))}(t,e),R(A(),e,"content")}function Et(){return y().getAttribute("data-queue-step")}function St(t,i){var r=B();if(!i.progressSteps||0===i.progressSteps.length)return G(r);X(r),r.innerHTML="";var a=parseInt(void 0===i.currentProgressStep?Et():i.currentProgressStep);a>=i.progressSteps.length&&D("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),i.progressSteps.forEach(function(t,e){var n=function(t){var e=document.createElement("li");return st(e,z["progress-step"]),e.innerHTML=t,e}(t);if(r.appendChild(n),e===a&&st(n,z["active-progress-step"]),e!==i.progressSteps.length-1){var o=function(t){var e=document.createElement("li");return st(e,z["progress-step-line"]),t.progressStepsDistance&&(e.style.width=t.progressStepsDistance),e}(t);r.appendChild(o)}})}function Tt(t,e){var n=O();R(n,e,"header"),St(0,e),function(t,e){var n=yt.innerParams.get(t);if(n&&e.icon===n.icon&&k())R(k(),e,"icon");else if(Mt(),e.icon)if(-1!==Object.keys(W).indexOf(e.icon)){var o=w(".".concat(z.icon,".").concat(W[e.icon]));X(o),jt(o,e),Ht(),R(o,e,"icon"),st(o,e.showClass.icon)}else h('Unknown icon! Expected "success", "error", "warning", "info" or "question", got "'.concat(e.icon,'"'))}(t,e),function(t,e){var n=P();if(!e.imageUrl)return G(n);X(n),n.setAttribute("src",e.imageUrl),n.setAttribute("alt",e.imageAlt),J(n,"width",e.imageWidth),J(n,"height",e.imageHeight),n.className=z.image,R(n,e,"image")}(0,e),function(t,e){var n=x();tt(n,e.title||e.titleText),e.title&&rt(e.title,n),e.titleText&&(n.innerText=e.titleText),R(n,e,"title")}(0,e),function(t,e){var n=j();n.innerHTML=e.closeButtonHtml,R(n,e,"closeButton"),tt(n,e.showCloseButton),n.setAttribute("aria-label",e.closeButtonAriaLabel)}(0,e)}function Lt(t,e){!function(t,e){var n=C();J(n,"width",e.width),J(n,"padding",e.padding),e.background&&(n.style.background=e.background),qt(n,e)}(0,e),vt(0,e),Tt(t,e),Bt(t,e),at(0,e),function(t,e){var n=M();tt(n,e.footer),e.footer&&rt(e.footer,n),R(n,e,"footer")}(0,e),"function"==typeof e.onRender&&e.onRender(C())}function Ot(){return S()&&S().click()}var Mt=function(){for(var t=n(),e=0;e<t.length;e++)G(t[e])},Ht=function(){for(var t=C(),e=window.getComputedStyle(t).getPropertyValue("background-color"),n=t.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),o=0;o<n.length;o++)n[o].style.backgroundColor=e},jt=function(t,e){if(t.innerHTML="",e.iconHtml)t.innerHTML=Vt(e.iconHtml);else if("success"===e.icon)t.innerHTML='\n      <div class="swal2-success-circular-line-left"></div>\n      <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n      <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n      <div class="swal2-success-circular-line-right"></div>\n    ';else if("error"===e.icon)t.innerHTML='\n      <span class="swal2-x-mark">\n        <span class="swal2-x-mark-line-left"></span>\n        <span class="swal2-x-mark-line-right"></span>\n      </span>\n    ';else{t.innerHTML=Vt({question:"?",warning:"!",info:"i"}[e.icon])}},Vt=function(t){return'<div class="'.concat(z["icon-content"],'">').concat(t,"</div>")},It=[],qt=function(t,e){t.className="".concat(z.popup," ").concat(dt(t)?e.showClass.popup:""),e.toast?(st([document.documentElement,document.body],z["toast-shown"]),st(t,z.toast)):st(t,z.modal),R(t,e,"popup"),"string"==typeof e.customClass&&st(t,e.customClass),e.icon&&st(t,z["icon-".concat(e.icon)])};function Rt(){var t=C();t||Je.fire(),t=C();var e=L(),n=S();X(e),X(n,"inline-block"),st([t,e],z.loading),n.disabled=!0,t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()}function Nt(){return new Promise(function(t){var e=window.scrollX,n=window.scrollY;zt.restoreFocusTimeout=setTimeout(function(){zt.previousActiveElement&&zt.previousActiveElement.focus?(zt.previousActiveElement.focus(),zt.previousActiveElement=null):document.body&&document.body.focus(),t()},100),void 0!==e&&void 0!==n&&window.scrollTo(e,n)})}function Dt(){if(zt.timeout)return function(){var t=H(),e=parseInt(window.getComputedStyle(t).width);t.style.removeProperty("transition"),t.style.width="100%";var n=parseInt(window.getComputedStyle(t).width),o=parseInt(e/n*100);t.style.removeProperty("transition"),t.style.width="".concat(o,"%")}(),zt.timeout.stop()}function Ut(){if(zt.timeout){var t=zt.timeout.start();return nt(t),t}}function Ft(t){return Object.prototype.hasOwnProperty.call(Wt,t)}function _t(t){return Yt[t]}var zt={},Wt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconHtml:void 0,toast:!1,animation:!0,showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:void 0,target:"body",backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showCancelButton:!1,preConfirm:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusCancel:!1,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",showLoaderOnConfirm:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,onBeforeOpen:void 0,onOpen:void 0,onRender:void 0,onClose:void 0,onAfterClose:void 0,scrollbarPadding:!0},Kt=["title","titleText","text","html","icon","customClass","showConfirmButton","showCancelButton","confirmButtonText","confirmButtonAriaLabel","confirmButtonColor","cancelButtonText","cancelButtonAriaLabel","cancelButtonColor","buttonsStyling","reverseButtons","imageUrl","imageWidth","imageHeight","imageAlt","progressSteps","currentProgressStep"],Yt={animation:'showClass" and "hideClass'},Zt=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusCancel","heightAuto","keydownListenerCapture"],Qt=Object.freeze({isValidParameter:Ft,isUpdatableParameter:function(t){return-1!==Kt.indexOf(t)},isDeprecatedParameter:_t,argsToParams:function(o){var i={};return"object"!==r(o[0])||b(o[0])?["title","html","icon"].forEach(function(t,e){var n=o[e];"string"==typeof n||b(n)?i[t]=n:void 0!==n&&h("Unexpected type of ".concat(t,'! Expected "string" or "Element", got ').concat(r(n)))}):c(i,o[0]),i},isVisible:function(){return dt(C())},clickConfirm:Ot,clickCancel:function(){return T()&&T().click()},getContainer:y,getPopup:C,getTitle:x,getContent:A,getHtmlContainer:function(){return e(z["html-container"])},getImage:P,getIcon:k,getIcons:n,getCloseButton:j,getActions:L,getConfirmButton:S,getCancelButton:T,getHeader:O,getFooter:M,getFocusableElements:V,getValidationMessage:E,isLoading:function(){return C().hasAttribute("data-loading")},fire:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return l(this,e)},mixin:function(n){return function(t){function e(){return o(this,e),d(this,s(e).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&u(t,e)}(e,t),a(e,[{key:"_main",value:function(t){return p(s(e.prototype),"_main",this).call(this,c({},n,t))}}]),e}(this)},queue:function(t){var r=this;It=t;function a(t,e){It=[],t(e)}var c=[];return new Promise(function(i){!function e(n,o){n<It.length?(document.body.setAttribute("data-swal2-queue-step",n),r.fire(It[n]).then(function(t){void 0!==t.value?(c.push(t.value),e(n+1,o)):a(i,{dismiss:t.dismiss})})):a(i,{value:c})}(0)})},getQueueStep:Et,insertQueueStep:function(t,e){return e&&e<It.length?It.splice(e,0,t):It.push(t)},deleteQueueStep:function(t){void 0!==It[t]&&It.splice(t,1)},showLoading:Rt,enableLoading:Rt,getTimerLeft:function(){return zt.timeout&&zt.timeout.getTimerLeft()},stopTimer:Dt,resumeTimer:Ut,toggleTimer:function(){var t=zt.timeout;return t&&(t.running?Dt():Ut())},increaseTimer:function(t){if(zt.timeout){var e=zt.timeout.increase(t);return nt(e,!0),e}},isTimerRunning:function(){return zt.timeout&&zt.timeout.isRunning()}});function $t(){var t=yt.innerParams.get(this);if(t){var e=yt.domCache.get(this);t.showConfirmButton||(G(e.confirmButton),t.showCancelButton||G(e.actions)),ut([e.popup,e.actions],z.loading),e.popup.removeAttribute("aria-busy"),e.popup.removeAttribute("data-loading"),e.confirmButton.disabled=!1,e.cancelButton.disabled=!1}}function Jt(){null===Y.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(Y.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight="".concat(Y.previousBodyPadding+function(){var t=document.createElement("div");t.className=z["scrollbar-measure"],document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e}(),"px"))}function Xt(){return!!window.MSInputMethodContext&&!!document.documentMode}function Gt(){var t=y(),e=C();t.style.removeProperty("align-items"),e.offsetTop<0&&(t.style.alignItems="flex-start")}var te=function(){var e,n=y();n.ontouchstart=function(t){e=t.target===n||!function(t){return!!(t.scrollHeight>t.clientHeight)}(n)&&"INPUT"!==t.target.tagName},n.ontouchmove=function(t){e&&(t.preventDefault(),t.stopPropagation())}},ee={swalPromiseResolve:new WeakMap};function ne(t,e,n,o){n?ae(t,o):(Nt().then(function(){return ae(t,o)}),zt.keydownTarget.removeEventListener("keydown",zt.keydownHandler,{capture:zt.keydownListenerCapture}),zt.keydownHandlerAdded=!1),e.parentNode&&e.parentNode.removeChild(e),I()&&(null!==Y.previousBodyPadding&&(document.body.style.paddingRight="".concat(Y.previousBodyPadding,"px"),Y.previousBodyPadding=null),function(){if(q(document.body,z.iosfix)){var t=parseInt(document.body.style.top,10);ut(document.body,z.iosfix),document.body.style.top="",document.body.scrollTop=-1*t}}(),"undefined"!=typeof window&&Xt()&&window.removeEventListener("resize",Gt),m(document.body.children).forEach(function(t){t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})),ut([document.documentElement,document.body],[z.shown,z["height-auto"],z["no-backdrop"],z["toast-shown"],z["toast-column"]])}function oe(t){var e=C();if(e){var n=yt.innerParams.get(this);if(n&&!q(e,n.hideClass.popup)){var o=ee.swalPromiseResolve.get(this);ut(e,n.showClass.popup),st(e,n.hideClass.popup);var i=y();ut(i,n.showClass.backdrop),st(i,n.hideClass.backdrop),function(t,e,n){var o=y(),i=ht&&et(e),r=n.onClose,a=n.onAfterClose;if(r!==null&&typeof r==="function"){r(e)}if(i){re(t,e,o,a)}else{ne(t,o,K(),a)}}(this,e,n),o(t||{})}}}function ie(t){for(var e in t)t[e]=new WeakMap}var re=function(t,e,n,o){zt.swalCloseEventFinishedCallback=ne.bind(null,t,n,K(),o),e.addEventListener(ht,function(t){t.target===e&&(zt.swalCloseEventFinishedCallback(),delete zt.swalCloseEventFinishedCallback)})},ae=function(t,e){setTimeout(function(){null!==e&&"function"==typeof e&&e(),C()||function(t){delete t.params,delete zt.keydownHandler,delete zt.keydownTarget,ie(yt),ie(ee)}(t)})};function ce(t,e,n){var o=yt.domCache.get(t);e.forEach(function(t){o[t].disabled=n})}function se(t,e){if(!t)return!1;if("radio"===t.type)for(var n=t.parentNode.parentNode.querySelectorAll("input"),o=0;o<n.length;o++)n[o].disabled=e;else t.disabled=e}var ue=function(){function n(t,e){o(this,n),this.callback=t,this.remaining=e,this.running=!1,this.start()}return a(n,[{key:"start",value:function(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}},{key:"stop",value:function(){return this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date-this.started),this.remaining}},{key:"increase",value:function(t){var e=this.running;return e&&this.stop(),this.remaining+=t,e&&this.start(),this.remaining}},{key:"getTimerLeft",value:function(){return this.running&&(this.stop(),this.start()),this.remaining}},{key:"isRunning",value:function(){return this.running}}]),n}(),le={email:function(t,e){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid email address")},url:function(t,e){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid URL")}};function de(t){!function(e){e.inputValidator||Object.keys(le).forEach(function(t){e.input===t&&(e.inputValidator=le[t])})}(t),t.showLoaderOnConfirm&&!t.preConfirm&&D("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),t.animation=F(t.animation),function(t){t.target&&("string"!=typeof t.target||document.querySelector(t.target))&&("string"==typeof t.target||t.target.appendChild)||(D('Target parameter is not valid, defaulting to "body"'),t.target="body")}(t),"string"==typeof t.title&&(t.title=t.title.split("\n").join("<br />")),ft(t)}function pe(t,e){t.removeEventListener(ht,pe),e.style.overflowY="auto"}function fe(t){var e=y(),n=C();"function"==typeof t.onBeforeOpen&&t.onBeforeOpen(n),xe(e,n,t),Ce(e,n),I()&&ke(e,t.scrollbarPadding),K()||zt.previousActiveElement||(zt.previousActiveElement=document.activeElement),"function"==typeof t.onOpen&&setTimeout(function(){return t.onOpen(n)})}function me(t,e){"select"===e.input||"radio"===e.input?Ee(t,e):-1!==["text","email","number","tel","textarea"].indexOf(e.input)&&v(e.inputValue)&&Se(t,e)}function he(t,e){t.disableButtons(),e.input?Oe(t,e):Me(t,e,!0)}function ge(t,e){t.disableButtons(),e(_.cancel)}function ve(t,e){t.closePopup({value:e})}function be(e,t,n,o){t.keydownTarget&&t.keydownHandlerAdded&&(t.keydownTarget.removeEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!1),n.toast||(t.keydownHandler=function(t){return Ve(e,t,n,o)},t.keydownTarget=n.keydownListenerCapture?window:C(),t.keydownListenerCapture=n.keydownListenerCapture,t.keydownTarget.addEventListener("keydown",t.keydownHandler,{capture:t.keydownListenerCapture}),t.keydownHandlerAdded=!0)}function ye(t,e,n){for(var o=V(),i=0;i<o.length;i++)return(e+=n)===o.length?e=0:-1===e&&(e=o.length-1),o[e].focus();C().focus()}function we(t,e,n){e.toast?De(t,e,n):(Fe(t),_e(t),ze(t,e,n))}var Ce=function(e,n){ht&&et(n)?(e.style.overflowY="hidden",n.addEventListener(ht,function(t){t.target===n&&pe.bind(null,n,e)})):e.style.overflowY="auto"},ke=function(t,e){!function(){if((/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints)&&!q(document.body,z.iosfix)){var t=document.body.scrollTop;document.body.style.top="".concat(-1*t,"px"),st(document.body,z.iosfix),te()}}(),"undefined"!=typeof window&&Xt()&&(Gt(),window.addEventListener("resize",Gt)),m(document.body.children).forEach(function(t){t===y()||function(t,e){if("function"==typeof t.contains)return t.contains(e)}(t,y())||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")),t.setAttribute("aria-hidden","true"))}),e&&Jt(),setTimeout(function(){t.scrollTop=0})},xe=function(t,e,n){st(t,n.showClass.backdrop),X(e),st(e,n.showClass.popup),st([document.documentElement,document.body],z.shown),n.heightAuto&&n.backdrop&&!n.toast&&st([document.documentElement,document.body],z["height-auto"])},Ae=function(t){return t.checked?1:0},Pe=function(t){return t.checked?t.value:null},Be=function(t){return t.files.length?null!==t.getAttribute("multiple")?t.files:t.files[0]:null},Ee=function(e,n){function o(t){return Te[n.input](i,Le(t),n)}var i=A();v(n.inputOptions)?(Rt(),n.inputOptions.then(function(t){e.hideLoading(),o(t)})):"object"===r(n.inputOptions)?o(n.inputOptions):h("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(r(n.inputOptions)))},Se=function(e,n){var o=e.getInput();G(o),n.inputValue.then(function(t){o.value="number"===n.input?parseFloat(t)||0:"".concat(t),X(o),o.focus(),e.hideLoading()}).catch(function(t){h("Error in inputValue promise: ".concat(t)),o.value="",X(o),o.focus(),e.hideLoading()})},Te={select:function(t,e,i){var r=lt(t,z.select);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("option");o.value=e,o.innerHTML=n,i.inputValue.toString()===e.toString()&&(o.selected=!0),r.appendChild(o)}),r.focus()},radio:function(t,e,a){var c=lt(t,z.radio);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("input"),i=document.createElement("label");o.type="radio",o.name=z.radio,o.value=e,a.inputValue.toString()===e.toString()&&(o.checked=!0);var r=document.createElement("span");r.innerHTML=n,r.className=z.label,i.appendChild(o),i.appendChild(r),c.appendChild(i)});var n=c.querySelectorAll("input");n.length&&n[0].focus()}},Le=function(e){var n=[];return"undefined"!=typeof Map&&e instanceof Map?e.forEach(function(t,e){n.push([e,t])}):Object.keys(e).forEach(function(t){n.push([t,e[t]])}),n},Oe=function(e,n){var o=function(t,e){var n=t.getInput();if(!n)return null;switch(e.input){case"checkbox":return Ae(n);case"radio":return Pe(n);case"file":return Be(n);default:return e.inputAutoTrim?n.value.trim():n.value}}(e,n);n.inputValidator?(e.disableInput(),Promise.resolve().then(function(){return n.inputValidator(o,n.validationMessage)}).then(function(t){e.enableButtons(),e.enableInput(),t?e.showValidationMessage(t):Me(e,n,o)})):e.getInput().checkValidity()?Me(e,n,o):(e.enableButtons(),e.showValidationMessage(n.validationMessage))},Me=function(e,t,n){(t.showLoaderOnConfirm&&Rt(),t.preConfirm)?(e.resetValidationMessage(),Promise.resolve().then(function(){return t.preConfirm(n,t.validationMessage)}).then(function(t){dt(E())||!1===t?e.hideLoading():ve(e,void 0===t?n:t)})):ve(e,n)},He=["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Left","Right","Up","Down"],je=["Escape","Esc"],Ve=function(t,e,n,o){n.stopKeydownPropagation&&e.stopPropagation(),"Enter"===e.key?Ie(t,e,n):"Tab"===e.key?qe(e,n):-1!==He.indexOf(e.key)?Re():-1!==je.indexOf(e.key)&&Ne(e,n,o)},Ie=function(t,e,n){if(!e.isComposing&&e.target&&t.getInput()&&e.target.outerHTML===t.getInput().outerHTML){if(-1!==["textarea","file"].indexOf(n.input))return;Ot(),e.preventDefault()}},qe=function(t){for(var e=t.target,n=V(),o=-1,i=0;i<n.length;i++)if(e===n[i]){o=i;break}t.shiftKey?ye(0,o,-1):ye(0,o,1),t.stopPropagation(),t.preventDefault()},Re=function(){var t=S(),e=T();document.activeElement===t&&dt(e)?e.focus():document.activeElement===e&&dt(t)&&t.focus()},Ne=function(t,e,n){F(e.allowEscapeKey)&&(t.preventDefault(),n(_.esc))},De=function(t,e,n){t.popup.onclick=function(){e.showConfirmButton||e.showCancelButton||e.showCloseButton||e.input||n(_.close)}},Ue=!1,Fe=function(e){e.popup.onmousedown=function(){e.container.onmouseup=function(t){e.container.onmouseup=void 0,t.target===e.container&&(Ue=!0)}}},_e=function(e){e.container.onmousedown=function(){e.popup.onmouseup=function(t){e.popup.onmouseup=void 0,t.target!==e.popup&&!e.popup.contains(t.target)||(Ue=!0)}}},ze=function(e,n,o){e.container.onclick=function(t){Ue?Ue=!1:t.target===e.container&&F(n.allowOutsideClick)&&o(_.backdrop)}};var We=function(t,e,n){var o=H();G(o),e.timer&&(t.timeout=new ue(function(){n("timer"),delete t.timeout},e.timer),e.timerProgressBar&&(X(o),setTimeout(function(){nt(e.timer)})))},Ke=function(t,e){if(!e.toast)return F(e.allowEnterKey)?e.focusCancel&&dt(t.cancelButton)?t.cancelButton.focus():e.focusConfirm&&dt(t.confirmButton)?t.confirmButton.focus():void ye(0,-1,1):Ye()},Ye=function(){document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()};var Ze,Qe=Object.freeze({hideLoading:$t,disableLoading:$t,getInput:function(t){var e=yt.innerParams.get(t||this),n=yt.domCache.get(t||this);return n?Z(n.content,e.input):null},close:oe,closePopup:oe,closeModal:oe,closeToast:oe,enableButtons:function(){ce(this,["confirmButton","cancelButton"],!1)},disableButtons:function(){ce(this,["confirmButton","cancelButton"],!0)},enableInput:function(){return se(this.getInput(),!1)},disableInput:function(){return se(this.getInput(),!0)},showValidationMessage:function(t){var e=yt.domCache.get(this);e.validationMessage.innerHTML=t;var n=window.getComputedStyle(e.popup);e.validationMessage.style.marginLeft="-".concat(n.getPropertyValue("padding-left")),e.validationMessage.style.marginRight="-".concat(n.getPropertyValue("padding-right")),X(e.validationMessage);var o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedBy",z["validation-message"]),Q(o),st(o,z.inputerror))},resetValidationMessage:function(){var t=yt.domCache.get(this);t.validationMessage&&G(t.validationMessage);var e=this.getInput();e&&(e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedBy"),ut(e,z.inputerror))},getProgressSteps:function(){return yt.domCache.get(this).progressSteps},_main:function(t){!function(t){for(var e in t)Ft(i=e)||D('Unknown parameter "'.concat(i,'"')),t.toast&&(o=e,-1!==Zt.indexOf(o)&&D('The parameter "'.concat(o,'" is incompatible with toasts'))),_t(n=e)&&g(n,_t(n));var n,o,i}(t),C()&&zt.swalCloseEventFinishedCallback&&(zt.swalCloseEventFinishedCallback(),delete zt.swalCloseEventFinishedCallback),zt.deferDisposalTimer&&(clearTimeout(zt.deferDisposalTimer),delete zt.deferDisposalTimer);var e=function(t){var e=c({},Wt.showClass,t.showClass),n=c({},Wt.hideClass,t.hideClass),o=c({},Wt,t);if(o.showClass=e,o.hideClass=n,t.animation===false){o.showClass={popup:"",backdrop:"swal2-backdrop-show swal2-noanimation"};o.hideClass={}}return o}(t);de(e),Object.freeze(e),zt.timeout&&(zt.timeout.stop(),delete zt.timeout),clearTimeout(zt.restoreFocusTimeout);var n=function(t){var e={popup:C(),container:y(),content:A(),actions:L(),confirmButton:S(),cancelButton:T(),closeButton:j(),validationMessage:E(),progressSteps:B()};return yt.domCache.set(t,e),e}(this);return Lt(this,e),yt.innerParams.set(this,e),function(n,o,i){return new Promise(function(t){var e=function t(e){n.closePopup({dismiss:e})};ee.swalPromiseResolve.set(n,t);We(zt,i,e);o.confirmButton.onclick=function(){return he(n,i)};o.cancelButton.onclick=function(){return ge(n,e)};o.closeButton.onclick=function(){return e(_.close)};we(o,i,e);be(n,zt,i,e);if(i.toast&&(i.input||i.footer||i.showCloseButton)){st(document.body,z["toast-column"])}else{ut(document.body,z["toast-column"])}me(n,i);fe(i);Ke(o,i);o.container.scrollTop=0})}(this,n,e)},update:function(e){var t=C(),n=yt.innerParams.get(this);if(!t||q(t,n.hideClass.popup))return D("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");var o={};Object.keys(e).forEach(function(t){Je.isUpdatableParameter(t)?o[t]=e[t]:D('Invalid parameter to update: "'.concat(t,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js'))});var i=c({},n,o);Lt(this,i),yt.innerParams.set(this,i),Object.defineProperties(this,{params:{value:c({},this.params,e),writable:!1,enumerable:!0}})}});function $e(){if("undefined"!=typeof window){"undefined"==typeof Promise&&h("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)"),Ze=this;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=Object.freeze(this.constructor.argsToParams(e));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0,configurable:!0}});var i=this._main(this.params);yt.promise.set(this,i)}}$e.prototype.then=function(t){return yt.promise.get(this).then(t)},$e.prototype.finally=function(t){return yt.promise.get(this).finally(t)},c($e.prototype,Qe),c($e,Qt),Object.keys(Qe).forEach(function(e){$e[e]=function(){var t;if(Ze)return(t=Ze)[e].apply(t,arguments)}}),$e.DismissReason=_,$e.version="9.5.3";var Je=$e;return Je.default=Je}),void 0!==this&&this.Sweetalert2&&(this.swal=this.sweetAlert=this.Swal=this.SweetAlert=this.Sweetalert2);