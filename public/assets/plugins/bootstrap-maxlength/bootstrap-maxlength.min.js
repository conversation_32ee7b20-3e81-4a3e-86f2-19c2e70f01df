!function(m){"use strict";m.event.special.destroyed||(m.event.special.destroyed={remove:function(t){t.handler&&t.handler()}}),m.fn.extend({maxlength:function(f,t){var e=m("body");function r(t){var e=t.charCodeAt();return e?e<128?1:e<2048?2:3:0}function i(t){return t.split("").map(r).concat(0).reduce(function(t,e){return t+e})}function p(t){var e=t.val(),e=f.twoCharLinebreak?e.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"):e.replace(/(?:\r\n|\r|\n)/g,"\n"),a=0,a=f.utf8?i(e):e.length;return"file"===t.prop("type")&&""!==t.val()&&(a-=12),a}function l(t,e){return e-p(t)}function h(t,e){e.css({display:"block"}),t.trigger("maxlength.shown")}function d(t,e,a){var n="";return f.message?n="function"==typeof f.message?f.message(t,e):f.message.replace("%charsTyped%",a).replace("%charsRemaining%",e-a).replace("%charsTotal%",e):(f.preText&&(n+=f.preText),f.showCharsTyped?n+=a:n+=e-a,f.showMaxLength&&(n+=f.separator+e),f.postText&&(n+=f.postText)),n}function c(t,e,a,n){var o,s,r,i,l,c;n&&(n.html(d(e.val(),a,a-t)),0<t?(r=e,i=f.threshold,l=a,c=!0,!f.alwaysShow&&l-p(r)>i&&(c=!1),c?h(e,n.removeClass(f.limitReachedClass).addClass(f.warningClass)):(o=e,s=n,f.alwaysShow||(s.css({display:"none"}),o.trigger("maxlength.hidden")))):h(e,n.removeClass(f.warningClass).addClass(f.limitReachedClass))),f.customMaxAttribute&&(t<0?e.addClass("overmax"):e.removeClass("overmax"))}function u(t,e){var a,n,o=(n=(a=t)[0],m.extend({},"function"==typeof n.getBoundingClientRect?n.getBoundingClientRect():{width:n.offsetWidth,height:n.offsetHeight},a.offset()));if("function"!==m.type(f.placement)){if(m.isPlainObject(f.placement))return s=f.placement,r=e,void(s&&r&&(i={},m.each(["top","bottom","left","right","position"],function(t,e){var a=f.placement[e];void 0!==a&&(i[e]=a)}),r.css(i)));var s,r,i,l=t.outerWidth(),c=e.outerWidth(),p=e.width(),h=e.height();switch(f.appendToParent&&(o.top-=t.parent().offset().top,o.left-=t.parent().offset().left),f.placement){case"bottom":e.css({top:o.top+o.height,left:o.left+o.width/2-p/2});break;case"top":e.css({top:o.top-h,left:o.left+o.width/2-p/2});break;case"left":e.css({top:o.top+o.height/2-h/2,left:o.left-p});break;case"right":e.css({top:o.top+o.height/2-h/2,left:o.left+o.width});break;case"bottom-right":e.css({top:o.top+o.height,left:o.left+o.width});break;case"top-right":e.css({top:o.top-h,left:o.left+l});break;case"top-left":e.css({top:o.top-h,left:o.left-c});break;case"bottom-left":e.css({top:o.top+t.outerHeight(),left:o.left-c});break;case"centered-right":e.css({top:o.top+h/2,left:o.left+l-c-3});break;case"bottom-right-inside":e.css({top:o.top+o.height,left:o.left+o.width-c});break;case"top-right-inside":e.css({top:o.top-h,left:o.left+l-c});break;case"top-left-inside":e.css({top:o.top-h,left:o.left});break;case"bottom-left-inside":e.css({top:o.top+t.outerHeight(),left:o.left})}}else f.placement(t,e,o)}function g(t){var e,a=t.attr("maxlength")||f.customMaxAttribute;return f.customMaxAttribute&&!f.allowOverMax&&(e=t.attr(f.customMaxAttribute),(!a||e<a)&&(a=e)),a=a||t.attr("size")}return m.isFunction(f)&&!t&&(t=f,f={}),f=m.extend({showOnReady:!1,alwaysShow:!0,threshold:0,warningClass:"small form-text text-muted",limitReachedClass:"small form-text text-danger",separator:" / ",preText:"",postText:"",showMaxLength:!0,placement:"bottom-right-inside",message:null,showCharsTyped:!0,validate:!1,utf8:!1,appendToParent:!1,twoCharLinebreak:!0,customMaxAttribute:null,allowOverMax:!1,zIndex:1099},f),this.each(function(){var n,o,s=m(this);function t(){var t=d(s.val(),n,"0");n=g(s),o=o||m('<span class="bootstrap-maxlength"></span>').css({display:"none",position:"absolute",whiteSpace:"nowrap",zIndex:f.zIndex}).html(t),s.is("textarea")&&(s.data("maxlenghtsizex",s.outerWidth()),s.data("maxlenghtsizey",s.outerHeight()),s.mouseup(function(){s.outerWidth()===s.data("maxlenghtsizex")&&s.outerHeight()===s.data("maxlenghtsizey")||u(s,o),s.data("maxlenghtsizex",s.outerWidth()),s.data("maxlenghtsizey",s.outerHeight())})),f.appendToParent?(s.parent().append(o),s.parent().css("position","relative")):e.append(o),c(l(s,g(s)),s,n,o),u(s,o)}m(window).resize(function(){o&&u(s,o)}),f.showOnReady?s.ready(function(){t()}):s.focus(function(){t()}),s.on("maxlength.reposition",function(){u(s,o)}),s.on("destroyed",function(){o&&o.remove()}),s.on("blur",function(){o&&!f.showOnReady&&o.remove()}),s.on("input",function(){var t=g(s),e=l(s,t),a=!0;return f.validate&&e<0?(function(t,e){var a=t.val();if(f.twoCharLinebreak&&"\n"===(a=a.replace(/\r(?!\n)|\n(?!\r)/g,"\r\n"))[a.length-1]&&(e-=a.length%2),f.utf8){for(var n=a.split("").map(r),o=0,s=i(a)-e;o<s;o+=n.pop());e-=e-n.length}t.val(a.substr(0,e))}(s,t),a=!1):c(e,s,n,o),("bottom-right-inside"===f.placement||"top-right-inside"===f.placement||"function"==typeof f.placement||f.message&&"function"==typeof f.message)&&u(s,o),a})})}})}(jQuery);