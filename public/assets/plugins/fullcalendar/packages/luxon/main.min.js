/*!
FullCalendar Luxon Plugin v4.3.0
Docs & License: https://fullcalendar.io/
(c) 2019 <PERSON>
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("luxon"),require("@fullcalendar/core")):"function"==typeof define&&define.amd?define(["exports","luxon","@fullcalendar/core"],t):t((e=e||self).FullCalendarLuxon={},e.luxon,e.FullCalendar)}(this,function(e,t,n){"use strict";var o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};var r=function(){return(r=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};var a=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(n,e),n.prototype.offsetForArray=function(e){return l(e,this.timeZoneName).offset},n.prototype.timestampToArray=function(e){return[(n=t.DateTime.fromMillis(e,{zone:this.timeZoneName})).year,n.month-1,n.day,n.hour,n.minute,n.second,n.millisecond];var n},n}(n.NamedTimeZoneImpl);var i=n.createPlugin({cmdFormatter:function(e,t){var n=function e(t){var n=t.match(/^(.*?)\{(.*)\}(.*)$/);if(n){var o=e(n[2]);return{head:n[1],middle:o,tail:n[3],whole:n[1]+o.whole+n[3]}}return{head:null,middle:null,tail:null,whole:t}}(e);if(t.end){var o=l(t.start.array,t.timeZone,t.localeCodes[0]),r=l(t.end.array,t.timeZone,t.localeCodes[0]);return function e(t,n,o,r){if(t.middle){var a=n(t.head),i=e(t.middle,n,o,r),l=n(t.tail),u=o(t.head),c=e(t.middle,n,o,r),d=o(t.tail);if(a===u&&l===d)return a+(i===c?i:i+r+c)+l}var f=n(t.whole),m=o(t.whole);return f===m?f:f+r+m}(n,o.toFormat.bind(o),r.toFormat.bind(r),t.separator)}return l(t.date.array,t.timeZone,t.localeCodes[0]).toFormat(n.whole)},namedTimeZonedImpl:a});function l(e,n,o){return t.DateTime.fromObject({zone:n,locale:o,year:e[0],month:e[1]+1,day:e[2],hour:e[3],minute:e[4],second:e[5],millisecond:e[6]})}e.default=i,e.toDateTime=function(e,o){if(!(o instanceof n.Calendar))throw new Error("must supply a Calendar instance");return t.DateTime.fromJSDate(e,{zone:o.dateEnv.timeZone,locale:o.dateEnv.locale.codes[0]})},e.toDuration=function(e,o){if(!(o instanceof n.Calendar))throw new Error("must supply a Calendar instance");return t.Duration.fromObject(r({},e,{locale:o.dateEnv.locale.codes[0]}))},Object.defineProperty(e,"__esModule",{value:!0})});