
var flotDataType1 = [
	[0, 48.11708650372481],
	[1, 44.83834104995953],
	[2, 45.727409628208974],
	[3, 44.69213146554142],
	[4, 44.92113232835135],
	[5, 44.200874587557415],
	[6, 41.750527715312444],
	[7, 44.84511185791557],
	[8, 46.04672992189592],
	[9, 45.9480092098883],
	[10, 46.9249480823427],
	[11, 43.600609487921346],
	[12, 40.29988975207692],
	[13, 42.03310106988357],
	[14, 39.457750445961125],
	[15, 40.540159797957294],
	[16, 37.277912393740806],
	[17, 41.43887402339309],
	[18, 39.47430428214318],
	[19, 36.91189415889479],
	[20, 36.42847097453014],
	[21, 36.96844325047937],
	[22, 35.54647151074562],
	[23, 32.998974290143025],
	[24, 30.43526314490385],
	[25, 31.14797888879888],
	[26, 27.20589032036549],
	[27, 25.777592542626508],
	[28, 30.052675048145275],
	[29, 30.92837408600937],
	[30, 34.190241658736014],
	[31, 37.57718922878679],
	[32, 41.18083316913268],
	[33, 41.27110666976231],
	[34, 36.33819281943194],
	[35, 37.39239238651191],
	[36, 37.046485292242615],
	[37, 34.594801853250495],
	[38, 31.488044618299227],
	[39, 34.69970813498227],
	[40, 39.66083111892072],
	[41, 40.203292838001616],
	[42, 36.089709320758985],
	[43, 40.31141091738469],
	[44, 44.170004784953846],
	[45, 48.84998014705778],
	[46, 43.93624560052546],
	[47, 40.62473022491363],
	[48, 39.154068738786684],
	[49, 42.803089612673666],
	[50, 40.6511024461858],
	[51, 38.34516630158569],
	[52, 39.546885205159555],
	[53, 42.50715860274628],
	[54, 38.1455129028495],
	[55, 33.87761157196474],
	[56, 37.30125615378047],
	[57, 38.799409423316405],
	[58, 39.185431079286275],
	[59, 43.32737024276462],
	[60, 41.52185070435002],
	[61, 41.613587244137946],
	[62, 44.23763577861365],
	[63, 44.91439321362589],
	[64, 42.18546432611939],
	[65, 41.0624926886062],
	[66, 44.24453261527582],
	[67, 47.34794952778721],
	[68, 48.10833243543891],
	[69, 43.640893412371504],
	[70, 40.614056030997666],
	[71, 42.9374730102888],
	[72, 46.1355421298619],
	[73, 48.995759760197956],
	[74, 52.19926195857424],
	[75, 49.2778849176981],
	[76, 52.46274689069702],
	[77, 56.74969793098863],
	[78, 60.92623317241021],
	[79, 57.70969775380601],
	[80, 57.35168105637668],
	[81, 59.39818648636745],
	[82, 58.87944453401413],
	[83, 63.104976246068674],
	[84, 60.16160410107729],
	[85, 60.3461385910513],
	[86, 63.41836851069141],
	[87, 58.881150853965565],
	[88, 54.25129328569841],
	[89, 49.66170902762076],
	[90, 45.671308451937406],
	[91, 43.42038067966773],
	[92, 46.505793156464286],
	[93, 46.06001872195206],
	[94, 50.91335602988896],
	[95, 46.84735026131701],
	[96, 47.41734754711108],
	[97, 44.36126529495156],
	[98, 41.99470503666513],
	[99, 43.632976322955784],
	[100, 46.36805334166653],
	[101, 48.16660610657209],
	[102, 50.56661518795267],
	[103, 47.20511080729683],
	[104, 51.57928093061832],
	[105, 46.82629992437289],
	[106, 43.71656947498538],
	[107, 46.11727847268647],
	[108, 46.239411607006936],
	[109, 41.99170406788848],
	[110, 44.59078988734815],
	[111, 39.99864995462555],
	[112, 39.59607991752385],
	[113, 40.86135028690851],
	[114, 39.81036719656035],
	[115, 40.328012974674394],
	[116, 41.65325716849331],
	[117, 45.00093543523572],
	[118, 46.04624698953661],
	[119, 48.003663497054745],
	[120, 50.17606274884235],
	[121, 55.05679484483894],
	[122, 55.96838640846091],
	[123, 55.544955954661],
	[124, 54.84832728252716],
	[125, 52.55313725959578],
	[126, 49.91965607013097],
	[127, 54.037850934955415],
	[128, 57.10789770988697],
	[129, 58.48651605604872],
	[130, 60.7485271818432],
	[131, 65.34376786732726],
	[132, 67.43791704755618],
	[133, 62.787033615491154],
	[134, 65.01110323823873],
	[135, 66.76229363100968],
	[136, 68.37430484004857],
	[137, 71.70168521356638],
	[138, 68.57137402747702],
	[139, 67.39836039140941],
	[140, 70.31406498879772],
	[141, 70.32681376237582],
	[142, 69.44430239433778],
	[143, 68.41358873180461],
	[144, 72.61057980411566],
	[145, 70.04463291270768],
	[146, 70.28596044322113],
	[147, 65.6023891614268],
	[148, 67.46401070074405],
	[149, 62.80776411813089]
];

var flotDataType2 = [
	[0, 40.42460652446133],
	[1, 39.746131861430484],
	[2, 35.95109348595284],
	[3, 33.295567798337025],
	[4, 28.87960054374564],
	[5, 28.498853797438535],
	[6, 24.44598918395687],
	[7, 20.218403695742982],
	[8, 17.498233218421312],
	[9, 16.54060961040485],
	[10, 19.002383747980975],
	[11, 16.471725580977914],
	[12, 13.155182881964787],
	[13, 18.077483369454345],
	[14, 17.938434631237822],
	[15, 18.92413124205944],
	[16, 18.461208995002494],
	[17, 19.661876313219913],
	[18, 18.042303047352455],
	[19, 17.785290125636354],
	[20, 20.151980264909543],
	[21, 18.924923650083358],
	[22, 17.088923942341232],
	[23, 17.11745721938192],
	[24, 15.703502004647063],
	[25, 15.078540825575075],
	[26, 14.510809401000387],
	[27, 15.226574724712297],
	[28, 18.01709489679379],
	[29, 19.770761552221565],
	[30, 23.670209769802682],
	[31, 27.985742905483164],
	[32, 30.80634374024116],
	[33, 28.56215635604935],
	[34, 29.459971127621614],
	[35, 29.506514532069936],
	[36, 27.289754685028775],
	[37, 24.365568424856836],
	[38, 22.893664052525622],
	[39, 26.57527073377395],
	[40, 28.04483981176638],
	[41, 27.77031588135324],
	[42, 30.245343380918406],
	[43, 26.57479109054868],
	[44, 22.18111812493286],
	[45, 19.644777576179102],
	[46, 16.745896664550347],
	[47, 17.213789404459703],
	[48, 20.056299583848645],
	[49, 16.133489834808596],
	[50, 12.954908672170685],
	[51, 10.710124578123633],
	[52, 7.99331653229623],
	[53, 11.330824794029468],
	[54, 15.366888531658518],
	[55, 20.162146683566043],
	[56, 22.56433862111984],
	[57, 19.342499731952728],
	[58, 18.325580989588303],
	[59, 20.7511874504748],
	[60, 17.099488390174667],
	[61, 19.327912207799372],
	[62, 18.31650048764758],
	[63, 14.34889182281918],
	[64, 9.939606691311928],
	[65, 10.640765261408266],
	[66, 6.184018402150329],
	[67, 10.32603369640253],
	[68, 12.800228260925913],
	[69, 13.441825186707572],
	[70, 18.356807970216398],
	[71, 22.877870826719246],
	[72, 22.265182194135164],
	[73, 26.922230352208814],
	[74, 22.50189449417149],
	[75, 18.14060836488997],
	[76, 19.06846754782137],
	[77, 19.73961245162804],
	[78, 18.82061647678131],
	[79, 23.33852310774632],
	[80, 20.4810751737507],
	[81, 25.47004674625981],
	[82, 28.842343230667943],
	[83, 29.09658130355575],
	[84, 27.714558649179516],
	[85, 25.220943394214757],
	[86, 25.43025835749838],
	[87, 24.13072502126257],
	[88, 20.020443915879174],
	[89, 18.387986699568284],
	[90, 18.307930265812836],
	[91, 18.72058117598284],
	[92, 22.46850401457292],
	[93, 21.718447234477544],
	[94, 26.488413058421976],
	[95, 29.882771503348536],
	[96, 26.94717052753741],
	[97, 28.06481155716483],
	[98, 30.40253552214977],
	[99, 28.987765656899995],
	[100, 30.13551373541587],
	[101, 27.605418583328863],
	[102, 30.214101672191696],
	[103, 26.88133118194294],
	[104, 25.727723710013045],
	[105, 28.279900485071032],
	[106, 27.89821646957165],
	[107, 30.69854959893513],
	[108, 31.4282872565538],
	[109, 36.14975119379828],
	[110, 32.0227980362552],
	[111, 27.309945041337073],
	[112, 29.51230564564233],
	[113, 32.67035607222466],
	[114, 28.82372957289023],
	[115, 28.85242847072152],
	[116, 29.63844624105993],
	[117, 29.157219655397313],
	[118, 27.90616896335908],
	[119, 30.71160984027734],
	[120, 28.026131698214115],
	[121, 23.82439628518755],
	[122, 18.83160453591808],
	[123, 14.487027404093734],
	[124, 11.761696821209515],
	[125, 12.758521331246762],
	[126, 11.367219794014758],
	[127, 14.21423733022224],
	[128, 11.602480291802959],
	[129, 15.244397384751025],
	[130, 13.050114582189945],
	[131, 17.253378403411432],
	[132, 18.506683542934038],
	[133, 23.04087000728893],
	[134, 21.87625260158983],
	[135, 25.974296957094985],
	[136, 22.463388750666468],
	[137, 17.675052230498956],
	[138, 14.806456821972226],
	[139, 18.589538541056534],
	[140, 20.005874168046084],
	[141, 22.934846222699328],
	[142, 25.155316598067426],
	[143, 27.883126867602705],
	[144, 27.76231130416712],
	[145, 28.618896779193612],
	[146, 26.413595554645298],
	[147, 28.097785659338193],
	[148, 29.502272077881898],
	[149, 26.1165859635503]
];

function getRandomData(totalPoints = 150, start = 50) {
	var data = [];
	
	// Zip the generated y values with the x values
	var res = [];
	for (var i = 0; i < data.length; ++i) {
		res.push([i, data[i]])
	}
	// Do a random walk
	while (data.length < totalPoints) {
		var prev = data.length > 0 ? data[data.length - 1] : start;
		var y = prev + Math.random() * 10 - 5;
		if (y < 0) {
			y = Math.random() * 10;
		} else if (y > 100) {
			y = 80;
		}
		data.push(y);
	}
	return res;
}