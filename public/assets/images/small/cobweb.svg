<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" width="32" height="32" viewBox="0 0 32 32.000001" id="svg5248" version="1.1" inkscape:version="0.91 r13725" sodipodi:docname="14.svg">
  <defs id="defs5250"/>
  <sodipodi:namedview id="base" pagecolor="#ffffff" bordercolor="#666666" borderopacity="1.0" inkscape:pageopacity="0.0" inkscape:pageshadow="2" inkscape:zoom="11.2" inkscape:cx="10.804283" inkscape:cy="11.805966" inkscape:document-units="px" inkscape:current-layer="layer1" showgrid="true" units="px" inkscape:snap-bbox="true" inkscape:bbox-paths="true" inkscape:bbox-nodes="true" inkscape:snap-bbox-edge-midpoints="true" inkscape:snap-bbox-midpoints="true" inkscape:window-width="1366" inkscape:window-height="705" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1">
    <inkscape:grid type="xygrid" id="grid5256"/>
  </sodipodi:namedview>
  
  <g inkscape:label="Layer 1" inkscape:groupmode="layer" id="layer1" transform="translate(0,-1020.3622)">
    <g id="g6283">
      <path inkscape:connector-curvature="0" id="path7570" d="M 15.992188,1020.3613 A 0.50004997,0.50004997 0 0 0 15.5,1020.8691 l 0,9.6133 0,11.7578 0,9.6153 a 0.50004997,0.50004997 0 1 0 1,0 l 0,-9.6153 0,-11.7578 0,-9.6133 a 0.50004997,0.50004997 0 0 0 -0.507812,-0.5078 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.99999994px;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
      <path inkscape:connector-curvature="0" id="path7572" d="m 29.394531,1028.1133 a 0.50004997,0.50004997 0 0 0 -0.222656,0.07 l -8.328125,4.8066 -10.1875,5.8789 -8.328125,4.8067 a 0.50004997,0.50004997 0 1 0 0.5,0.8652 l 8.328125,-4.8066 10.1875,-5.8789 8.328125,-4.8067 a 0.50004997,0.50004997 0 0 0 -0.277344,-0.9355 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.99999994px;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
      <path inkscape:connector-curvature="0" id="path7574" d="m 2.5410156,1028.1133 a 0.50004997,0.50004997 0 0 0 -0.2128906,0.9355 l 8.328125,4.8067 10.1875,5.8789 8.328125,4.8066 a 0.50004997,0.50004997 0 1 0 0.5,-0.8652 l -8.328125,-4.8067 -10.1875,-5.8789 -8.328125,-4.8066 a 0.50004997,0.50004997 0 0 0 -0.2871094,-0.07 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.99999994px;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
      <path inkscape:connector-curvature="0" id="path7578" d="m 16.250207,1028.4318 a 0.50033222,0.49976787 0 0 0 -0.500359,0 l -6.4988667,3.748 a 0.50033222,0.49976787 0 0 0 -0.2501746,0.4328 l -7.89e-5,7.4959 a 0.50033222,0.49976787 0 0 0 0.2490653,0.4334 l 6.5006799,3.749 a 0.50033222,0.49976787 0 0 0 0.500359,-0 l 6.498867,-3.748 a 0.50033222,0.49976787 0 0 0 0.250175,-0.4328 l 7.8e-5,-7.4958 a 0.50033222,0.49976787 0 0 0 -0.249065,-0.4335 l -6.50068,-3.749 z m -0.250598,1.0099 6.001066,3.4608 3.44e-4,6.9188 -6.00184,3.46 -5.999173,-3.4597 7.75e-4,-6.9208 5.998828,-3.4591 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.8942802;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:3.9000001;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
      <path inkscape:connector-curvature="0" id="path7580" d="m 16.248833,1032.1372 a 0.49830783,0.50179817 0 0 0 -0.497371,-3e-4 l -3.502882,2.0361 a 0.49830783,0.50179817 0 0 0 -0.24901,0.4343 l 4.04e-4,4.0729 a 0.49830783,0.50179817 0 0 0 0.248964,0.4336 l 3.502267,2.0362 a 0.49830783,0.50179817 0 0 0 0.497371,3e-4 l 3.502882,-2.0361 a 0.49830783,0.50179817 0 0 0 0.24901,-0.4343 l -4.04e-4,-4.0729 a 0.49830783,0.50179817 0 0 0 -0.248964,-0.4336 l -3.502267,-2.0362 z m -0.249212,1.0132 3.003691,1.7464 1.9e-4,3.4923 -3.004104,1.7471 -3.003692,-1.7464 4.14e-4,-3.4934 3.003501,-1.746 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1.65318871;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:3.9000001;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
      <path inkscape:connector-curvature="0" id="path7582" d="m 16.420673,1024.6169 a 0.50111245,0.49898979 0 0 0 -0.499539,5e-4 l -9.6710899,5.5568 a 0.50111245,0.49898979 0 0 0 -0.2520047,0.4347 l 0.0027,11.1183 a 0.50111245,0.49898979 0 0 0 0.2492815,0.4312 l 9.6709733,5.5599 a 0.50111245,0.49898979 0 0 0 0.499621,-6e-4 l 9.668276,-5.5585 a 0.50111245,0.49898979 0 0 0 0.252005,-0.4346 l 1.16e-4,-11.1168 a 0.50111245,0.49898979 0 0 0 -0.249364,-0.431 l -9.670973,-5.5599 z m -0.250025,1.0075 9.169828,5.2718 -3.49e-4,10.5409 -9.171843,5.272 -9.1670125,-5.2702 0.00201,-10.5438 9.1673625,-5.2707 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.60205889;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:3.9000001;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
      <path inkscape:connector-curvature="0" id="path7584" d="m 16.205966,1021.5562 a 0.50147559,0.49862854 0 0 0 -0.455072,0.023 l -12.5023529,7.1757 a 0.50147559,0.49862854 0 0 0 -0.2499945,0.4305 l 0.00134,14.3538 a 0.50147559,0.49862854 0 0 0 0.2508983,0.4286 l 12.5000491,7.1759 a 0.50147559,0.49862854 0 0 0 0.498742,0 l 12.498714,-7.1778 a 0.50147559,0.49862854 0 0 0 0.249995,-0.4306 l 0.0023,-14.3516 a 0.50147559,0.49862854 0 0 0 -0.250898,-0.4286 l -12.50005,-7.176 a 0.50147559,0.49862854 0 0 0 -0.04367,-0.025 z m -0.205334,1.0304 11.997864,6.8877 -0.0011,13.7731 -12.001266,6.8893 -11.9942257,-6.8856 0.0034,-13.7769 11.9953685,-6.8876 z" style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;baseline-shift:baseline;text-anchor:start;white-space:normal;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#a4abc5;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:0.46613532;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:3.9000001;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"/>
    </g>
  </g>

	
	
	<metadata>
		<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#" xmlns:dc="http://purl.org/dc/elements/1.1/">
			<rdf:Description about="https://iconscout.com/legal#licenses" dc:title="Cobweb, Spider, Net, Spiderweb, Web, Trap, Tricky, Silk" dc:description="Cobweb, Spider, Net, Spiderweb, Web, Trap, Tricky, Silk" dc:publisher="Iconscout" dc:date="2016-12-14" dc:format="image/svg+xml" dc:language="en">
				<dc:creator>
					<rdf:Bag>
						<rdf:li>Jemis Mali</rdf:li>
					</rdf:Bag>
				</dc:creator>
			</rdf:Description>
		</rdf:RDF>
    </metadata></svg>
