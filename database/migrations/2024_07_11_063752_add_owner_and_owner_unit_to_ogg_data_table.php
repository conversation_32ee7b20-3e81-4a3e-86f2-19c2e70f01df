<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOwnerAndOwnerUnitToOggDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ogg_data', function (Blueprint $table) {
            $table->string('owner')->nullable()->after('aging');
            $table->string('ownerUnit')->nullable()->after('owner');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ogg_data', function (Blueprint $table) {
            $table->dropColumn('owner');
            $table->dropColumn('ownerUnit');
        });
    }
}
