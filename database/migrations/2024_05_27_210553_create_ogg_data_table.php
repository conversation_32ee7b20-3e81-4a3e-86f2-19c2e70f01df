<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOggDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ogg_data', function (Blueprint $table) {
            $table->id();
            $table->string('orderNo');
            $table->string('orderType');
            $table->string('product');
            $table->string('orderStatus');
            $table->string('activityName');
            $table->string('activityStatus');
            $table->string('serviceId');
            $table->string('account');
            $table->timestamp('activityCreated');
            $table->integer('aging');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ogg_data');
    }
}
