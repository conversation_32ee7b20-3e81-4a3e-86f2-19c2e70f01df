<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToUnisDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('unis_data', function (Blueprint $table) {
            // Adding PeDeviceId after Pe column
            $table->string('PeDeviceId')->nullable()->after('Pe');
            
            // Adding CeIp after PeInterface column
            $table->string('CeIp')->nullable()->after('PeInterface');
            
            // Adding Cidr after CeIp column
            $table->string('Cidr')->nullable()->after('CeIp');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('unis_data', function (Blueprint $table) {
            $table->dropColumn(['PeDeviceId', 'CeIp', 'Cidr']);
        });
    }
}
