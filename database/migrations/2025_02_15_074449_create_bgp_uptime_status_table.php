<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBgpUptimeStatusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bgp_uptime_status', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->string('CustAbbr'); // Example column for CustAbbr
            $table->string('ServiceType'); // Example column for ServiceId
            $table->string('Product'); // Example column for product name
            $table->string('LegId'); // Example column for Leg Id
            $table->string('ServiceId'); // Example column for ServiceId
            $table->string('PeNode'); // Example column for PE node
            $table->string('PeDeviceIp'); // Example column for PeDeviceIp
            $table->string('CeWanIp'); // Example column for CeWanIp
            $table->string('BGPStatus'); // Example column for BGPStatus
            $table->string('BGPUptime'); // Example column for BGPStatus
            $table->timestamps(); // Adds created_at and updated_at columns
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bgp_uptime_status');
    }
}
