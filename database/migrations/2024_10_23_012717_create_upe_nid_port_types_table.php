<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUpeNidPortTypesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('upe_nid_port_types', function (Blueprint $table) {
            $table->id();
            $table->string('ne_type'); // NID/UPE
            $table->string('vendor');    // vendor
            $table->string('model');  // model
            $table->string('port_range_start')->nullable();   // Port Start
            $table->string('port_range_end')->nullable();   // Port End
            $table->string('port_communication')->nullable(); // Port Communication
            $table->string('port_type')->nullable(); // Port Type
            $table->string('description')->nullable(); // Description
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('upe_nid_port_types');
    }
}
