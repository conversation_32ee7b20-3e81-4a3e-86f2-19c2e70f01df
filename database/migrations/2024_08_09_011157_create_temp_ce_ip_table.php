<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTempCeIpTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('temp_ce_ip', function (Blueprint $table) {
            $table->id(); // id column, auto-incrementing primary key
            $table->string('serviceId'); // serviceId column
            $table->string('ceWanIp'); // ceWanIp column
            $table->timestamps(); // adds created_at and updated_at columns
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('temp_ce_ip');
    }
}
