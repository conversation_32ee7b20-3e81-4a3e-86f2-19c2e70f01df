<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUnisDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('unis_data', function (Blueprint $table) {
            $table->id(); // Primary key, non-nullable

            // Existing columns, all made nullable
            $table->string('CustAbbr');
            $table->string('ServiceId');
            $table->string('Product')->nullable();
            $table->string('Bandwidth')->nullable();
            $table->string('RedundancyId')->nullable();
            $table->string('ParentId')->nullable();
            $table->string('PathStatus')->nullable();
            $table->string('ServiceType')->nullable();
            $table->string('Npe')->nullable();
            $table->string('NpeInterface')->nullable();
            $table->string('Pe')->nullable();
            $table->string('PeIp')->nullable();
            $table->string('PeInterface')->nullable();

            // Additional columns from JSON, all made nullable
            $table->string('L2CustAbbr')->nullable();
            $table->string('ParentServiceId')->nullable();
            $table->string('L2ServiceId')->nullable();
            $table->string('L2Product')->nullable();
            $table->string('L2Bandwidth')->nullable();
            $table->string('L2RedundancyId')->nullable();
            $table->string('L2ParentSvc')->nullable();
            $table->string('L2PathStatus')->nullable();
            $table->string('L2ServiceType')->nullable();
            $table->string('UpeNidDlink')->nullable();
            $table->string('UpeNidDlinkInterface')->nullable();
            $table->string('UpeNidUlinkPri')->nullable();
            $table->string('UpeNidUlinkPriInterface')->nullable();
            $table->string('UpeNidUlinkSec')->nullable();
            $table->string('UpeNidUlinkSecInterface')->nullable();
            $table->string('EpePri')->nullable();
            $table->string('EpePriInterface')->nullable();
            $table->string('EpeSec')->nullable();
            $table->string('EpeSecInterface')->nullable();

            $table->timestamps(); // Created at and updated at timestamps
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('unis_data');
    }
}
