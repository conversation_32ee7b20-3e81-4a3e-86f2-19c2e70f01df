<?php

namespace Database\Seeders;

use App\Models\PremiumCustomer;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class PremiumCustomersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        // Insert premium customers
        $customers = [
            ['name' => 'Maybank', 'abbreviation' => 'MBBBR', 'path' => '870', 'account' => 'MALAYAN BANKING BERHAD', 'logo' => 'Maybank.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'CIMB', 'abbreviation' => 'CIMBD', 'path' => '1', 'account' => 'CIMB BANK BERHAD', 'logo' => 'CIMB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'CIMB', 'abbreviation' => 'BCBIP', 'path' => '446', 'account' => 'CIMB BANK BERHAD', 'logo' => 'CIMB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'CIMB', 'abbreviation' => 'CIMSD', 'path' => '289', 'account' => 'CIMB SDIPVPN', 'logo' => 'CIMB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBAG', 'path' => '4', 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBMT', 'path' => '16', 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBMM', 'path' => '411', 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBNK', 'path' => '48', 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBEP', 'path' => null, 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBIG', 'path' => null, 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'RHB', 'abbreviation' => 'RHBOA', 'path' => '403', 'account' => 'RHB BANK BERHAD', 'logo' => 'RHB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'BSN', 'abbreviation' => 'BSNVT', 'path' => '870', 'account' => 'BANK SIMPANAN NASIONAL', 'logo' => 'BSN.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'KWSP', 'abbreviation' => 'VKWSP', 'path' => '322', 'account' => 'KUMPULAN WANG SIMPANAN PEKERJA', 'logo' => 'KWSP.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'POS', 'abbreviation' => 'POSHQ', 'path' => '3', 'account' => 'POS MALAYSIA BERHAD', 'logo' => 'POS.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'POS', 'abbreviation' => 'VPOSM', 'path' => '133', 'account' => 'POS MALAYSIA BERHAD', 'logo' => 'POS.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'POS', 'abbreviation' => 'POSML', 'path' => '2', 'account' => 'POS MALAYSIA BERHAD', 'logo' => 'POS.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'KPJ', 'abbreviation' => 'KPJHQ', 'path' => '2', 'account' => 'KPJ HEALTHCARE BERHAD', 'logo' => 'KPJ.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'UMW IT', 'abbreviation' => 'UMWIT', 'path' => '21', 'account' => 'UMW IT SERVICES SDN BHD', 'logo' => 'UMW IT.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Petronas', 'abbreviation' => 'PETV1', 'path' => '96', 'account' => 'PETROLIAM NASIONAL BERHAD', 'logo' => 'Petronas.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Petronas', 'abbreviation' => 'PETRO', 'path' => null, 'account' => 'PETROLIAM NASIONAL BERHAD', 'logo' => 'Petronas.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'UITM', 'abbreviation' => 'VUITM', 'path' => '99', 'account' => 'UNIVERSITI TEKNOLOGI MARA', 'logo' => 'UITM.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Bank Muamalat', 'abbreviation' => 'MUAMA', 'path' => '472', 'account' => 'BANK MUAMALAT MALAYSIA BERHAD', 'logo' => 'Bank Muamalat.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Bank Muamalat', 'abbreviation' => 'SDBHQ', 'path' => '6', 'account' => 'BANK MUAMALAT MALAYSIA BERHAD (SDIPVPN DOME-HQ)', 'logo' => 'Bank Muamalat.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Affin Bank', 'abbreviation' => 'AFFBR', 'path' => '508', 'account' => 'AFFIN BANK BERHAD', 'logo' => 'Affin Bank.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'AGRO Bank', 'abbreviation' => 'AGROM', 'path' => '650', 'account' => 'AGRO BANK (BANK PERTANIAN MALAYSIA)', 'logo' => 'AGRO Bank.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'UMW Toyota', 'abbreviation' => 'UMWTM', 'path' => '32', 'account' => 'UMW TOYOTA MOTOR SDN BHD', 'logo' => 'UMW Toyota.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'UMW Toyota', 'abbreviation' => 'UMWT3', 'path' => '10', 'account' => 'UMW TOYOTA MOTOR SDN BHD', 'logo' => 'UMW Toyota.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'UMW Toyota', 'abbreviation' => 'UMWTD', 'path' => null, 'account' => 'UMW TOYOTA MOTOR SDN BHD', 'logo' => 'UMW Toyota.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'UMW Toyota', 'abbreviation' => 'UMWTC', 'path' => null, 'account' => 'UMW TOYOTA MOTOR SDN BHD', 'logo' => 'UMW Toyota.png' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'PERODUA', 'abbreviation' => 'PEROD', 'path' => '235', 'account' => 'PERUSAHAAN OTOMOBIL KEDUA SDN BHD', 'logo' => 'PERODUA.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'PERODUA', 'abbreviation' => null, 'path' => null, 'account' => 'PERODUA SALES SDN BHD', 'logo' => 'PERODUA.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Kementah', 'abbreviation' => 'VMIND', 'path' => '325', 'account' => 'KEMENTERIAN PERTAHANAN MALAYSIA - KEMENTAH', 'logo' => 'Kementah.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'Perkeso', 'abbreviation' => 'SOCSO', 'path' => '78', 'account' => 'PERTUBUHAN KESELAMATAN SOSIAL', 'logo' => 'Perkeso.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'MAB', 'abbreviation' => 'MASCC', 'path' => '12', 'account' => 'MALAYSIA AIRLINES BERHAD', 'logo' => 'MAB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'MAB', 'abbreviation' => 'MAB11', 'path' => '16', 'account' => 'MALAYSIA AIRLINES BERHAD', 'logo' => 'MAB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'MAB', 'abbreviation' => 'MABSF', 'path' => '4', 'account' => 'MALAYSIA AIRLINES BERHAD/MAB SDWAN DOME SFB', 'logo' => 'MAB.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'AEON', 'abbreviation' => 'AEONP', 'path' => '18', 'account' => 'AEON CO.(M) BHD', 'logo' => 'AEON.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')],
            ['name' => 'PadiBeras Nasional', 'abbreviation' => 'BERNS', 'path' => '161', 'account' => 'PADIBERAS NASIONAL BERHAD', 'logo' => 'PadiBeras Nasional.jpeg' , 'user_id' => 1, 'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')]
         ];

       PremiumCustomer::insert($customers);
    }
}
