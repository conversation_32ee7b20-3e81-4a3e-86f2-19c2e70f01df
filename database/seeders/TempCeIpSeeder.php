<?php

namespace Database\Seeders;

use App\Models\TempCeIp;
use Illuminate\Database\Seeder;

class TempCeIpSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
            ['serviceId' => 'PS1062746591', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1062746594', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1048496212', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1048496215', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1025460517', 'ceWanIp' => '*********'],
            ['serviceId' => 'BS1025460520', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1095277934', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1095277931', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1095245327', 'ceWanIp' => '*********'],
            ['serviceId' => 'PS1095245324', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1010781724', 'ceWanIp' => '**********'],
            ['serviceId' => 'BS1084498157', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1084498154', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1084489678', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1084489681', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1087771576', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1087772446', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1087770129', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1089208338', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1089208341', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1035976485', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1062685369', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1062685366', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1087649408', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1087649807', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1062683764', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1062683761', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1066281140', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1087784643', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1087784646', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1006612635', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1077856970', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1077856967', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1047341631', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1047341628', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1045860086', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1045860089', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1085992844', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1038456030', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1038597173', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1038472441', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1083142920', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1039898322', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1070675459', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1038482249', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1038561941', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1038561107', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1038466919', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1038453782', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1046824514', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1038596625', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1038474545', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1038475610', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1038456486', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1038454460', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1038453804', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1038456111', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1038560328', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1038473965', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1018639629', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1025678102', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1025678099', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1080263031', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1077880504', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1077880501', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1030394753', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1080556579', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1080233346', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1092173919', 'ceWanIp' => '***************'],
            ['serviceId' => 'BS1092173922', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1092239669', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1092166585', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1092239666', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1092798089', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1092809465', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1093051801', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1093055458', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1092773003', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1054253638', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1028151742', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1028151745', 'ceWanIp' => '***************'],
            ['serviceId' => 'BS1028149662', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1028149659', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1028147369', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1028147372', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1028960244', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1028960240', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1025882426', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1028067097', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1028067094', 'ceWanIp' => '*********'],
            ['serviceId' => 'BS1028068332', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1028068329', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1028097150', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1028097147', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1028151369', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1028151366', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1027831064', 'ceWanIp' => '*********'],
            ['serviceId' => 'BS1027831071', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1087667504', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1087667507', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1029085735', 'ceWanIp' => '218.208.119.166'],
            ['serviceId' => 'BS1029085732', 'ceWanIp' => '218.208.46.174'],
            ['serviceId' => 'PS1028152608', 'ceWanIp' => '218.208.119.90'],
            ['serviceId' => 'BS1028152611', 'ceWanIp' => '218.208.119.18'],
            ['serviceId' => 'BS1028142897', 'ceWanIp' => '218.208.119.22'],
            ['serviceId' => 'PS1028142894', 'ceWanIp' => '218.208.119.58'],
            ['serviceId' => 'PS1028103012', 'ceWanIp' => '218.208.119.130'],
            ['serviceId' => 'BS1028103015', 'ceWanIp' => '218.208.119.194'],
            ['serviceId' => 'BS1027420318', 'ceWanIp' => '218.208.13.86'],
            ['serviceId' => 'PS1027420315', 'ceWanIp' => '218.208.13.90'],
            ['serviceId' => 'BS1085903410', 'ceWanIp' => '58.27.21.74'],
            ['serviceId' => 'PS1085903407', 'ceWanIp' => '218.208.73.130'],
            ['serviceId' => 'BS1028150872', 'ceWanIp' => '218.208.119.242'],
            ['serviceId' => 'PS1028150869', 'ceWanIp' => '218.208.119.126'],
            ['serviceId' => 'PS1028977285', 'ceWanIp' => '218.208.119.30'],
            ['serviceId' => 'BS1028977290', 'ceWanIp' => '218.208.119.202'],
            ['serviceId' => 'BS1028090834', 'ceWanIp' => '1.9.72.86'],
            ['serviceId' => 'PS1028090831', 'ceWanIp' => '58.27.62.182'],
            ['serviceId' => 'BS1028149193', 'ceWanIp' => '58.26.122.154'],
            ['serviceId' => 'PS1028149190', 'ceWanIp' => '210.187.20.122'],
            ['serviceId' => 'PS1028596423', 'ceWanIp' => '218.208.119.222'],
            ['serviceId' => 'BS1028596426', 'ceWanIp' => '218.208.119.158'],
            ['serviceId' => 'BS1028140211', 'ceWanIp' => '218.208.119.98'],
            ['serviceId' => 'PS1026074777', 'ceWanIp' => '210.187.84.118'],
            ['serviceId' => 'BS1026074780', 'ceWanIp' => '210.187.84.70'],
            ['serviceId' => 'PS1028140208', 'ceWanIp' => '58.27.116.202'],
            ['serviceId' => 'BS1028099555', 'ceWanIp' => '58.26.190.170'],
            ['serviceId' => 'PS1028099552', 'ceWanIp' => '1.9.190.58'],
            ['serviceId' => 'BS1028081362', 'ceWanIp' => '210.187.64.50'],
            ['serviceId' => 'PS1028081359', 'ceWanIp' => '58.26.158.130'],
            ['serviceId' => 'BS1028148285', 'ceWanIp' => '218.208.119.38'],
            ['serviceId' => 'BS1028098297', 'ceWanIp' => '58.27.62.66'],
            ['serviceId' => 'PS1028148282', 'ceWanIp' => '218.208.119.170'],
            ['serviceId' => 'PS1028098294', 'ceWanIp' => '2************'],
            ['serviceId' => 'BS1028148852', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1028148849', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1032061544', 'ceWanIp' => '***********'],
            ['serviceId' => 'PS1096307081', 'ceWanIp' => '***************'],
            ['serviceId' => 'BS1096307084', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1036366948', 'ceWanIp' => '*************'],
            ['serviceId' => 'BS1036366951', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1036415753', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1036415756', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1036417089', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1036417092', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1088948049', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1088948052', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1088948788', 'ceWanIp' => '************'],
            ['serviceId' => 'BS1088948791', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1088950512', 'ceWanIp' => '***********'],
            ['serviceId' => 'BS1088950515', 'ceWanIp' => '*************'],
            ['serviceId' => 'PS1091351593', 'ceWanIp' => '**************'],
            ['serviceId' => 'BS1091351596', 'ceWanIp' => '************'],
            ['serviceId' => 'PS1098264682', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1098416463', 'ceWanIp' => '**********'],
            ['serviceId' => 'PS1085669069', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1085668964', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1085668798', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1085667218', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1085666603', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1085666129', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1085660397', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1085659245', 'ceWanIp' => '**************'],
            ['serviceId' => 'PS1085657484', 'ceWanIp' => '***************'],
            ['serviceId' => 'PS1085626684', 'ceWanIp' => '**************'],
        ];

        foreach ($data as $item) {
            TempCeIp::updateOrCreate(
                ['serviceId' => $item['serviceId']],
                ['ceWanIp' => $item['ceWanIp']]
            );
        }
    }
}
