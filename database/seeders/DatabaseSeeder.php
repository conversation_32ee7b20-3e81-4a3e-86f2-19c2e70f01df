<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // Seed Users data
        // $dataUsers = [
        //     [
        //         'staff_id' => 'S54197',
        //         'full_name' => '<PERSON><PERSON><PERSON>', 
        //         'email' => '<EMAIL>', 
        //         'role' => 'admin', 
        //         'status' => 'active',
        //         'email_verified_at' => now(),
        //         'remember_token' => Str::random(10), 
        //         'created_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s'), 'updated_at' => Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s')
        //     ]
        // ];

        // User::insert($dataUsers);

        // Seed Premium Customers data
        // $this->call(PremiumCustomersSeeder::class);

        // Seed Temp CE data
        $this->call(TempCeIpSeeder::class);
    }
}
